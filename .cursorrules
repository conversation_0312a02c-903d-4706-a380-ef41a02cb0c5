# Running dev frontend
cd frontend
npm start

# Running dev backend
cd backend
npm start

# Old version v1
- Everything in ./v1 folder is the old version and should be used only for a basis for example functionality, the code is a different tech stack so cannot be copied, only the overall functionality should be viewed and then translated into the new tech stack if needed.

# Loveable Design folder
- Everything in ./loveable-designs folder are just design templates for some of our pages, the codebase is not what we are using, sometimes we will go into the folder just to see the design layouts from loveable that we might want to copy to our code in ./frontend

# Structure
-  We have the frontend and backend folders that house the respective code, when we are making changes to one we need to make sure we make any relevant changes to the other.

# NiceDeals TypeScript Rules and Best Practices

## Type Definitions
- Always define comprehensive interfaces in types/index.ts
- Include optional properties with ? operator: `property?: type`
- For properties that may be undefined, use nullish coalescing: `property ?? defaultValue`
- When extending existing types, use interface inheritance: `interface ExtendedType extends BaseType`

## React Component Best Practices
- Explicitly type all component props: `const Component: React.FC<PropType> = ({ prop1, prop2 }) => {}`
- Use proper typing for useState: `const [state, setState] = useState<Type>(initialValue)`
- When using enums or literal union types in useState, add type annotation: `useState<TabType>(0)`
- For conditional rendering with literal types, use type assertions when needed: `tabIndex === (1 as TabType)`

## API and Data Handling
- Define strict response types for all API calls
- Use optional chaining for nested properties: `data?.user?.profile?.name`
- When dealing with objects that could be strings or objects: `typeof data.store === 'object' ? data.store.name : data.store`
- Handle array type guards: `Array.isArray(data) ? data : []`

## API Response Transformation Pattern
- **IMPORTANT**: Our application follows a standard pattern where backend API responses use snake_case (`thumbnail_url`) but frontend code uses camelCase (`thumbnailUrl`)
- All API responses are automatically transformed from snake_case to camelCase via the `humps` library in `api.ts`
- Always access properties in component code using camelCase: `comment.thumbnailUrl`, NOT `comment.thumbnail_url`
- Type definitions in interfaces should use camelCase to match the transformed data that will be used in components
- When debugging API responses, be aware that the Network tab will show snake_case, but console logs in components will show camelCase
- When creating new API endpoints or models, maintain this pattern: snake_case in backend/database, camelCase in frontend code

## Form Handling
- Always define Yup validation schemas matching form interfaces
- Use proper typing with Formik: `<Formik<FormValues>...`

## Common Type Issues to Watch For
- Comparison between different literal types (like string literals or number literals)
- Optional properties being used without null/undefined checks
- Type narrowing issues in nested components or conditions
- Inconsistent property names between frontend types and API responses

## Code Organization
- Keep related types together in logical groups
- Use descriptive type names that indicate their purpose
- Comment complex type definitions

## Testing Strategies
- Run TypeScript compiler checks before committing code: `npm run tsc`
- Test edge cases with potentially undefined values
- Verify type compatibility between API responses and component props

## VS Code Helpers
- Use "Go to Definition" to inspect type definitions
- Check hover information for type details
- Use the Problems panel to find and fix TypeScript errors

Remember: TypeScript is meant to help prevent errors, not create extra work. When encountering persistent type errors, simplify the type definitions or use more explicit type assertions.