This is nicedeals.app

We are converting a CSR react site over to an SSR site
The SSR site is all in frontend/app
The CSR site was in frontend/src
We will copy the EXACT look and feel of the old site over but with SSR logic
Until i delete this file we are in conversion mode
- do not make things up as you think they should be, we are in copy mode, copy the csr version to make our new nextjs ssr version
- if you run into token limit problems then immediately stop and tell me how we will split the task up into smaller parts

dont forget you have access to the backend code via @backend to review how the api works
dont forget you have access to the old CSR code via @frontend/src to review the exact functionality of the old site

We use react-hot-toast 

When starting the NextJS server it is highly likely that one is already running, so when it reports back with an error when you attempt to start it via the command line take that in mind. If its running on port 3000 then theres no point in starting another one on 3001

We are building on Windows 10 using Powershell for our terminal

The live server is on Ubuntu

API Endpoint Rules:
- NEVER make up or assume API endpoints that don't exist in the backend
- Always verify endpoints by checking the backend code before using them
- If a needed endpoint doesn't exist, we should:
  1. Document what's needed
  2. Add it to the backend first
  3. Then use it in the frontend
- Making up endpoints is not acceptable - it breaks functionality and looks unprofessional