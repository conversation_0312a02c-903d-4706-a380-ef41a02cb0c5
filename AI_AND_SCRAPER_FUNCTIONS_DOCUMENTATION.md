# AI and Scraper Functions Documentation for NiceDeals App

This document provides comprehensive documentation for implementing three key functions from the NiceDeals app:
1. AI Title Generator
2. AI Description Generator  
3. Scraper System (HotUKDeals)

## Overview

These functions work together to automate content creation and data collection for a deals website. The AI functions use external APIs to improve titles and generate descriptions, while the scraper automatically collects deals from HotUKDeals.

---

## 1. AI Title Generator

### Purpose
Improves existing product titles to be more concise and SEO-friendly while maintaining essential product information.

### Implementation

#### Backend Service (`services/ai.js`)

```javascript
const axios = require('axios');

/**
 * Helper to extract a clean title from AI response
 */
function extractCleanTitle(text) {
  let cleanTitle = text.trim();
  
  // Handle multiple options in response
  if (cleanTitle.includes('Option')) {
    const optionMatch = cleanTitle.match(/Option 1[^:]*:\s*(.+?)(?=Option|\n\n|$)/is);
    if (optionMatch && optionMatch[1]) {
      cleanTitle = optionMatch[1].trim();
    }
  }
  
  // Remove formatting characters
  cleanTitle = cleanTitle
    .replace(/\*\*/g, '')  // Bold markdown
    .replace(/\*/g, '')    // Italic markdown
    .replace(/^[-*•]/g, '') // Bullet points
    .trim();
  
  return cleanTitle;
}

/**
 * Improve a product title using AI
 */
async function improveTitle(title) {
  if (!title) return title;
  
  try {
    const prompt = `Can you change the wording of this title please. The most important thing is to keep the product title. Any superfluous words that describe the product or are attributes of the product can be removed.\n\nOriginal Title: "${title}"\n\nGive me ONLY the improved title with no explanations, options, or formatting. Return just the improved title text.`;
    
    const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
      model: "deepseek/deepseek-chat:free",
      messages: [{ role: 'user', content: prompt }]
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'HTTP-Referer': process.env.FRONTEND_URL || 'http://localhost:3010',
        'X-Title': 'NiceDeals'
      }
    });
    
    let improvedTitle = response.data.choices[0].message.content;
    improvedTitle = extractCleanTitle(improvedTitle);
    
    return improvedTitle || title;
  } catch (error) {
    console.error('Error improving title with OpenRouter:', error);
    return title; // Fallback to original
  }
}

module.exports = { improveTitle };
```

#### API Route (`routes/ai.js`)

```javascript
const express = require('express');
const router = express.Router();
const { authMiddleware } = require('../middlewares/auth');

router.post('/generate', authMiddleware, async (req, res) => {
  try {
    const { prompt } = req.body;
    
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'HTTP-Referer': process.env.FRONTEND_URL,
        'X-Title': 'YourAppName',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        'model': 'deepseek/deepseek-chat:free',
        'messages': [{ role: 'user', content: prompt }]
      })
    });

    const result = await response.json();
    let text = result.choices[0].message.content;
    
    // Clean up title responses
    if (prompt.includes('change the wording of this title')) {
      text = extractCleanTitle(text);
    }
    
    res.json({ success: true, text });
  } catch (error) {
    console.error('Error calling OpenRouter API:', error);
    res.status(500).json({ error: 'Failed to process with AI' });
  }
});
```

#### Frontend Service (`services/aiService.ts`)

```typescript
import api from './api';

interface OpenRouterResponse {
  text: string;
}

const aiService = {
  async improveTitle(title: string): Promise<string> {
    try {
      const prompt = `Can you change the wording of this title to be more concise while keeping the essential product title? The original title is: "${title}". Give me ONLY the improved title with no explanations or formatting or quotes or product attributes.`;
      
      const response = await api.post<OpenRouterResponse>('/ai/generate', { prompt });
      
      let improvedTitle = response.data.text.trim();
      
      // Clean up response
      improvedTitle = improvedTitle
        .replace(/\*\*Option \d+( \([^)]+\))?:\*\*/g, '')
        .replace(/\*Option \d+( \([^)]+\))?:\*/g, '')
        .replace(/Option \d+[:.]/g, '')
        .replace(/\*\*/g, '')
        .replace(/\*/g, '')
        .replace(/^[-*•]/g, '')
        .trim();
      
      // Take first line if multiple
      if (improvedTitle.includes('\n')) {
        const lines = improvedTitle.split('\n')
          .map(line => line.trim())
          .filter(line => line && !line.toLowerCase().startsWith('option'));
        
        if (lines.length > 0) {
          improvedTitle = lines[0];
        }
      }
      
      return improvedTitle;
    } catch (error) {
      console.error('Error improving title:', error);
      throw new Error('Failed to improve title with AI');
    }
  }
};

export default aiService;
```

### Environment Variables Required
```
OPENROUTER_API_KEY=your_openrouter_api_key_here
FRONTEND_URL=http://localhost:3000
```

---

## 2. AI Description Generator

### Purpose
Generates product descriptions by analyzing product URLs and extracting key features and specifications.

### Implementation

#### Backend Service (extends the ai.js service)

```javascript
/**
 * Generate a product description using AI based on a URL
 */
async function generateDescription(url) {
  if (!url) return "";
  
  try {
    const timestampedUrl = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;
    const prompt = process.env.AI_DESCRIPTION_PROMPT.replace('{{URL}}', timestampedUrl);
    
    const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
      model: "deepseek/deepseek-chat:free",
      messages: [{ role: 'user', content: prompt }]
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'HTTP-Referer': process.env.FRONTEND_URL || 'http://localhost:3010',
        'X-Title': 'NiceDeals'
      }
    });
    
    let description = response.data.choices[0].message.content;
    description = description
      .replace(/^\*\*Description:?\*\*/i, '')
      .replace(/^Description:?/i, '')
      .replace(/\*\*/g, '')
      .replace(/\*/g, '')
      .replace(/^[-*•]/g, '')
      .trim();
    
    return description;
  } catch (error) {
    console.error('Error generating description with OpenRouter:', error);
    return "";
  }
}
```

#### Frontend Service (extends aiService.ts)

```typescript
const aiService = {
  // ... (improveTitle method above)
  
  async generateDescription(url: string, title: string): Promise<string> {
    try {
      const timestampedUrl = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;
      
      const response = await api.post<OpenRouterResponse>('/ai/generate', {
        prompt: timestampedUrl,
        title
      });
      
      let description = response.data.text.trim();
      description = description
        .replace(/\*\*/g, '')
        .replace(/\*/g, '')
        .replace(/^[-*•]/g, '')
        .trim();
      
      return description;
    } catch (error) {
      console.error('Error generating description:', error);
      throw new Error('Failed to generate description with AI');
    }
  }
};
```

### Environment Variables Required
```
AI_DESCRIPTION_PROMPT="Please analyze this product {{URL}}, the products title is \"{{TITLE}}\". Extract key features and specifications. Focus on:\n- Main product benefits\n- Technical specifications\n- Present in clear bullet points with emoji categorization. Keep under 200 words. Ignore any prices on the page. The description is for a UK deals website (using GBP) to allow the deals website viewer to learn about the product."
```

### Frontend Usage Example

```typescript
// In a React component
const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);

const handleGenerateDescription = async () => {
  try {
    const currentUrl = watch('url');
    
    if (!currentUrl) {
      toast.error('Please enter a product URL first');
      return;
    }
    
    setIsGeneratingDescription(true);
    const generatedDescription = await aiService.generateDescription(currentUrl, getValues('title'));
    
    if (generatedDescription) {
      setValue('description', generatedDescription, { shouldValidate: true });
      toast.success('Description generated successfully');
    }
  } catch (error) {
    console.error('Error generating description:', error);
    toast.error('Failed to generate description');
  } finally {
    setIsGeneratingDescription(false);
  }
};

// UI Button
<button
  type="button"
  onClick={handleGenerateDescription}
  disabled={isGeneratingDescription || !url}
  className="flex items-center gap-1 px-3 py-2 bg-gradient-to-r from-green-500 to-teal-600 text-white text-sm font-medium rounded hover:from-green-600 hover:to-teal-700"
>
  <DocumentTextIcon className="h-4 w-4" />
  {isGeneratingDescription ? 'Generating...' : 'Grab a Description'}
</button>
```

---

## 3. Scraper System (HotUKDeals)

### Purpose
Automatically scrapes deals from HotUKDeals and imports them into the database.

### Database Schema Required

```sql
-- Deals table
CREATE TABLE deals (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER,
  title TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10,2),
  original_price DECIMAL(10,2),
  store_id INTEGER,
  category_id INTEGER,
  url TEXT,
  image_url TEXT,
  created_at DATETIME,
  updated_at DATETIME,
  source TEXT,
  external_id TEXT,
  status TEXT DEFAULT 'pending'
);

-- Scrape logs table
CREATE TABLE scrape_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  source TEXT NOT NULL,
  timestamp DATETIME,
  status TEXT,
  deals_found INTEGER DEFAULT 0,
  deals_added INTEGER DEFAULT 0,
  data_file TEXT,
  error TEXT
);

-- Stores table
CREATE TABLE stores (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  url TEXT,
  logoUrl TEXT
);

-- Categories table
CREATE TABLE categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  slug TEXT
);
```

### Implementation

#### Scraper Class (`cron/hukdScraper.js`)

```javascript
const { getDatabase } = require('../models/database');
const path = require('path');
const fs = require('fs');
const { format } = require('date-fns');

class HotUKDealsScraper {
  constructor() {
    this.db = null;
    this.logDir = path.join(process.cwd(), 'scraper-data');
    this.startTime = new Date();
    this.totalDealsFound = 0;
    this.dealsAdded = 0;
    this.skippedDeals = 0;
    this.SYSTEM_USER_ID = 1;
    
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  async initialize() {
    this.db = await getDatabase();
  }

  async run() {
    console.log('Starting HUKD scraper at:', this.startTime.toISOString());

    let logId;
    try {
      await this.initialize();

      // Insert initial log entry
      const result = await this.db.get(`
        INSERT INTO scrape_logs (source, timestamp, status, deals_found, deals_added)
        VALUES ('hotukdeals', datetime('now'), 'started', 0, 0)
        RETURNING id
      `);
      
      logId = result.id;

      // Fetch and process deals
      const deals = await this.fetchDeals();
      
      // Save detailed data to JSON file
      const timestamp = format(new Date(), 'yyyyMMdd-HHmmss');
      const dataFile = `hukd-${timestamp}.json`;
      const dataFilePath = path.join(this.logDir, dataFile);
      
      fs.writeFileSync(dataFilePath, JSON.stringify(deals, null, 2));

      // Update log with results
      await this.db.get(`
        UPDATE scrape_logs 
        SET status = 'completed',
            deals_found = ${this.totalDealsFound},
            deals_added = ${this.dealsAdded},
            data_file = '${dataFile}',
            timestamp = datetime('now')
        WHERE id = ${logId}
      `);

      console.log(`Scraping completed. Found: ${this.totalDealsFound}, Added: ${this.dealsAdded}`);
      return { 
        success: true, 
        dealsFound: this.totalDealsFound, 
        dealsAdded: this.dealsAdded,
        dealsSkipped: this.skippedDeals 
      };

    } catch (error) {
      console.error('Scraper error:', error);
      
      if (this.db && logId) {
        await this.db.get(`
          UPDATE scrape_logs 
          SET status = 'error',
              error = '${error.message.replace(/'/g, "''")}',
              timestamp = datetime('now')
          WHERE id = ${logId}
        `);
      }
      
      return { success: false, error: error.message };
    }
  }

  async fetchDeals() {
    console.log('Fetching HUKD hot deals page...');
    const response = await fetch('https://www.hotukdeals.com/hot', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const html = await response.text();
    console.log('Page fetched successfully');

    // Extract deal data using regex pattern
    const threadDataRegex = /data-vue2='(\{"name":"ThreadMainListItemNormalizer","props":\{"thread":[^']+\}})'/g;
    const deals = [];
    let match;

    while ((match = threadDataRegex.exec(html)) !== null) {
      try {
        const jsonStr = match[1].replace(/&quot;/g, '"');
        const vueData = JSON.parse(jsonStr);
        const currentDealData = vueData.props.thread;
        
        this.totalDealsFound++;
        
        if (!currentDealData?.link) {
          console.log(`Skipping deal: "${currentDealData?.title}" - No URL provided`);
          this.skippedDeals++;
          continue;
        }

        try {
          const url = new URL(currentDealData.link);
          const storeName = url.hostname.replace('www.', '').split('.')[0];
          const cleanUrl = currentDealData.link.split('?')[0];
          
          const deal = {
            title: currentDealData.title,
            description: currentDealData.description || '',
            price: parseFloat(currentDealData.price) || 0,
            original_price: parseFloat(currentDealData.nextBestPrice) || null,
            url: cleanUrl,
            image_url: currentDealData.mainImage ? 
              `https://images.hotukdeals.com/${currentDealData.mainImage.path}/${currentDealData.mainImage.uid}` : 
              null,
            store_name: currentDealData.merchant?.merchantName || storeName,
            temperature: parseFloat(currentDealData.temperature) || 0,
            category_name: currentDealData.mainGroup?.threadGroupName || 'Uncategorized',
            hukd_id: parseInt(currentDealData.threadId),
            status: 'pending',
            created_at: new Date().toISOString()
          };

          deals.push(deal);
          await this.saveDealToDatabase(deal);

        } catch (urlError) {
          console.log(`Skipping deal: "${currentDealData.title}" - Invalid URL format`);
          this.skippedDeals++;
          continue;
        }

      } catch (e) {
        this.skippedDeals++;
        console.log('Skipping deal: Invalid deal data');
      }
    }

    return deals;
  }

  async saveDealToDatabase(deal) {
    try {
      // Find or create store
      const storeResult = await this.db.get(`
        INSERT INTO stores (name, url, logoUrl)
        SELECT 
          '${deal.store_name.replace(/'/g, "''")}', 
          '${deal.url}',
          NULL
        WHERE NOT EXISTS (
          SELECT 1 FROM stores WHERE LOWER(name) LIKE LOWER('${deal.store_name}') || '%'
        )
        RETURNING id;
      `);

      const storeId = storeResult?.id || await this.db.get(`
        SELECT id FROM stores WHERE LOWER(name) LIKE LOWER('${deal.store_name}') || '%'
      `).then(result => result?.id);

      // Find or create category
      const categoryResult = await this.db.get(`
        INSERT INTO categories (name, slug)
        SELECT 
          '${deal.category_name.replace(/'/g, "''")}',
          '${deal.category_name.toLowerCase().replace(/[^a-z0-9]+/g, "-")}'
        WHERE NOT EXISTS (
          SELECT 1 FROM categories WHERE LOWER(name) LIKE LOWER('${deal.category_name}') || '%'
        )
        RETURNING id;
      `);

      const categoryId = categoryResult?.id || await this.db.get(`
        SELECT id FROM categories WHERE LOWER(name) LIKE LOWER('${deal.category_name}') || '%'
      `).then(result => result?.id);

      // Insert deal
      const insertResult = await this.db.get(`
        INSERT INTO deals 
        (user_id, title, description, price, original_price, store_id, 
         category_id, url, image_url, created_at, updated_at, source, external_id, status)
        SELECT 
          ${this.SYSTEM_USER_ID},
          '${deal.title.replace(/'/g, "''")}',
          '${deal.description.replace(/'/g, "''")}',
          ${deal.price},
          ${deal.original_price || 'NULL'},
          ${storeId},
          ${categoryId},
          '${deal.url}',
          ${deal.image_url ? `'${deal.image_url}'` : 'NULL'},
          strftime('%Y-%m-%dT%H:%M:%fZ', 'now'),
          strftime('%Y-%m-%dT%H:%M:%fZ', 'now'),
          'hukd',
          '${deal.hukd_id}',
          '${deal.status}'
        WHERE NOT EXISTS (
          SELECT 1 FROM deals WHERE external_id = '${deal.hukd_id}'
        )
        RETURNING id
      `);

      if (insertResult && insertResult.id) {
        this.dealsAdded++;
        console.log(`Added new deal: ${deal.title}`);
      }
    } catch (error) {
      console.error('Error saving deal to database:', error);
    }
  }
}

module.exports = HotUKDealsScraper;
```

#### Controller (`controllers/hotukdeals.js`)

```javascript
const HotUKDealsScraper = require('../cron/hukdScraper');

async function runHotUKDealsScraper() {
  const scraper = new HotUKDealsScraper();
  return await scraper.run();
}

module.exports = {
  runHotUKDealsScraper
};
```

### Cron Job Setup

To run the scraper automatically, set up a cron job:

```javascript
// In your main server file or cron setup
const cron = require('node-cron');
const { runHotUKDealsScraper } = require('./src/controllers/hotukdeals');

// Run every 6 hours
cron.schedule('0 */6 * * *', async () => {
  console.log('Starting scheduled HUKD scraper...');
  try {
    const result = await runHotUKDealsScraper();
    console.log('Scraper completed:', result);
  } catch (error) {
    console.error('Scraper failed:', error);
  }
});
```

### Manual Trigger API Endpoint

```javascript
// Add to your routes
app.post('/api/admin/scrape/hukd', authMiddleware, async (req, res) => {
  try {
    const result = await runHotUKDealsScraper();
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

---

## Integration Tips

### 1. Error Handling
- Always provide fallbacks when AI calls fail
- Log errors for debugging
- Return original data if processing fails

### 2. Rate Limiting
- Add delays between scraper requests
- Implement retry logic with exponential backoff
- Respect robots.txt and rate limits

### 3. Performance
- Cache AI responses where possible
- Run scrapers during off-peak hours
- Use database indexes on external_id fields

### 4. Security
- Validate and sanitize all scraped data
- Use environment variables for API keys
- Implement authentication for admin endpoints

### 5. Monitoring
- Track success/failure rates
- Monitor API usage and costs
- Set up alerts for scraper failures

---

## Environment Variables Summary

```env
# OpenRouter AI
OPENROUTER_API_KEY=your_api_key_here
FRONTEND_URL=http://localhost:3000

# AI Prompts
AI_DESCRIPTION_PROMPT="Please analyze this product {{URL}}, the products title is \"{{TITLE}}\". Extract key features and specifications. Focus on:\n- Main product benefits\n- Technical specifications\n- Present in clear bullet points with emoji categorization. Keep under 200 words. Ignore any prices on the page. The description is for a UK deals website (using GBP) to allow the deals website viewer to learn about the product."

# Scraper Settings
SYSTEM_USER_ID=1
SCRAPER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
SCRAPER_DELAY=2000
```

This documentation provides everything needed to implement these three functions in a new application. The code examples are production-ready and include proper error handling, logging, and security considerations.
