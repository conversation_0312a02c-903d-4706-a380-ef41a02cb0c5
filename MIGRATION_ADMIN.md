# Admin Section Migration Plan (CSR to SSR)

This document outlines the plan to migrate the admin functionality from the old CSR React application (`frontend/src/pages/admin`) to the new SSR Next.js application (`frontend/app/admin`). The goal is to replicate the exact look, feel, and functionality of the existing admin panel.

## General Approach for Each Page/Section:

1.  **Understand Existing Functionality**: Thoroughly review the CSR component (`frontend/src/pages/admin/<PageName>.tsx`) to understand all its features, UI elements, state management logic, API interactions, form validations, and any page-specific business logic (e.g., regex for scrapers, complex calculations).
2.  **Code Migration Strategy**: All necessary code from the CSR application (`frontend/src`) including components (from `frontend/src/components`), services (from `frontend/src/services`), utility functions (from `frontend/src/utils`), and types (from `frontend/src/types`) **must be recreated or migrated into the new SSR structure within `frontend/app`**. Direct imports from `frontend/src` into `frontend/app` are to be avoided to ensure a clean separation and a self-contained SSR application. This may involve rewriting, refactoring, or copying and adapting existing code.
3.  **Data Fetching Strategy (SSR vs. Client-side)**:
    *   For data essential for the initial page render and potentially beneficial for SEO (though less critical for admin panels), utilize Next.js server-side data fetching (e.g., in `async function getData()` within page components or via Route Handlers if abstracting data logic).
    *   For interactive data, data that updates frequently after the initial load, or data triggered by user actions, employ client-side fetching (e.g., using `useEffect` with `fetch`, or libraries like SWR/React Query if already in use or planned) within components marked with `'use client'`.
4.  **API Endpoint Integration**:
    *   Verify all API calls target the correct backend services and endpoints.
    *   API service files will be recreated/migrated to `frontend/app/lib/services` (or similar) and should use a correctly configured Axios instance (e.g., `frontend/app/lib/api.ts`) that includes JWT token handling for authenticated requests.
5.  **State Management**:
    *   For local component state, use `useState` and `useReducer`.
    *   For cross-component state or global state (like user authentication), leverage `useContext` (e.g., a new or migrated `AuthContext` for `frontend/app`) or URL-based state management offered by Next.js.
6.  **Routing and Navigation**:
    *   Recreate the routing structure using the Next.js App Router conventions (e.g., `frontend/app/admin/categories/page.tsx`).
    *   Implement dynamic routes for pages that require parameters (e.g., `frontend/app/admin/deals/[id]/page.tsx` for editing a specific deal).
    *   Use the `next/link` component for navigation and `next/navigation` ( `useRouter`, `useSearchParams`) for programmatic navigation and accessing URL parameters.
7.  **Styling**:
    *   Ensure Tailwind CSS is correctly configured and applied in the Next.js environment to achieve an identical visual appearance. Migrate or replicate any custom CSS into the `frontend/app` structure.
8.  **Authentication & Authorization**:
    *   Protect all admin routes. This will likely involve a layout component for the `/admin` path that checks authentication status (e.g., using a migrated/recreated `AuthContext` for `frontend/app`).
    *   Ensure that backend API calls are properly authenticated and authorized.
9.  **Form Handling**:
    *   Replicate all forms with their respective fields, validation rules (client-side and potentially server-side), and submission logic within `frontend/app`.
    *   Use libraries like React Hook Form if forms are complex, or manage them with React state for simpler cases.
10. **Testing**:
    *   After migration, thoroughly test each page for functional parity, UI consistency, responsiveness, error handling, and performance.

## UI Component Strategy

After investigation, we've determined the following about UI components in the project:

### Current Implementation

1.  **CSR Admin Area (`frontend/src`):**
    *   Uses custom UI components in `frontend/src/components/ui/` (tabs, card, skeleton)
    *   Uses Heroicons for icons.
    *   Some custom components resemble shadcn/ui in appearance but are not directly from that library.
2.  **Existing SSR Area (`frontend/app`):**
    *   Already has some manually created shadcn/ui-style components in `frontend/app/components/ui/`.
    *   Uses Heroicons.
    *   `tailwind.config.js` is set up for shadcn/ui theming variables.

### SSR UI Strategy (Confirmed)

1.  **NO `shadcn/ui` CLI**: Do not use `npx shadcn-vue@latest add` or similar due to potential conflicts with the existing `tailwind.config.js` and the manual setup.
2.  **Continue Manual Implementation**:
    *   Use existing manually created shadcn/ui-style components in `frontend/app/components/ui/`.
    *   For any *new* components required that were previously custom in `frontend/src` or are needed for the admin section, find their equivalent on the [shadcn/ui documentation website](https://ui.shadcn.com/docs/components).
    *   Manually copy the code (React version) for these components from the shadcn/ui website into new files within `frontend/app/components/ui/`. Adapt as necessary.
    *   This approach maintains consistency with the existing setup in `frontend/app` and avoids CLI complexities.
3.  **Heroicons**: Continue using Heroicons for iconography.
4.  **Component Migration Mapping (Conceptual - to be built in `frontend/app`):**
    *   CSR custom components (`frontend/src/components/ui/`) → Recreate as shadcn/ui equivalent components in `frontend/app/components/ui/`.
    *   Example mappings:
        *   Custom alerts/modals → shadcn/ui `alert-dialog` or `dialog`
        *   Custom tables → shadcn/ui `table`
        *   Custom forms → shadcn/ui `form` (with inputs, labels, etc.)
        *   Custom cards → shadcn/ui `card`
        *   Custom tabs → shadcn/ui `tabs`
        *   Custom buttons → shadcn/ui `button`
        *   Custom skeletons → shadcn/ui `skeleton`

## Page-Specific Migration Plans

(Tick off items as they are completed)

- [ ] ### 1. Admin Dashboard
    *   **CSR Path**: `frontend/src/pages/admin/AdminDashboardPage.tsx`
    *   **Target SSR Path**: `frontend/app/admin/page.tsx` (assuming this is the main admin landing/dashboard page)
    *   **Plan**:
        1.  Recreate the overall layout and structure in `frontend/app/admin/page.tsx`.
        2.  Identify all data displayed (e.g., statistics, summaries, recent activities).
        3.  Determine data fetching strategy (server-side for initial view, client-side for updates if any).
        4.  Rebuild UI components (cards, charts if any, stat displays) using the manual shadcn/ui strategy in `frontend/app/components/ui/`.
        5.  Migrate/recreate any services needed for data fetching into `frontend/app/lib/services/`.
        6.  Implement necessary state management within the page or related client components.

- [ ] ### 2. Admin Categories
    *   **CSR Path**: `frontend/src/pages/admin/AdminCategoriesPage.tsx`
    *   **Target SSR Path**: `frontend/app/admin/categories/page.tsx`
    *   **Plan**:
        1.  Recreate the categories listing table/grid in `frontend/app/admin/categories/page.tsx`.
        2.  Implement functionality for creating, editing, and deleting categories (likely via modals or separate pages like `frontend/app/admin/categories/new/page.tsx` and `frontend/app/admin/categories/[id]/edit/page.tsx`).
        3.  Rebuild UI components (table, forms, modals, buttons) using manual shadcn/ui components in `frontend/app/components/ui/`.
        4.  Migrate/recreate `categoryService` and any related types/utils into `frontend/app`.
        5.  Data fetching: List likely server-rendered; mutations (create/edit/delete) via client-side actions calling API endpoints.

- [ ] ### 3. Admin Stores
    *   **CSR Path**: `frontend/src/pages/admin/AdminStoresPage.tsx`
    *   **Target SSR Path**: `frontend/app/admin/stores/page.tsx`
    *   **Plan**:
        1.  Similar to Categories: recreate the stores listing.
        2.  Implement CRUD operations for stores.
        3.  Rebuild UI components (table, forms for store details including image uploads, modals) using manual shadcn/ui components.
        4.  Migrate/recreate `storeService` and related types/utils into `frontend/app`.
        5.  Handle image uploads if this functionality exists.

- [ ] ### 4. Admin Deals
    *   **CSR Paths**:
        *   Listing: `frontend/src/pages/admin/AdminDealsPage.tsx`
        *   Create/Edit: `frontend/src/pages/admin/AdminEditDealPage.tsx` (and `AdminDealFormModal.tsx`)
    *   **Target SSR Paths**:
        *   `frontend/app/admin/deals/page.tsx` (for listing deals)
        *   `frontend/app/admin/deals/new/page.tsx` (for creating a new deal)
        *   `frontend/app/admin/deals/[id]/edit/page.tsx` (for editing an existing deal)
        *   Consider if a modal for quick edit/create on the listing page is still desired and how it would be implemented with Next.js App Router (e.g., Intercepting Routes or Parallel Routes).
    *   **Plan**:
        1.  **Deals List (`AdminDealsPage.tsx` -> `deals/page.tsx`)**:
            *   Recreate the deals table with columns for title, store, category, dates, status, price, etc.
            *   Implement server-side fetching for the initial list, with client-side for pagination, sorting, and filtering.
            *   Rebuild filter components (dropdowns for type, store, category; date pickers; search bar) using manual shadcn/ui.
            *   Replicate UI for sort indicators and batch action controls (e.g., delete selected, change status).
            *   Ensure "select all" and individual deal selection for batch actions work correctly.
        2.  **Create/Edit Deal (`AdminEditDealPage.tsx` -> `deals/new/page.tsx` & `deals/[id]/edit/page.tsx`)**:
            *   Recreate the deal form with all fields (title, description, URL, image upload, price, discount, dates, store/category selection, type, status, etc.).
            *   Rebuild the form using manual shadcn/ui components.
            *   Implement client-side validation and submission logic.
            *   Migrate/recreate `dealService` and associated types/utils (like `DealFormData`, `DealStatus`, `DealType`) into `frontend/app`.
            *   Handle image uploads and interactions with any rich text editors.
        3.  **Modal Usage**: If `AdminDealFormModal` was used for quick edits/creations from the list, evaluate how to best replicate this. This might involve client components and potentially Next.js advanced routing features if the modal updates the URL or needs to be shareable.

- [ ] ### 5. Admin Scrapers
    *   **CSR Path**: `frontend/src/pages/admin/AdminScrapersPage.tsx`
    *   **Target SSR Path**: `frontend/app/admin/scrapers/page.tsx`
    *   **Plan**:
        1.  Recreate the scraper configuration listing.
        2.  Implement UI for adding, editing, and deleting scraper configurations (store, URL, selectors for deal elements like title, price, image, etc.).
        3.  Rebuild UI components (table, forms with regex inputs, test buttons) using manual shadcn/ui.
        4.  Migrate/recreate `scraperService` (or equivalent admin functions for scrapers) into `frontend/app`.
        5.  Implement functionality to test scraper rules and view last run status/results.

- [ ] ### 6. Admin Users
    *   **CSR Path**: `frontend/src/pages/admin/AdminUsersPage.tsx`
    *   **Target SSR Path**: `frontend/app/admin/users/page.tsx`
    *   **Plan**:
        1.  Recreate the user listing table.
        2.  Implement functionality to view user details, change roles, ban/unban users.
        3.  Rebuild UI components (table, modals for actions, search/filter) using manual shadcn/ui.
        4.  Migrate/recreate `userService` (or admin user functions) and related types into `frontend/app`.
        5.  Ensure search and filtering functionalities are identical.

## Cross-Cutting Concerns (To be built/migrated into `frontend/app`):

*   **Services (e.g., `frontend/app/lib/services/`)**:
    *   Services used by the admin pages (e.g., `authService`, `adminService`, `categoryService`, `storeService`, `dealService`, `userService`), originally in `frontend/src/services`, **must be recreated or migrated into `frontend/app/lib/services` (or a similar appropriate path within `frontend/app`)**. This ensures that the new admin section does not directly depend on `frontend/src`. API service files should use the correctly configured Axios instance (likely `frontend/app/lib/api.ts` or equivalent) for JWT token handling.
*   **Types (e.g., `frontend/app/types/` or co-located)**:
    *   All necessary TypeScript types and interfaces originally defined in `frontend/src/types` **should be copied or redefined within the `frontend/app` directory structure** (e.g., in a new `frontend/app/types` directory or co-located with their respective modules). Avoid direct imports of types from `frontend/src`.
*   **Utilities (e.g., `frontend/app/lib/utils/`)**:
    *   Utility functions (e.g., `formatDate`, `imageUtils`, validation helpers) previously in `frontend/src/utils` that are needed for the new admin section **must be migrated or recreated within `frontend/app/lib/utils` (or a similar path)**. This ensures all dependencies are self-contained within the `frontend/app` codebase.
*   **Contexts (e.g., `frontend/app/context/`)**:
    *   The `AuthContext` is critical. It **must be recreated or migrated into `frontend/app/context/`**. Its provider must wrap the admin section (or the entire app) correctly in the Next.js layout structure (`frontend/app/layout.tsx` or `frontend/app/admin/layout.tsx`). Verify that authentication state and user data are accessible where needed in client components.
*   **UI Components (e.g., `frontend/app/components/ui/`, `frontend/app/components/shared/`)**:
    *   Common UI components from `frontend/src/components` (e.g., `Pagination`, `AlertModal`) **will be recreated or migrated into `frontend/app/components/ui` or other relevant component directories within `frontend/app`**, following the established 'manual shadcn/ui' strategy. No direct imports from `frontend/src/components` should be made.
*   **Environment Variables**:
    *   Ensure all backend API URLs, AI API keys, and other necessary configurations are managed via environment variables in the Next.js project (e.g., `.env.local`, prefixed with `NEXT_PUBLIC_` for client-side access if needed).

## `tsconfig.json` Configuration Guidance:

Given the decision to avoid direct runtime imports from `frontend/src` into `frontend/app`, the `paths` configuration in `frontend/tsconfig.json` should primarily support clean module resolution within the new `frontend/app` structure.

**Current `frontend/tsconfig.json` state (as per user info):**
*   `target`: `es5`
*   `paths`: `{"@/*": ["./src/*"]}`
*   `downlevelIteration` removed.
*   (Assuming `baseUrl` is either `"."` at project root or `"./frontend"`)

**Recommended `tsconfig.json` Setup for `frontend/app`:**

The goal is to make imports within `frontend/app` intuitive and self-contained.

If `tsconfig.json` is located at [c:\CODING\nicedeals\frontend\tsconfig.json](cci:7://file:///CODING/nicedeals/frontend/tsconfig.json:0:0-0:0):
```json
{
  "compilerOptions": {
    "baseUrl": ".", // This means paths are relative to c:/CODING/nicedeals/frontend/
    "paths": {
      "@/*": ["./app/*"] // So @/foo maps to c:/CODING/nicedeals/frontend/app/foo
      // You can add more specific paths if needed, e.g.:
      // "@/components/*": ["./app/components/*"],
      // "@/lib/*": ["./app/lib/*"]
    },
    "target": "es5", // Keep as es5 if it was a deliberate change for compatibility
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler", // Or "node" depending on your setup, "bundler" is common for Next.js
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve", // Crucial for Next.js
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ]
    // Remove "downlevelIteration" if it was indeed removed and not needed.
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts" // if using Next.js 13.3+ app directory
  ],
  "exclude": ["node_modules"]
}
