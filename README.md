# NiceDeals - Slickdeals Clone

## Getting Started
Live server domain will be nicedeal.app

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install backend dependencies:
   ```
   cd backend
   npm install
   ```
3. Install frontend dependencies:
   ```
   cd frontend
   npm install
   ```

### Running the Application

1. Start the backend server:
   ```
   cd backend
   npm start
   ```
2. Start the frontend development server (nextJS):
   ```
   cd frontend
   npm run dev
   ```

## Development

- Backend will run on http://localhost:5010
- Frontend will run on http://localhost:3010

## Deployment

Instructions for deploying to an Ubuntu server are included in the deployment guide.

## Scraper

- Run a custom scraper: node src/controllers/scrapers.js runCustomScraper hotukdeals

## Crontab (still to test)

Setup Instructions:
- Upload the updated generate-sitemap.sh to your server at /var/www/nicedeals/frontend/scripts/
- Make the script executable:

chmod +x /var/www/nicedeals/frontend/scripts/generate-sitemap.sh
- Create the logs directory:
mkdir -p /var/www/nicedeals/frontend/logs
chown -R www-data:www-data /var/www/nicedeals/frontend/logs
- Add the crontab entry:
crontab -e
(Then add the line shown above and save)
- Test the script manually:
/bin/bash /var/www/nicedeals/frontend/scripts/generate-sitemap.sh
- Check the log file:
cat /var/www/nicedeals/frontend/logs/sitemap.log
- This approach is much simpler than the systemd service and will work just as well for this use case. 
The script will run daily at 3 AM and log its output to /var/www/nicedeals/frontend/logs/sitemap.log.
