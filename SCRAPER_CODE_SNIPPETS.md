# Quick Reference - Scraper Code Snippets

## Core Scraper Logic (The Heart of the System)

```javascript
// Main regex to extract Vue.js data from HotUKDeals HTML
const threadDataRegex = /data-vue2='(\{"name":"ThreadMainListItemNormalizer","props":\{"thread":[^']+\}})'/g;

// Parse the embedded JSON data
const jsonStr = match[1].replace(/&quot;/g, '"');
const vueData = JSON.parse(jsonStr);
const dealData = vueData.props.thread;

// Extract deal information
const deal = {
  title: dealData.title,
  description: dealData.description || '',
  price: parseFloat(dealData.price) || 0,
  original_price: parseFloat(dealData.nextBestPrice) || null,
  url: dealData.link.split('?')[0], // Remove query params
  image_url: dealData.mainImage ? 
    `https://images.hotukdeals.com/${dealData.mainImage.path}/${dealData.mainImage.uid}` : null,
  store_name: dealData.merchant?.merchantName || url.hostname.replace('www.', '').split('.')[0],
  temperature: parseFloat(dealData.temperature) || 0,
  category_name: dealData.mainGroup?.threadGroupName || 'Uncategorized',
  hukd_id: parseInt(dealData.threadId),
  status: 'pending'
};
```

## Database Schema

```sql
-- Scraper logs table
CREATE TABLE scrape_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  source TEXT NOT NULL,
  timestamp TEXT DEFAULT (datetime('now')),
  status TEXT, -- 'started', 'completed', 'error'
  deals_found INTEGER DEFAULT 0,
  deals_added INTEGER DEFAULT 0,
  error TEXT,
  data_file TEXT
);

-- Deals table (key fields)
ALTER TABLE deals ADD COLUMN source TEXT DEFAULT 'manual';
ALTER TABLE deals ADD COLUMN external_id TEXT; -- For HotUKDeals thread ID
```

## API Endpoints

```javascript
// GET /api/admin/scrapers/logs
export default async function handler(req, res) {
  const result = await db.query(`
    SELECT id, source, timestamp as created_at, status, deals_found, deals_added, error
    FROM scrape_logs ORDER BY timestamp DESC LIMIT 20
  `);
  res.json({ success: true, data: { logs: result.rows } });
}

// POST /api/admin/scrapers/run
export default async function handler(req, res) {
  const { source } = req.body;
  const scraper = new HotUKDealsScraper(db);
  const result = await scraper.run();
  res.json({ success: true, data: result });
}
```

## Frontend Component (Minimal)

```jsx
function AdminScrapersPage() {
  const [logs, setLogs] = useState([]);
  const [running, setRunning] = useState(false);

  const runScraper = async () => {
    setRunning(true);
    const response = await fetch('/api/admin/scrapers/run', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ source: 'hotukdeals' })
    });
    const data = await response.json();
    if (data.success) {
      toast.success('Scraper completed');
      fetchLogs();
    }
    setRunning(false);
  };

  return (
    <div>
      <button onClick={runScraper} disabled={running}>
        {running ? 'Running...' : 'Run HotUKDeals Scraper'}
      </button>
      
      <table>
        <thead>
          <tr><th>Source</th><th>Status</th><th>Found</th><th>Added</th><th>Date</th></tr>
        </thead>
        <tbody>
          {logs.map(log => (
            <tr key={log.id}>
              <td>{log.source}</td>
              <td>{log.status}</td>
              <td>{log.deals_found}</td>
              <td>{log.deals_added}</td>
              <td>{new Date(log.created_at).toLocaleString()}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
```

## Key Technical Details

1. **Target URL**: `https://www.hotukdeals.com/hot`
2. **User-Agent Required**: `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36`
3. **Data Source**: Vue.js components embedded in HTML as `data-vue2` attributes
4. **Duplicate Prevention**: Use `external_id` field with HotUKDeals thread ID
5. **File Storage**: Save raw JSON data to `scraper-data/` directory
6. **Error Handling**: Log all scraper runs to `scrape_logs` table

## Dependencies

```json
{
  "dependencies": {
    "date-fns": "^2.29.3",
    "react-hot-toast": "^2.4.1"
  }
}
``` 