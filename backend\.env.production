# Server configuration
PORT=5010
NODE_ENV=production

# JWT Secret - Change this to a secure random string in productions
JWT_SECRET=2afaed26f3cf5f2f8f910587f6357cb297242c9825c61d3af2db6f611005b9f6
JWT_EXPIRATION=7d

# Frontend URL for CORS
FRONTEND_URL=https://www.nicedeals.app

# Admin settings
ADMIN_DEALS_PER_PAGE=30
DEALS_PAGE_SIZE=30

# Scraper settings
SCRAPER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
SCRAPER_DELAY=2000
SYSTEM_USER_ID=1
SCRAPER_DEBUG=false

AI_DESCRIPTION_PROMPT="Please analyze this product {{URL}}, the products title is \"{{TITLE}}\". Extract key features and specifications. Focus on:\n- Main product benefits\n- Technical specifications\n- Present in clear bullet points with emoji categorization. Keep under 200 words. Ignore any prices on the page. The description is for a UK deals website (using GBP) to allow the deals website viewer to learn about the product."

GETTING_WARM_TEMPERATURE=1
HOT_TEMPERATURE=10

# Email Configuration
BREVO_SMTP_HOST=smtp-relay.brevo.com
BREVO_PORT=587
BREVO_USER=<EMAIL> 
BREVO_PASSWORD=DzVknBjsfCyI0Y8K