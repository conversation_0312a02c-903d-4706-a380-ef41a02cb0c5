{"name": "nicedeals-backend", "version": "1.0.0", "description": "Backend API for NiceDeals - A Slickdeals Clone", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "scrape": "node src/scrapers/hotukdeals.js", "alerts": "node src/cron/processDealAlerts.js"}, "keywords": ["deals", "express", "api", "sqlite", "scraper"], "author": "", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.24.0", "axios": "^1.8.2", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^2.29.3", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "express-validator": "^7.0.1", "helmet": "^6.1.5", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "node-fetch": "^2.7.0", "nodemailer": "^6.10.0", "puppeteer": "^19.11.1", "sharp": "^0.33.5", "socket.io": "^4.6.1", "sqlite": "^4.2.0", "sqlite3": "^5.1.6", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20.12.6", "jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3", "ts-node": "^10.9.2", "typescript": "^5.4.2"}}