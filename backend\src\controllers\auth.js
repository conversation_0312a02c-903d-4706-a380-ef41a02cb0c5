const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { getDatabase } = require('../models/database');
const { sendVerificationEmail } = require('../utils/emailService');

/**
 * Register a new user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
async function register(req, res) {
  try {
    const { username, email, password, termsAccepted } = req.body;
    
    console.log('Registration attempt:', { username, email, termsAccepted });
    
    if (!username || !email || !password) {
      console.log('Missing required fields');
      return res.status(400).json({ 
        success: false,
        error: 'Please provide username, email, and password' 
      });
    }
    
    // Validate terms acceptance
    if (termsAccepted !== true) {
      console.log('Terms not accepted:', termsAccepted);
      return res.status(400).json({ 
        success: false,
        error: 'You must accept the terms and conditions' 
      });
    }
    
    const db = await getDatabase();
    
    // Check if username or email already exists
    const existingUser = await db.get(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );
    
    if (existingUser) {
      console.log('Username or email already exists:', existingUser);
      return res.status(409).json({ 
        success: false,
        error: 'Username or email already exists' 
      });
    }
    
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    // Insert new user
    const result = await db.run(
      'INSERT INTO users (username, email, password_hash, email_verified) VALUES (?, ?, ?, 0)',
      [username, email, hashedPassword]
    );
    
    console.log('User created:', result);
    
    const user = await db.get(
      'SELECT id, username, email FROM users WHERE id = ?',
      [result.lastID]
    );
    
    // Generate verification token
    const verificationToken = jwt.sign(
      { userId: user.id },
      process.env.JWT_SECRET + '_EMAIL_VERIFY',
      { expiresIn: '1h' }
    );

    await sendVerificationEmail(user.email, verificationToken);

    return res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          emailVerified: false,
          isAdmin: false,
          isModerator: false
        }
      },
      message: 'Registration successful. Please check your email to verify your account before logging in.'
    });
    
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Server error during registration' 
    });
  }
}

/**
 * Log in a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with JWT token
 */
async function login(req, res) {
  try {
    const { credential, password } = req.body;
    
    if (!credential || !password) {
      return res.status(400).json({ 
        success: false,
        error: 'Please provide login credentials and password' 
      });
    }
    
    const db = await getDatabase();
    
    // Find user by email or username
    const user = await db.get(
      'SELECT id, username, email, password_hash, is_admin, is_moderator FROM users WHERE email = ? OR username = ?',
      [credential, credential]
    );
    
    if (!user) {
      return res.status(401).json({ 
        success: false,
        error: 'Invalid credentials' 
      });
    }
    
    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    
    if (!isPasswordValid) {
      return res.status(401).json({ 
        success: false,
        error: 'Invalid credentials' 
      });
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        id: user.id, 
        username: user.username,
        isAdmin: !!user.is_admin,
        isModerator: !!user.is_moderator
      },
      process.env.JWT_SECRET || 'dev_secret',
      { expiresIn: process.env.JWT_EXPIRATION || '7d' }
    );
    
    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          isAdmin: !!user.is_admin,
          isModerator: !!user.is_moderator
        },
        token
      },
      message: 'Login successful'
    });
    
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Server error during login' 
    });
  }
}

/**
 * Get current user information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with user data
 */
async function getCurrentUser(req, res) {
  try {
    const db = await getDatabase();
    
    // Get user data
    const user = await db.get(
      'SELECT id, username, email, is_admin, is_moderator, created_at FROM users WHERE id = ?',
      [req.user.id]
    );
    
    if (!user) {
      return res.status(404).json({ 
        success: false,
        error: 'User not found' 
      });
    }
    
    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          isAdmin: !!user.is_admin,
          isModerator: !!user.is_moderator,
          createdAt: user.created_at
        }
      },
      message: 'User data retrieved successfully'
    });
    
  } catch (error) {
    console.error('Get current user error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Server error' 
    });
  }
}

/**
 * Verify email address
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
async function verifyEmail(req, res) {
  try {
    const { token } = req.query;

    if (!token) {
      return res.status(400).json({
        success: false,
        error: 'Verification token is required'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET + '_EMAIL_VERIFY');
    const db = await getDatabase();

    // Update user verification status
    await db.run(
      'UPDATE users SET email_verified = 1 WHERE id = ?',
      [decoded.userId]
    );

    return res.status(200).json({
      success: true,
      message: 'Email verified successfully'
    });

  } catch (error) {
    console.error('Email verification error:', error);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(400).json({
        success: false,
        error: 'Verification link has expired'
      });
    }

    if (error.name === 'JsonWebTokenError') {
      return res.status(400).json({
        success: false,
        error: 'Invalid verification token'
      });
    }

    return res.status(500).json({
      success: false,
      error: 'Server error during verification'
    });
  }
}

/**
 * Resend verification email
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
async function resendVerification(req, res) {
  try {
    const db = await getDatabase();
    const user = await db.get(
      'SELECT id, email, email_verified FROM users WHERE id = ?',
      [req.user.id]
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    if (user.email_verified) {
      return res.status(400).json({
        success: false,
        error: 'Email is already verified'
      });
    }

    // Generate new verification token
    const verificationToken = jwt.sign(
      { userId: user.id },
      process.env.JWT_SECRET + '_EMAIL_VERIFY',
      { expiresIn: '1h' }
    );

    await sendVerificationEmail(user.email, verificationToken);

    return res.status(200).json({
      success: true,
      message: 'Verification email has been resent'
    });

  } catch (error) {
    console.error('Resend verification error:', error);
    return res.status(500).json({
      success: false,
      error: 'Server error during resend verification'
    });
  }
}

module.exports = {
  register,
  login,
  getCurrentUser,
  verifyEmail,
  resendVerification
};
