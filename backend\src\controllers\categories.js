const { getDatabase } = require('../models/database');

/**
 * Get all categories
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getCategories(req, res) {
  try {
    const db = await getDatabase();
    const { store } = req.query;
    
    let query = `
      SELECT 
        c.id, 
        c.name, 
        c.slug,
        COUNT(DISTINCT d.id) as dealsCount, 
        SUM(CASE WHEN d.status = 'active' THEN 1 ELSE 0 END) as activeDealsCount
      FROM categories c
      LEFT JOIN deals d ON c.id = d.category_id`;

    const queryParams = [];

    // If store is specified, only count deals from that store
    if (store) {
      query += ` AND d.store_id = ?`;
      queryParams.push(store);
    }

    query += `
      GROUP BY c.id, c.name, c.slug
      ORDER BY c.name ASC
    `;
    
    const categories = await db.all(query, queryParams);
    
    res.status(200).json(categories);
  } catch (error) {
    console.error('Error getting categories:', error);
    res.status(500).json({ message: 'Error getting categories' });
  }
}

/**
 * Get a single category by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getCategoryById(req, res) {
  try {
    const { id } = req.params;
    const db = await getDatabase();
    
    const category = await db.get(`
      SELECT 
        c.id, 
        c.name, 
        c.slug,
        COUNT(d.id) as dealsCount
      FROM categories c
      LEFT JOIN deals d ON c.id = d.category_id
      WHERE c.id = ?
      GROUP BY c.id
    `, [id]);
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }
    
    res.status(200).json(category);
  } catch (error) {
    console.error('Error getting category:', error);
    res.status(500).json({ message: 'Error getting category' });
  }
}

/**
 * Create a new category (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function createCategory(req, res) {
  try {
    const { name, slug } = req.body;
    
    if (!name) {
      return res.status(400).json({ message: 'Category name is required' });
    }
    
    // Generate slug if not provided
    const categorySlug = slug || name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
    
    const db = await getDatabase();
    
    // Check if category with same name or slug already exists
    const existingCategory = await db.get(
      'SELECT id FROM categories WHERE name = ? OR slug = ?',
      [name, categorySlug]
    );
    
    if (existingCategory) {
      return res.status(400).json({ message: 'Category with this name or slug already exists' });
    }
    
    // Insert new category
    const result = await db.run(
      'INSERT INTO categories (name, slug) VALUES (?, ?)',
      [name, categorySlug]
    );
    
    const newCategory = await db.get('SELECT * FROM categories WHERE id = ?', [result.lastID]);
    
    res.status(201).json(newCategory);
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ message: 'Error creating category' });
  }
}

/**
 * Update a category (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function updateCategory(req, res) {
  try {
    const { id } = req.params;
    const { name, slug } = req.body;
    
    if (!name && !slug) {
      return res.status(400).json({ message: 'At least one field must be provided' });
    }
    
    const db = await getDatabase();
    
    // Check if category exists
    const [existingCategories] = await db.query(
      'SELECT id FROM categories WHERE id = ?',
      [id]
    );
    
    if (existingCategories.length === 0) {
      return res.status(404).json({ message: 'Category not found' });
    }
    
    // Check if slug is unique if provided
    if (slug) {
      const [existingSlug] = await db.query(
        'SELECT id FROM categories WHERE slug = ? AND id != ?',
        [slug, id]
      );
      
      if (existingSlug.length > 0) {
        return res.status(400).json({ message: 'Category with this slug already exists' });
      }
    }
    
    // Build update query
    let updateQuery = 'UPDATE categories SET ';
    const queryParams = [];
    
    if (name) {
      updateQuery += 'name = ?';
      queryParams.push(name);
      
      if (slug) {
        updateQuery += ', ';
      }
    }
    
    if (slug) {
      updateQuery += 'slug = ?';
      queryParams.push(slug);
    }
    
    updateQuery += ' WHERE id = ?';
    queryParams.push(id);
    
    await db.query(updateQuery, queryParams);
    
    // Get updated category
    const [updatedCategories] = await db.query(
      `
      SELECT 
        c.id, 
        c.name, 
        c.slug,
        COUNT(d.id) as dealsCount
      FROM categories c
      LEFT JOIN deals d ON c.id = d.category_id
      WHERE c.id = ?
      GROUP BY c.id
      `,
      [id]
    );
    
    res.status(200).json(updatedCategories[0]);
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ message: 'Error updating category' });
  }
}

/**
 * Delete a category (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function deleteCategory(req, res) {
  try {
    const { id } = req.params;
    const db = await getDatabase();
    
    // Check if category exists
    const [existingCategories] = await db.query(
      'SELECT id FROM categories WHERE id = ?',
      [id]
    );
    
    if (existingCategories.length === 0) {
      return res.status(404).json({ message: 'Category not found' });
    }
    
    // Check if category has deals
    const dealsCount = await db.get(
      'SELECT COUNT(*) as count FROM deals WHERE category_id = ?',
      [id]
    );
    
    if (dealsCount && dealsCount.count > 0) {
      return res.status(400).json({ 
        message: `Cannot delete category with ${dealsCount.count} associated deals`
      });
    }
    
    // Delete category
    await db.query('DELETE FROM categories WHERE id = ?', [id]);
    
    res.status(200).json({ message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ message: 'Error deleting category' });
  }
}

module.exports = {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory
};
