const { getDatabase } = require('../models/database');

/**
 * Get comments for a deal
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getComments(req, res) {
  try {
    const { dealId } = req.params;
    const db = await getDatabase();
    
    // Get comments with user information
    const comments = await db.all(`
      SELECT 
        c.id, c.text, c.created_at, c.parent_id,
        u.id as user_id, u.username
      FROM comments c
      JOIN users u ON c.user_id = u.id
      WHERE c.deal_id = ?
      ORDER BY c.created_at ASC
    `, [dealId]);
    
    // Organize comments into a tree structure
    const commentMap = {};
    const rootComments = [];
    
    comments.forEach(comment => {
      commentMap[comment.id] = {
        ...comment,
        replies: []
      };
    });
    
    comments.forEach(comment => {
      if (comment.parent_id) {
        // This is a reply, add it to its parent's replies
        if (commentMap[comment.parent_id]) {
          commentMap[comment.parent_id].replies.push(commentMap[comment.id]);
        }
      } else {
        // This is a root comment
        rootComments.push(commentMap[comment.id]);
      }
    });
    
    res.json(rootComments);
    
  } catch (error) {
    console.error('Get comments error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

/**
 * Add a comment to a deal
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function addComment(req, res) {
  try {
    const { dealId, text, parentId = null } = req.body;
    const userId = req.user.id;
    
    console.log('Adding comment:', {
      dealId,
      text,
      parentId,
      userId
    });

    if (!dealId || !text) {
      console.log('Missing required fields:', { dealId, text });
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const db = await getDatabase();
    
    // First check if deal exists and is active
    const dealCheck = await db.get(`
      SELECT id, status FROM deals WHERE id = ?
    `, [dealId]);
    
    console.log('Deal check result:', dealCheck);
    
    if (!dealCheck) {
      console.log(`Deal ${dealId} not found in database`);
      return res.status(404).json({ error: 'Deal not found' });
    }
    
    if (dealCheck.status !== 'active') {
      console.log(`Deal ${dealId} is not active (status: ${dealCheck.status})`);
      return res.status(400).json({ error: 'Comments can only be added to active deals' });
    }

    // Add the comment
    const result = await db.run(`
      INSERT INTO comments (deal_id, user_id, text, parent_id, created_at)
      VALUES (?, ?, ?, ?, datetime('now'))
    `, [dealId, userId, text.trim(), parentId]);
    
    console.log('Comment added successfully:', result);

    // Get the newly created comment with user info
    const newComment = await db.get(`
      SELECT c.*, u.username
      FROM comments c
      JOIN users u ON c.user_id = u.id
      WHERE c.id = ?
    `, [result.lastID]);
    
    console.log('Returning new comment:', newComment);
    
    res.json(newComment);
  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(500).json({ error: 'Failed to add comment' });
  }
}

/**
 * Update a comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function updateComment(req, res) {
  try {
    const { commentId } = req.params;
    const { text } = req.body;
    
    if (!text) {
      return res.status(400).json({ error: 'Comment text is required' });
    }
    
    const db = await getDatabase();
    
    // Check if comment exists and belongs to user
    const comment = await db.get(
      'SELECT user_id, deal_id FROM comments WHERE id = ?',
      [commentId]
    );
    
    if (!comment) {
      return res.status(404).json({ error: 'Comment not found' });
    }
    
    // Only allow the comment author or admin/moderator to update
    if (comment.user_id !== req.user.id && !req.user.isAdmin && !req.user.isModerator) {
      return res.status(403).json({ error: 'Not authorized to update this comment' });
    }
    
    // Update comment
    await db.run(
      'UPDATE comments SET text = ? WHERE id = ?',
      [text, commentId]
    );
    
    // Get the updated comment
    const updatedComment = await db.get(`
      SELECT 
        c.id, c.text, c.created_at, c.parent_id, c.deal_id,
        u.id as user_id, u.username
      FROM comments c
      JOIN users u ON c.user_id = u.id
      WHERE c.id = ?
    `, [commentId]);
    
    // Notify connected clients via Socket.io
    if (req.io) {
      req.io.emit('comment-updated', {
        dealId: updatedComment.deal_id,
        comment: updatedComment
      });
    }
    
    res.json(updatedComment);
    
  } catch (error) {
    console.error('Update comment error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

/**
 * Delete a comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function deleteComment(req, res) {
  try {
    const { commentId } = req.params;
    const db = await getDatabase();
    
    // Check if comment exists and belongs to user
    const comment = await db.get(
      'SELECT user_id, deal_id FROM comments WHERE id = ?',
      [commentId]
    );
    
    if (!comment) {
      return res.status(404).json({ error: 'Comment not found' });
    }
    
    // Only allow the comment author or admin/moderator to delete
    if (comment.user_id !== req.user.id && !req.user.isAdmin && !req.user.isModerator) {
      return res.status(403).json({ error: 'Not authorized to delete this comment' });
    }
    
    // Delete replies first
    await db.run('DELETE FROM comments WHERE parent_id = ?', [commentId]);
    
    // Delete the comment
    await db.run('DELETE FROM comments WHERE id = ?', [commentId]);
    
    // Notify connected clients via Socket.io
    if (req.io) {
      req.io.emit('comment-deleted', {
        dealId: comment.deal_id,
        commentId
      });
    }
    
    res.json({ message: 'Comment deleted successfully' });
    
  } catch (error) {
    console.error('Delete comment error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

/**
 * Get comments made by the current user with pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getUserComments(req, res) {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 20;
    const offset = (page - 1) * pageSize;
    
    const db = await getDatabase();
    
    // Get total count for pagination
    const countResult = await db.get(`
      SELECT COUNT(*) as total
      FROM comments
      WHERE user_id = ?
    `, [userId]);
    
    // Get comments with deal details
    const comments = await db.all(`
      SELECT 
        c.id, c.text, c.created_at, c.parent_id, c.deal_id,
        d.title as deal_title, d.price, d.original_price, d.image_url, d.thumbnail_url,
        d.user_id as deal_owner_id, d.category_id,
        u.username as commenter_username,
        du.username as deal_owner_username,
        cat.name as category_name
      FROM comments c
      JOIN deals d ON c.deal_id = d.id
      JOIN users u ON c.user_id = u.id
      JOIN users du ON d.user_id = du.id
      LEFT JOIN categories cat ON d.category_id = cat.id
      WHERE c.user_id = ?
      ORDER BY c.created_at DESC
      LIMIT ? OFFSET ?
    `, [userId, pageSize, offset]);
    
    res.json({
      data: comments,
      pagination: {
        total: countResult.total,
        totalPages: Math.ceil(countResult.total / pageSize),
        page,
        pageSize
      }
    });
  } catch (error) {
    console.error('Get user comments error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

module.exports = {
  getComments,
  addComment,
  updateComment,
  deleteComment,
  getUserComments
};
