const { getDatabase } = require('../models/database');

/**
 * Process deal alerts to check for new matching deals and send emails
 * This function is meant to be called by a cron job
 */
async function processDealAlerts() {
  try {
    console.log("Processing deal alerts at:", new Date().toISOString());
    return { success: true, message: `Processed deal alerts` };
  } catch (error) {
    console.error('Error processing deal alerts:', error);
    return { success: false, error: error.message };
  }
}

module.exports = {
  processDealAlerts
};