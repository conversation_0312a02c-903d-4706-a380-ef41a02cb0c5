const { getDatabase } = require('../models/database');
const { processImage } = require('../utils/imageProcessor');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs').promises;
const path = require('path');

/**
 * Get all deals with filtering and pagination
 */
async function getDeals(req, res) {
  try {
    const {
      page = 1,
      limit = parseInt(process.env.DEALS_PAGE_SIZE) || 30,
      sort = 'hot', // hot, new, price
      category,
      store,
      query,
      dealType,
      minPrice,
      maxPrice
    } = req.query;
    
    const offset = (page - 1) * limit;
    const db = await getDatabase();
    
    // Build query conditions
    let conditions = [];
    let params = [];
    
    if (category) {
      conditions.push('d.category_id = ?');
      params.push(category);
    }
    
    if (store) {
      conditions.push('d.store_id = ?');
      params.push(store);
    }
    
    if (query) {
      conditions.push('(d.title LIKE ? OR d.description LIKE ?)');
      params.push(`%${query}%`, `%${query}%`);
    }
    
    // Add price range filters
    if (minPrice) {
      conditions.push('COALESCE(d.price, 0) >= ?');
      params.push(minPrice);
    }
    
    if (maxPrice) {
      conditions.push('COALESCE(d.price, 0) <= ?');
      params.push(maxPrice);
    }
    
    conditions.push('d.status = ?');
    params.push('active');
    
    // Add dealType-specific conditions
    let havingClause = '';
    if (dealType) {
      switch (dealType) {
        case 'hot':
          havingClause = `HAVING temperature > ${process.env.GETTING_WARM_TEMPERATURE}`;
          break;
        case 'warm':
          havingClause = `HAVING temperature = ${process.env.GETTING_WARM_TEMPERATURE}`;
          break;
        case 'free':
          conditions.push('d.price = 0');
          break;
        case 'discounted':
          conditions.push('d.original_price > d.price AND d.price > 0');
          break;
      }
    }
    
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    // Determine sorting
    let orderBy;
    if (sort === 'newest') {
      orderBy = 'ORDER BY d.updated_at DESC';
    } else if (sort === 'price-asc') {
      orderBy = 'ORDER BY COALESCE(d.price, 0) ASC';
    } else if (sort === 'price-desc') {
      orderBy = 'ORDER BY COALESCE(d.price, 0) DESC';
    } else if (sort === 'most-commented') {
      orderBy = 'ORDER BY comment_count DESC, d.created_at DESC';
    } else if (sort === 'getting-warm') {
      orderBy = `HAVING temperature >= ${process.env.GETTING_WARM_TEMPERATURE} ORDER BY reached_hot_at DESC, temperature DESC`;
    } else if (sort === 'trending') {
      orderBy = 'ORDER BY recent_votes DESC, temperature DESC';
    } else {
      // Default 'hot' sorting - using votes and recency
      orderBy = 'ORDER BY temperature DESC, d.created_at DESC';
    }
    
    // If we have both a HAVING clause from dealType and one from orderBy, combine them
    if (havingClause && orderBy.startsWith('HAVING')) {
      const orderByParts = orderBy.split(' ORDER BY ');
      orderBy = `${havingClause} AND ${orderByParts[0].substring(7)} ORDER BY ${orderByParts[1]}`;
    } else if (havingClause) {
      orderBy = `${havingClause} ${orderBy}`;
    }
    
    // Count total deals matching criteria
    // Note: We need a more complex count query for deals with having clauses
    let countQuery;
    if (havingClause) {
      countQuery = `
        SELECT COUNT(*) as total
        FROM (
          SELECT 
            d.id, 
            COALESCE(SUM(v.vote_type), 0) as temperature
          FROM deals d
          LEFT JOIN votes v ON d.id = v.deal_id
          ${whereClause}
          GROUP BY d.id
          ${havingClause}
        ) subquery
      `;
    } else {
      countQuery = `
        SELECT COUNT(*) as total
        FROM deals d
        ${whereClause}
      `;
    }
    
    const countResult = await db.get(countQuery, [...params]);
    const total = countResult.total;
    
    // Get deals with vote counts and user info for dealsBrowse pge. Frontpage scroll deals are handled in getGettingWarmDeals or getTrendingDeals etc 
    const dealsQuery = `
      SELECT 
        d.*, 
        u.username as user_username,
        c.name as category_name,
        s.name as store_name,
        COALESCE(SUM(v.vote_type), 0) as temperature,
        COUNT(cm.id) as comment_count,
        (
          SELECT MAX(created_at) 
          FROM votes 
          WHERE deal_id = d.id 
          AND vote_type IN (1, -1)
          GROUP BY deal_id
          HAVING SUM(vote_type) >= ${process.env.GETTING_WARM_TEMPERATURE}
        ) as reached_hot_at,
        (
          SELECT COUNT(*) 
          FROM votes 
          WHERE deal_id = d.id 
          AND created_at >= datetime('now', '-24 hours')
        ) as recent_votes
      FROM deals d
      LEFT JOIN users u ON d.user_id = u.id
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      LEFT JOIN votes v ON d.id = v.deal_id
      LEFT JOIN comments cm ON d.id = cm.deal_id
      ${whereClause}
      GROUP BY d.id
      ${orderBy}
      LIMIT ? OFFSET ?
    `;
    
    const deals = await db.all(dealsQuery, [...params, limit, offset]);
    
    // Get available categories and stores for filters
    const categories = await db.all('SELECT DISTINCT id, name FROM categories WHERE id IS NOT NULL');
    const stores = await db.all('SELECT DISTINCT id, name FROM stores WHERE id IS NOT NULL');
    
    res.json({
      deals,
      totalCount: total,
      page: Number(page),
      pageSize: Number(limit),
      totalPages: Math.ceil(total / limit)
    });
    
  } catch (error) {
    console.error('Get deals error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

/**
 * Get a single deal by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getDealById(req, res) {
  try {
    const { id } = req.params;
    const { userId } = req.query; // Extract userId from query params as a backup auth mechanism
    console.log('Fetching deal by ID:', id, userId ? `with userId param: ${userId}` : '');
    
    // Debug authentication headers
    console.log('Auth debug - Authorization header:', req.headers.authorization);
    console.log('Auth debug - User object in request:', req.user);
    console.log('Auth debug - Query params:', req.query);
    
    const db = await getDatabase();
    
    // First - get the basic deal info regardless of status
    const dealCheck = await db.get(`
      SELECT id, status, user_id FROM deals WHERE id = ?
    `, [id]);
    
    console.log('Deal check result:', dealCheck);
    
    if (!dealCheck) {
      console.log(`Deal ${id} not found in database`);
      return res.status(404).json({ error: 'Deal not found' });
    }
    
    console.log(`Found deal ${dealCheck.id}: Status=${dealCheck.status}, Owner=${dealCheck.user_id}`);
    
    // Try to determine ownership using multiple methods
    // 1. Regular auth through req.user
    // 2. Backup auth through userId query param
    const isPending = dealCheck.status === 'pending';
    
    // Check if user is owner through multiple means
    const isOwnerViaUser = req.user && req.user.id === dealCheck.user_id;
    const isOwnerViaParam = userId && parseInt(userId) === dealCheck.user_id;
    const isOwner = isOwnerViaUser || isOwnerViaParam;
    
    console.log(`Deal is pending: ${isPending}, User is owner via user object: ${isOwnerViaUser}, User is owner via param: ${isOwnerViaParam}, Final ownership determination: ${isOwner}`);
    
    // If deal is pending and user is not the owner, return 404
    if (isPending && !isOwner) {
      console.log('User is not the owner of this pending deal - access denied');
      return res.status(404).json({ 
        error: 'No such active deal currently',
        isPendingDeal: true
      });
    }
    
    // At this point, either:
    // 1. The deal is active (not pending), or
    // 2. The deal is pending but the user is the owner
    // So we can fetch the full deal details
    
    const dealQuery = `
      SELECT 
        d.*, 
        u.username as user_username,
        c.name as category_name,
        s.name as store_name,
        COALESCE(SUM(v.vote_type), 0) as temperature,
        COUNT(DISTINCT cm.id) as comment_count,
        (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = 1) as upvotes,
        (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = -1) as downvotes
      FROM deals d
      LEFT JOIN users u ON d.user_id = u.id
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      LEFT JOIN votes v ON d.id = v.deal_id
      LEFT JOIN comments cm ON d.id = cm.deal_id
      WHERE d.id = ? AND (d.status = 'active' OR (d.status = 'pending' AND d.user_id = ?))
      GROUP BY d.id
    `;
    
    // Use determined userId for the query
    const userIdForQuery = req.user ? req.user.id : (userId ? parseInt(userId) : -1);
    console.log(`Using userIdForQuery: ${userIdForQuery} for detailed query`);
    
    const deal = await db.get(dealQuery, [id, userIdForQuery]);
    
    if (!deal) {
      console.log('Deal not found after detailed query');
      return res.status(404).json({ error: 'Deal not found' });
    }

    // Get user vote if authenticated
    let userVote = null;
    if (req.user) {
      const voteResult = await db.get(
        'SELECT vote_type FROM votes WHERE user_id = ? AND deal_id = ?',
        [req.user.id, id]
      );
      if (voteResult) {
        userVote = voteResult.vote_type;
      }
    }

    // Ensure temperature calculation is correct (should be upvotes - downvotes)
    const calculatedTemperature = (deal.upvotes || 0) - (deal.downvotes || 0);
    if (deal.temperature !== calculatedTemperature) {
      console.log(`Temperature inconsistency detected for deal ${deal.id}:`, {
        reportedTemperature: deal.temperature,
        calculatedTemperature,
        upvotes: deal.upvotes,
        downvotes: deal.downvotes
      });
      
      // Fix the temperature value
      deal.temperature = calculatedTemperature;
    }

    // Add userVote to response if available
    const response = {
      data: {
        ...deal,
        ...(userVote !== null && { userVote }),
        ...(isPending && { isPending: true })
      }
    };
    
    console.log('Successfully returning deal data');
    res.json(response);
  } catch (error) {
    console.error('Get deal by ID error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

/**
 * Create a new deal
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function createDeal(req, res) {
  try {
    const {
      title,
      description,
      url,
      price,
      original_price,
      store_id,
      category_id,
      image_url,
      thumbnail_url
    } = req.body;

    const db = await getDatabase();
    const result = await db.run(
      `INSERT INTO deals (
        title, description, url, price, original_price,
        store_id, category_id, user_id, image_url, thumbnail_url,
        created_at, updated_at, source
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, strftime('%Y-%m-%dT%H:%M:%fZ', 'now'), strftime('%Y-%m-%dT%H:%M:%fZ', 'now'), ?)`,
      [
        title,
        description,
        url,
        price,
        original_price,
        store_id,
        category_id,
        req.user.id,
        image_url,
        thumbnail_url,
        'manual'
      ]
    );

    const newDeal = await db.get('SELECT * FROM deals WHERE id = ?', [result.lastID]);
    res.status(201).json({ data: newDeal });
  } catch (error) {
    console.error('Error creating deal:', error);
    res.status(500).json({ error: 'Failed to create deal' });
  }
}

/**
 * Update an existing deal
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function updateDeal(req, res) {
  try {
    const { id } = req.params;
    const updates = req.body;
    const db = await getDatabase();

    // Check if deal exists and user has permission
    const deal = await db.get('SELECT * FROM deals WHERE id = ?', [id]);
    if (!deal) {
      return res.status(404).json({ error: 'Deal not found' });
    }

    if (deal.user_id !== req.user.id && !req.user.is_moderator) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    const updateFields = Object.keys(updates)
      .filter(key => updates[key] !== undefined)
      .map(key => `${key} = ?`)
      .join(', ');

    const updateValues = Object.keys(updates)
      .filter(key => updates[key] !== undefined)
      .map(key => updates[key]);

    await db.run(
      `UPDATE deals SET ${updateFields} WHERE id = ?`,
      [...updateValues, id]
    );

    const updatedDeal = await db.get('SELECT * FROM deals WHERE id = ?', [id]);
    res.json({ data: updatedDeal });
  } catch (error) {
    console.error('Error updating deal:', error);
    res.status(500).json({ error: 'Failed to update deal' });
  }
}

/**
 * Delete a deal and its associated data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function deleteDeal(req, res) {
  try {
    const { id } = req.params;
    const db = await getDatabase();

    // Check if deal exists and user has permission
    const deal = await db.get('SELECT * FROM deals WHERE id = ?', [id]);
    if (!deal) {
      return res.status(404).json({ error: 'Deal not found' });
    }

    // Only allow deletion by deal owner, moderator, or admin
    if (deal.user_id !== req.user.id && !req.user.is_moderator && !req.user.is_admin) {
      return res.status(403).json({ error: 'Unauthorized to delete this deal' });
    }

    await db.run('BEGIN TRANSACTION');
    try {
      // Backup the deal before deletion if it's not already in backup
      const backupExists = await db.get('SELECT id FROM deals_backup WHERE original_deal_id = ?', [id]);
      if (!backupExists) {
        await db.run(`
          INSERT INTO deals_backup 
          SELECT *, id as original_deal_id, datetime('now') as backup_date 
          FROM deals WHERE id = ?
        `, [id]);
      }

      // Delete associated files
      if (deal.image_url) {
        try {
          await fs.unlink(path.join(__dirname, '../../uploads', path.basename(deal.image_url)));
        } catch (err) {
          console.error('Error deleting image file:', err);
        }
      }
      if (deal.thumbnail_url) {
        try {
          await fs.unlink(path.join(__dirname, '../../uploads', path.basename(deal.thumbnail_url)));
        } catch (err) {
          console.error('Error deleting thumbnail file:', err);
        }
      }

      // Delete related data
      await db.run('DELETE FROM votes WHERE deal_id = ?', [id]);
      await db.run('DELETE FROM comments WHERE deal_id = ?', [id]);
      await db.run('DELETE FROM deals WHERE id = ?', [id]);
      
      await db.run('COMMIT');
      res.json({ 
        success: true,
        message: 'Deal and associated data deleted successfully' 
      });
    } catch (err) {
      await db.run('ROLLBACK');
      throw err;
    }
  } catch (error) {
    console.error('Delete deal error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Failed to delete deal',
      details: error.message 
    });
  }
}

/**
 * Upload and process a deal image
 * @param {Object} req - Express request object with file upload
 * @param {Object} res - Express response object
 */
async function uploadDealImage(req, res) {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No image file provided' });
    }

    const imageId = uuidv4();
    const { image_url, thumbnail_url } = await processImage(req.file.buffer, imageId);

    res.json({
      data: {
        image_url,
        thumbnail_url
      }
    });
  } catch (error) {
    console.error('Upload image error:', error);
    res.status(500).json({ error: 'Failed to upload image' });
  }
}

/**
 * Download and process an external image
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function localizeImage(req, res) {
  const db = await getDatabase();
  try {
    const { imageUrl, dealId } = req.body;
    
    if (!imageUrl) {
      return res.status(400).json({ error: 'Image URL is required' });
    }

    // If dealId is provided, verify deal exists
    if (dealId) {
      const deal = await db.get('SELECT id FROM deals WHERE id = ?', [dealId]);
      if (!deal) {
        return res.status(404).json({ error: `Deal ${dealId} not found` });
      }
    }

    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
    }

    const buffer = Buffer.from(await response.arrayBuffer());
    const imageId = uuidv4();
    
    const { image_url, thumbnail_url } = await processImage(buffer, imageId);

    // Update the database if dealId is provided
    if (dealId) {
      const updateResult = await db.run(
        'UPDATE deals SET image_url = ?, thumbnail_url = ? WHERE id = ?',
        [image_url, thumbnail_url, dealId]
      );

      if (updateResult.changes === 0) {
        throw new Error('Database update failed');
      }

      // Verify the update
      const updatedDeal = await db.get(
        'SELECT id, image_url, thumbnail_url FROM deals WHERE id = ?',
        [dealId]
      );

      if (!updatedDeal || !updatedDeal.image_url) {
        throw new Error('Failed to verify database update');
      }
    }

    res.json({
      data: {
        imageUrl: image_url,
        thumbnailUrl: thumbnail_url
      }
    });

  } catch (error) {
    console.error('Error localizing image:', error);
    res.status(500).json({ 
      error: 'Failed to localize image',
      details: error.message
    });
  }
}

/**
 * Get newest deals
 */
async function getNewestDeals(req, res) {
  try {
    const { limit = 10 } = req.query;
    const db = await getDatabase();
    
    const deals = await db.all(`
      SELECT 
        d.*, 
        u.username as user_username,
        c.name as category_name,
        s.name as store_name,
        COALESCE(SUM(v.vote_type), 0) as temperature,
        COUNT(DISTINCT cm.id) as comment_count
      FROM deals d
      LEFT JOIN users u ON d.user_id = u.id
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      LEFT JOIN votes v ON d.id = v.deal_id
      LEFT JOIN comments cm ON d.id = cm.deal_id
      WHERE d.status = 'active'
      GROUP BY d.id
      ORDER BY d.updated_at DESC
      LIMIT ?
    `, [limit]);

    res.json({ data: deals });
  } catch (error) {
    console.error('Get newest deals error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

/**
 * Get trending deals based on recent votes
 */
async function getTrendingDeals(req, res) {
  try {
    const { limit = 10, hours = 24 } = req.query;
    const db = await getDatabase();

    const timeAgo = new Date();
    timeAgo.setHours(timeAgo.getHours() - hours);
    const timeAgoStr = timeAgo.toISOString();

    const deals = await db.all(`
      SELECT 
        d.*, 
        u.username as user_username,
        c.name as category_name,
        s.name as store_name,
        COALESCE(SUM(v.vote_type), 0) as temperature,
        COUNT(cm.id) as comment_count,
        (
          SELECT COUNT(*) 
          FROM votes 
          WHERE deal_id = d.id 
          AND created_at >= ?
        ) as recent_votes
      FROM deals d
      LEFT JOIN users u ON d.user_id = u.id
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      LEFT JOIN votes v ON d.id = v.deal_id
      LEFT JOIN comments cm ON d.id = cm.deal_id
      WHERE d.status = 'active'
      GROUP BY d.id
      HAVING recent_votes > 0
      ORDER BY recent_votes DESC, temperature DESC
      LIMIT ?
    `, [timeAgoStr, limit]);

    res.json({ data: deals });
  } catch (error) {
    console.error('Get trending deals error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

// Get Getting Warm Deals
async function getGettingWarmDeals(req, res) {
  try {
    const { limit = 10 } = req.query;
    const db = await getDatabase();

    const deals = await db.all(`
      SELECT 
        d.*, 
        u.username as user_username,
        c.name as category_name,
        s.name as store_name,
        COALESCE(SUM(v.vote_type), 0) as temperature,
        COUNT(cm.id) as comment_count,
        (
          SELECT MAX(created_at) 
          FROM votes 
          WHERE deal_id = d.id 
          AND vote_type IN (1, -1)
          GROUP BY deal_id
          HAVING SUM(vote_type) >= ${process.env.GETTING_WARM_TEMPERATURE}
        ) as reached_hot_at
      FROM deals d
      LEFT JOIN users u ON d.user_id = u.id
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      LEFT JOIN votes v ON d.id = v.deal_id
      LEFT JOIN comments cm ON d.id = cm.deal_id
      WHERE d.status = 'active'
      GROUP BY d.id
      HAVING temperature >= ${process.env.GETTING_WARM_TEMPERATURE}
      ORDER BY reached_hot_at DESC, temperature DESC
      LIMIT ?
    `, [limit]);
    
    res.json({ data: deals });
  } catch (error) {
    console.error('Get trending by temperature error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

// Get most commented deals
async function getMostCommentedDeals(req, res) {
  try {
    const { limit = 10 } = req.query;
    const db = await getDatabase();
    
    // Get deals from last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const sevenDaysAgoStr = sevenDaysAgo.toISOString();

    const deals = await db.all(`
      SELECT 
        d.*, 
        u.username as user_username,
        c.name as category_name,
        s.name as store_name,
        COALESCE(SUM(v.vote_type), 0) as temperature,
        COUNT(cm.id) as comment_count
      FROM deals d
      LEFT JOIN users u ON d.user_id = u.id
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      LEFT JOIN votes v ON d.id = v.deal_id
      LEFT JOIN comments cm ON d.id = cm.deal_id
      WHERE cm.created_at >= ? AND d.status = 'active'
      GROUP BY d.id
      ORDER BY comment_count DESC
      LIMIT ?
    `, [sevenDaysAgoStr, limit]);
    
    res.json({ data: deals });
  } catch (error) {
    console.error('Get most commented deals error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

// Get deal for admin review
async function getAdminDeal(req, res) {
  try {
    const { id } = req.params;
    const db = await getDatabase();
    
    const deal = await db.get(`
      SELECT 
        d.*,
        u.username as user_username,
        u.email as user_email,
        c.name as category_name,
        s.name as store_name,
        COALESCE(SUM(v.vote_type), 0) as total_votes,
        COUNT(DISTINCT cm.id) as comment_count
      FROM deals d
      LEFT JOIN users u ON d.user_id = u.id
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      LEFT JOIN votes v ON d.id = v.deal_id
      LEFT JOIN comments cm ON d.id = cm.deal_id
      WHERE d.id = ?
      GROUP BY d.id
    `, [id]);

    if (!deal) {
      return res.status(404).json({ error: 'Deal not found' });
    }

    res.json({ data: deal });
  } catch (error) {
    console.error('Get admin deal error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

module.exports = {
  getDeals,
  getDealById,
  createDeal,
  updateDeal,
  deleteDeal,
  uploadDealImage,
  localizeImage,
  getNewestDeals,
  getTrendingDeals,
  getGettingWarmDeals,
  getMostCommentedDeals,
  getAdminDeal
};
