const { getDatabase } = require('../models/database');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

/**
 * Get all stores
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getStores(req, res) {
  try {
    const db = await getDatabase();
    const { category } = req.query;
    
    let query = `
      SELECT 
        s.id, 
        s.name, 
        s.logoUrl,
        s.url,
        COUNT(DISTINCT CASE WHEN d.id IS NOT NULL AND d.status = 'active'`;

    // If category is specified, only count deals in that category
    if (category) {
      query += ` AND d.category_id = ?`;
    }
    
    query += ` THEN d.id END) as dealsCount
      FROM stores s
      LEFT JOIN deals d ON s.id = d.store_id
      GROUP BY s.id
      ORDER BY s.name ASC
    `;
    
    const stores = await db.all(query, category ? [category] : []);
    
    res.status(200).json(stores);
  } catch (error) {
    console.error('Error getting stores:', error);
    res.status(500).json({ message: 'Error getting stores' });
  }
}

/**
 * Get a single store by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getStoreById(req, res) {
  try {
    const { id } = req.params;
    const db = await getDatabase();
    
    const store = await db.get(`
      SELECT 
        s.id, 
        s.name, 
        s.logoUrl,
        s.url,
        COUNT(d.id) as dealsCount
      FROM stores s
      LEFT JOIN deals d ON s.id = d.store_id
      WHERE s.id = ?
      GROUP BY s.id
    `, [id]);
    
    if (!store) {
      return res.status(404).json({ message: 'Store not found' });
    }
    
    res.status(200).json(store);
  } catch (error) {
    console.error('Error getting store:', error);
    res.status(500).json({ message: 'Error getting store' });
  }
}

/**
 * Create a new store (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function createStore(req, res) {
  try {
    const { name, url, logoUrl } = req.body;
    
    if (!name) {
      return res.status(400).json({ message: 'Store name is required' });
    }
    
    const db = await getDatabase();
    
    // Check if store already exists
    const existingStore = await db.get('SELECT id FROM stores WHERE name = ?', [name]);
    
    if (existingStore) {
      return res.status(400).json({ message: 'Store with this name already exists' });
    }
    
    // Insert new store
    const result = await db.run(
      'INSERT INTO stores (name, url, logoUrl) VALUES (?, ?, ?)',
      [name, url || null, logoUrl || null]
    );
    
    const newStore = await db.get('SELECT * FROM stores WHERE id = ?', [result.lastID]);
    
    res.status(201).json(newStore);
  } catch (error) {
    console.error('Error creating store:', error);
    res.status(500).json({ message: 'Error creating store' });
  }
}

/**
 * Upload a store logo
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function uploadStoreLogo(req, res) {
  try {
    const { id } = req.params;
    
    if (!req.file) {
      return res.status(400).json({ message: 'No logo file provided' });
    }
    
    const db = await getDatabase();
    
    // Check if store exists
    const [existingStores] = await db.query(
      'SELECT id, logoUrl FROM stores WHERE id = ?',
      [id]
    );
    
    if (existingStores.length === 0) {
      return res.status(404).json({ message: 'Store not found' });
    }
    
    // Delete old logo if exists
    const oldLogoUrl = existingStores[0].logoUrl;
    if (oldLogoUrl) {
      const oldLogoPath = path.join(__dirname, '../..', oldLogoUrl);
      if (fs.existsSync(oldLogoPath)) {
        fs.unlinkSync(oldLogoPath);
      }
    }
    
    // Generate a unique filename
    const fileExtension = path.extname(req.file.originalname);
    const fileName = `${uuidv4()}${fileExtension}`;
    
    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(__dirname, '../../uploads/stores');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    // Save the file
    const filePath = path.join(uploadsDir, fileName);
    fs.writeFileSync(filePath, req.file.buffer);
    
    // Update store with logo URL
    const logoUrl = `/uploads/stores/${fileName}`;
    await db.query(
      'UPDATE stores SET logoUrl = ? WHERE id = ?',
      [logoUrl, id]
    );
    
    res.status(200).json({ logoUrl });
  } catch (error) {
    console.error('Error uploading store logo:', error);
    res.status(500).json({ message: 'Error uploading logo' });
  }
}

/**
 * Update a store (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function updateStore(req, res) {
  try {
    const { id } = req.params;
    const { name, url } = req.body;
    
    if (!name && url === undefined) {
      return res.status(400).json({ message: 'At least one field must be provided' });
    }
    
    const db = await getDatabase();
    
    // Check if store exists
    const [existingStores] = await db.query(
      'SELECT id FROM stores WHERE id = ?',
      [id]
    );
    
    if (existingStores.length === 0) {
      return res.status(404).json({ message: 'Store not found' });
    }
    
    // Build update query
    let updateQuery = 'UPDATE stores SET ';
    const queryParams = [];
    
    if (name) {
      updateQuery += 'name = ?';
      queryParams.push(name);
      
      if (url !== undefined) {
        updateQuery += ', ';
      }
    }
    
    if (url !== undefined) {
      updateQuery += 'url = ?';
      queryParams.push(url || null);
    }
    
    updateQuery += ' WHERE id = ?';
    queryParams.push(id);
    
    await db.query(updateQuery, queryParams);
    
    // Get updated store
    const [updatedStores] = await db.query(
      `
      SELECT 
        s.id, 
        s.name, 
        s.logoUrl,
        s.url,
        COUNT(d.id) as dealsCount
      FROM stores s
      LEFT JOIN deals d ON s.id = d.store_id
      WHERE s.id = ?
      GROUP BY s.id
      `,
      [id]
    );
    
    res.status(200).json(updatedStores[0]);
  } catch (error) {
    console.error('Error updating store:', error);
    res.status(500).json({ message: 'Error updating store' });
  }
}

/**
 * Delete a store (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function deleteStore(req, res) {
  try {
    const { id } = req.params;
    const db = await getDatabase();
    
    // Check if store exists
    const [existingStores] = await db.query(
      'SELECT id, logoUrl FROM stores WHERE id = ?',
      [id]
    );
    
    if (existingStores.length === 0) {
      return res.status(404).json({ message: 'Store not found' });
    }
    
    // Check if store has deals
    const dealsCount = await db.get(
      'SELECT COUNT(*) as count FROM deals WHERE store_id = ?',
      [id]
    );
    
    if (dealsCount && dealsCount.count > 0) {
      return res.status(400).json({ 
        message: `Cannot delete store with ${dealsCount.count} associated deals`
      });
    }
    
    // Delete logo if exists
    const logoUrl = existingStores[0].logoUrl;
    if (logoUrl) {
      const logoPath = path.join(__dirname, '../..', logoUrl);
      if (fs.existsSync(logoPath)) {
        fs.unlinkSync(logoPath);
      }
    }
    
    // Delete store
    await db.query('DELETE FROM stores WHERE id = ?', [id]);
    
    res.status(200).json({ message: 'Store deleted successfully' });
  } catch (error) {
    console.error('Error deleting store:', error);
    res.status(500).json({ message: 'Error deleting store' });
  }
}

module.exports = {
  getStores,
  getStoreById,
  createStore,
  uploadStoreLogo,
  updateStore,
  deleteStore
};
