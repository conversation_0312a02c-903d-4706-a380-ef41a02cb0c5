const { getDatabase } = require('../models/database');

/**
 * Get deals created by the current authenticated user
 * @route GET /api/user/deals
 */
exports.getCurrentUserDeals = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Get database connection
    const db = await getDatabase();
    
    // Get user's deals including both active and pending deals
    const deals = await db.all(
      `SELECT d.*, 
        c.name as category_name,
        s.name as store_name,
        (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = 'up') as upvotes,
        (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = 'down') as downvotes,
        (SELECT COUNT(*) FROM comments WHERE deal_id = d.id) as comment_count
      FROM deals d 
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      WHERE d.user_id = ?
      ORDER BY d.created_at DESC`,
      [userId]
    );
    
    // Format the response data according to API schema
    const formattedDeals = deals.map(deal => {
      return {
        id: deal.id,
        title: deal.title,
        description: deal.description,
        price: deal.price,
        original_price: deal.original_price,
        store: deal.store,
        url: deal.url,
        image_url: deal.image_url,
        thumbnail_url: deal.thumbnail_url,
        status: deal.status,
        created_at: deal.created_at,
        expires_at: deal.expires_at,
        category: {
          id: deal.category_id,
          name: deal.category_name
        },
        store: {
          id: deal.store_id,
          name: deal.store_name
        },
        upvotes: deal.upvotes,
        downvotes: deal.downvotes,
        comment_count: deal.comment_count
      };
    });
    
    res.json({
      success: true,
      data: formattedDeals
    });
  } catch (error) {
    console.error('Error fetching current user deals:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching your deals',
      error: error.message
    });
  }
};
