const { validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const path = require('path');
const fs = require('fs');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const sharp = require('sharp');
const { getDatabase } = require('../models/database');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads/avatars');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const ext = path.extname(file.originalname);
    cb(null, `${uuidv4()}${ext}`); // Generate unique filename
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  // Accept only image files
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed'), false);
  }
};

// Initialize multer upload
const upload = multer({
  storage,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB max file size
  },
  fileFilter
}).single('avatar');

/**
 * Get user profile
 * @route GET /api/users/:userId/profile
 */
exports.getUserProfile = async (req, res) => {
  try {
    const { userId } = req.params;
    const db = await getDatabase();
    
    // Get user from database
    const user = await db.get(
      `SELECT id, username, email, is_admin, is_moderator, avatar, bio, created_at  
       FROM users 
       WHERE id = ?`,
      [userId]
    );
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Remove sensitive data
    delete user.password;
    
    // Get user stats
    const stats = await db.get(
      `SELECT 
        (SELECT COUNT(*) FROM deals WHERE user_id = ?) as totalDeals,
        (SELECT COUNT(*) FROM comments WHERE user_id = ?) as totalComments,
        (SELECT COUNT(*) FROM saved_deals WHERE user_id = ?) as savedDeals
      `,
      [userId, userId, userId]
    );
    
    res.json({
      ...user,
      stats
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update user profile
 * @route PUT /api/users/:userId/profile
 */
exports.updateUserProfile = async (req, res) => {
  try {
    const { userId } = req.params;
    const { username, email, currentPassword, newPassword, bio } = req.body;
    
    // Check if user exists
    const user = await db.get('SELECT * FROM users WHERE id = ?', [userId]);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Verify user is updating their own profile
    if (parseInt(userId) !== req.user.id) {
      return res.status(403).json({ message: 'Not authorized to update this profile' });
    }
    
    // Check if username is already taken by another user
    if (username !== user.username) {
      const existingUser = await db.get('SELECT id FROM users WHERE username = ?', [username]);
      if (existingUser) {
        return res.status(400).json({ message: 'Username is already taken' });
      }
    }
    
    // Check if email is already taken by another user
    if (email !== user.email) {
      const existingUser = await db.get('SELECT id FROM users WHERE email = ?', [email]);
      if (existingUser) {
        return res.status(400).json({ message: 'Email is already registered' });
      }
    }
    
    // Update user data
    let updatedFields = {
      username,
      email,
      bio: bio || null
    };
    
    // Handle password update if provided
    if (newPassword) {
      // Verify current password
      const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
      
      if (!isPasswordValid) {
        return res.status(400).json({ message: 'Current password is incorrect' });
      }
      
      // Hash new password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);
      
      updatedFields.password = hashedPassword;
    }
    
    // Build SQL query dynamically
    const fields = Object.keys(updatedFields);
    const placeholders = fields.map(field => `${field.replace(/([A-Z])/g, '_$1').toLowerCase()} = ?`).join(', ');
    const values = Object.values(updatedFields);
    
    await db.run(
      `UPDATE users SET ${placeholders} WHERE id = ?`,
      [...values, userId]
    );
    
    // Get updated user data
    const updatedUser = await db.get(
      `SELECT id, username, email, is_admin, is_moderator, avatar, bio, created_at as createdAt 
       FROM users 
       WHERE id = ?`,
      [userId]
    );
    
    res.json(updatedUser);
  } catch (error) {
    console.error('Error updating user profile:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Upload user avatar
 * @route POST /api/users/:userId/avatar
 */
exports.uploadAvatar = async (req, res) => {
  try {
    const { userId } = req.params;
    
    // Check if user exists
    const user = await db.get('SELECT * FROM users WHERE id = ?', [userId]);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Verify user is updating their own avatar
    if (parseInt(userId) !== req.user.id) {
      return res.status(403).json({ message: 'Not authorized to update this profile' });
    }
    
    // Handle file upload with multer
    upload(req, res, async function (err) {
      if (err instanceof multer.MulterError) {
        // A Multer error occurred
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({ message: 'File is too large. Maximum size is 2MB.' });
        }
        return res.status(400).json({ message: err.message });
      } else if (err) {
        // An unknown error occurred
        return res.status(400).json({ message: err.message });
      }
      
      // If no file was uploaded
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }
      
      // Delete old avatar if exists (except default avatar)
      if (user.avatar && !user.avatar.includes('default-avatar')) {
        const oldAvatarPath = path.join(__dirname, '../../uploads/avatars', path.basename(user.avatar));
        if (fs.existsSync(oldAvatarPath)) {
          fs.unlinkSync(oldAvatarPath);
        }
      }
      
      // Save avatar path to database
      const avatarUrl = `/uploads/avatars/${req.file.filename}`;
      
      await db.run(
        'UPDATE users SET avatar = ? WHERE id = ?',
        [avatarUrl, userId]
      );
      
      // Get updated user data
      const updatedUser = await db.get(
        `SELECT id, username, email, is_admin, is_moderator, avatar, bio, created_at as createdAt 
         FROM users 
         WHERE id = ?`,
        [userId]
      );
      
      res.json(updatedUser);
    });
  } catch (error) {
    console.error('Error uploading avatar:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get deals created by user
 * @route GET /api/users/:userId/deals
 */
exports.getUserDeals = async (req, res) => {
  try {
    const { userId } = req.params;
    
    // Check if user exists
    const user = await db.get('SELECT id FROM users WHERE id = ?', [userId]);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Get user's deals
    const deals = await db.all(
      `SELECT d.*, 
        u.username, 
        (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = 'up') as upvotes,
        (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = 'down') as downvotes,
        (SELECT COUNT(*) FROM comments WHERE deal_id = d.id) as comment_count
      FROM deals d 
      JOIN users u ON d.user_id = u.id
      WHERE d.user_id = ?
      ORDER BY d.created_at DESC`,
      [userId]
    );
    
    // Transform the deals to match frontend expectations
    const transformedDeals = deals.map(deal => ({
      id: deal.id,
      title: deal.title,
      description: deal.description,
      price: deal.price,
      originalPrice: deal.original_price,
      store: deal.store,
      url: deal.url,
      imageUrl: deal.image_url,
      category: deal.category,
      createdAt: deal.created_at,
      expiresAt: deal.expires_at,
      userId: deal.user_id,
      username: deal.username,
      upvotes: deal.upvotes,
      downvotes: deal.downvotes,
      commentCount: deal.comment_count
    }));
    
    res.json(transformedDeals);
  } catch (error) {
    console.error('Error fetching user deals:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get deals saved by user
 * @route GET /api/users/:userId/saved-deals
 */
exports.getUserSavedDeals = async (req, res) => {
  try {
    const { userId } = req.params;
    const db = await getDatabase();
    
    // Check if user exists
    const user = await db.get('SELECT id FROM users WHERE id = ?', [userId]);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Get user's saved deals
    const deals = await db.all(
      `SELECT d.*, 
        u.username, 
        (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = 'up') as upvotes,
        (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = 'down') as downvotes,
        (SELECT COUNT(*) FROM comments WHERE deal_id = d.id) as comment_count,
        c.name as category_name,
        s.name as store_name
      FROM deals d
      JOIN users u ON d.user_id = u.id
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      JOIN saved_deals sd ON d.id = sd.deal_id
      WHERE sd.user_id = ?
      ORDER BY sd.created_at DESC`,
      [userId]
    );
    
    res.json(deals);
  } catch (error) {
    console.error('Error fetching saved deals:', error);
    res.status(500).json({ message: 'Error fetching saved deals' });
  }
};
