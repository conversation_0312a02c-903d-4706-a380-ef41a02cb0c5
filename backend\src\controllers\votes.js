const { getDatabase } = require('../models/database');

/**
 * Vote on a deal (upvote or downvote)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function vote(req, res) {
  try {
    const { dealId, voteType } = req.body;
    
    if (!dealId) {
      return res.status(400).json({ error: 'Deal ID is required' });
    }
    
    if (voteType !== 1 && voteType !== -1 && voteType !== 0) {
      return res.status(400).json({ error: 'Vote type must be 1 (upvote), -1 (downvote), or 0 (remove vote)' });
    }
    
    const db = await getDatabase();
    
    // Check if deal exists
    const deal = await db.get('SELECT id FROM deals WHERE id = ?', [dealId]);
    if (!deal) {
      return res.status(404).json({ error: 'Deal not found' });
    }
    
    // Check if user has already voted
    const existingVote = await db.get(
      'SELECT vote_type FROM votes WHERE user_id = ? AND deal_id = ?',
      [req.user.id, dealId]
    );
    
    if (existingVote) {
      return res.status(400).json({ 
        error: 'You have already voted on this deal',
        existingVote: existingVote.vote_type 
      });
    }
    
    if (voteType === 0) {
      // Remove vote if it exists
      if (existingVote) {
        await db.run(
          'DELETE FROM votes WHERE user_id = ? AND deal_id = ?',
          [req.user.id, dealId]
        );
      }
    } else {
      // Insert new vote
      await db.run(
        'INSERT INTO votes (user_id, deal_id, vote_type) VALUES (?, ?, ?)',
        [req.user.id, dealId, voteType]
      );
    }
    
    // Get updated vote count
    const voteCount = await db.get(
      'SELECT COALESCE(SUM(vote_type), 0) as total FROM votes WHERE deal_id = ?',
      [dealId]
    );
    
    // Notify connected clients via Socket.io
    if (req.io) {
      req.io.emit('vote-update', {
        dealId,
        totalVotes: voteCount.total
      });
    }
    
    res.json({
      dealId,
      voteType: voteType === 0 ? null : voteType,
      totalVotes: voteCount.total
    });
    
  } catch (error) {
    console.error('Vote error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

/**
 * Get user's vote on a deal
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function getUserVote(req, res) {
  try {
    const { dealId } = req.params;
    
    const db = await getDatabase();
    
    // Get user's vote
    const vote = await db.get(
      'SELECT vote_type FROM votes WHERE user_id = ? AND deal_id = ?',
      [req.user.id, dealId]
    );
    
    res.json({
      dealId,
      voteType: vote ? vote.vote_type : null
    });
    
  } catch (error) {
    console.error('Get user vote error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

module.exports = {
  vote,
  getUserVote
};
