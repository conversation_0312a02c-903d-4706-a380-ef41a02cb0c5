const HotUKDealsScraper = require('./hukdScraper');

async function analyzeDealFields() {
  const scraper = new HotUKDealsScraper();
  const fieldStats = {
    totalDeals: 0,
    fields: {
      title: { present: 0, empty: 0, null: 0 },
      description: { present: 0, empty: 0, null: 0 },
      price: { present: 0, zero: 0, null: 0 },
      original_price: { present: 0, zero: 0, null: 0 },
      url: { present: 0, empty: 0, null: 0 },
      image: { present: 0, empty: 0, null: 0 },
      store_name: { present: 0, empty: 0, null: 0 },
      temperature: { present: 0, zero: 0, null: 0 },
      category_name: { present: 0, uncategorized: 0, null: 0 },
      hukd_id: { present: 0, zero: 0, null: 0 }
    }
  };

  try {
    const response = await fetch('https://www.hotukdeals.com/hot', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    });

    const html = await response.text();
    const threadDataRegex = /data-vue2='(\{"name":"ThreadMainListItemNormalizer","props":\{"thread":[^']+\}})'/g;
    
    let match;
    let foundValidDeal = false;

    while ((match = threadDataRegex.exec(html)) !== null && !foundValidDeal) {
      try {
        const jsonStr = match[1].replace(/&quot;/g, '"');
        const vueData = JSON.parse(jsonStr);
        const threadData = vueData.props.thread;
        
        // Skip deals without a link
        if (!threadData.link) {
          console.log(`Skipping deal without URL: "${threadData.title}"`);
          continue;
        }

        // Found a valid deal - let's analyze it as an example
        console.log('\nAnalyzing next valid deal:');
        console.log('Title:', threadData.title);
        console.log('URL:', threadData.link);
        console.log('Store:', threadData.merchant?.merchantName);
        console.log('Price:', threadData.price);
        console.log('Original Price:', threadData.nextBestPrice);
        console.log('Category:', threadData.mainGroup?.threadGroupName);

        // Add image URL logging
        const imageUrl = threadData.mainImage ? 
          `https://images.hotukdeals.com/${threadData.mainImage.path}/${threadData.mainImage.uid}` : 
          null;
        console.log('Image URL:', imageUrl);

        // Process the deal as before...
        const cleanUrl = threadData.link.split('?')[0];
        let storeName = threadData.merchant?.merchantName || 'unknown';
        if (!storeName || storeName === 'unknown') {
          try {
            const url = new URL(threadData.link);
            storeName = url.hostname.replace('www.', '').split('.')[0];
          } catch (urlError) {
            // Keep the merchant name or 'unknown' if URL parsing fails
          }
        }

        const deal = {
          title: threadData.title,
          description: threadData.description || '',
          price: parseFloat(threadData.price) || 0,
          original_price: parseFloat(threadData.nextBestPrice) || null,
          url: cleanUrl,
          image: threadData.mainImage ? 
            `https://images.hotukdeals.com/${threadData.mainImage.path}/${threadData.mainImage.uid}` : 
            null,
          store_name: storeName,
          temperature: parseFloat(threadData.temperature) || 0,
          category_name: threadData.mainGroup?.threadGroupName || 'Uncategorized',
          hukd_id: parseInt(threadData.threadId),
          created_at: new Date().toISOString()
        };

        // Only analyze one valid deal as an example
        fieldStats.totalDeals++;
        foundValidDeal = true;

        // Analyze fields for this deal
        Object.entries(deal).forEach(([field, value]) => {
          if (field === 'created_at') return;

          if (value === null) {
            fieldStats.fields[field].null++;
          } else if (value === '') {
            fieldStats.fields[field].empty++;
          } else if (typeof value === 'number' && value === 0) {
            fieldStats.fields[field].zero++;
          } else if (field === 'category_name' && value === 'Uncategorized') {
            fieldStats.fields[field].uncategorized++;
          } else {
            fieldStats.fields[field].present++;
          }
        });

      } catch (e) {
        console.error('Error processing deal:', e);
        continue;
      }
    }

    // Print results for the example deal
    console.log('\nExample Deal Analysis Results:');
    console.log('Fields Analysis:');
    Object.entries(fieldStats.fields).forEach(([field, stats]) => {
      console.log(`\n${field}:`);
      Object.entries(stats).forEach(([stat, count]) => {
        console.log(`  ${stat}: ${count}`);
      });
    });

  } catch (error) {
    console.error('Analysis failed:', error);
  }
}

// Run the analysis
analyzeDealFields();