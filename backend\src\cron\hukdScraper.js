const { getDatabase } = require('../models/database');
const path = require('path');
const fs = require('fs');
const { format } = require('date-fns');

class HotUKDealsScraper {
  constructor() {
    this.db = null;
    this.logDir = path.join(process.cwd(),  'scraper-data');
    this.startTime = new Date();
    this.totalDealsFound = 0;  // New counter for total deals
    this.dealsAdded = 0;
    this.skippedDeals = 0;
    this.SYSTEM_USER_ID = 1;
    
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  async initialize() {
    this.db = await getDatabase();
  }

  async run() {
    console.log('==========================================');
    console.log('Starting HUKD scraper at:', this.startTime.toISOString());
    console.log('==========================================');

    let logId;
    try {
      await this.initialize();

      // Insert initial log entry
      const result = await this.db.get(`
        INSERT INTO scrape_logs (source, timestamp, status, deals_found, deals_added)
        VALUES ('hotukdeals', datetime('now'), 'started', 0, 0)
        RETURNING id
      `);
      
      logId = result.id;

      // Fetch and process deals
      const deals = await this.fetchDeals();
      
      // Save detailed data to JSON file
      const timestamp = format(new Date(), 'yyyyMMdd-HHmmss');
      const dataFile = `hukd-${timestamp}.json`;
      const dataFilePath = path.join(this.logDir, dataFile);
      
      fs.writeFileSync(dataFilePath, JSON.stringify(deals, null, 2));

      // Update log with results
      await this.db.get(`
        UPDATE scrape_logs 
        SET status = 'completed',
            deals_found = ${this.totalDealsFound},
            deals_added = ${this.dealsAdded},
            data_file = '${dataFile}',
            timestamp = datetime('now')
        WHERE id = ${logId}
      `);

      console.log(`Scraping completed. Found: ${this.totalDealsFound}, Added: ${this.dealsAdded} + Skipped: ${this.skippedDeals}`);
      return { 
        success: true, 
        dealsFound: this.totalDealsFound, 
        dealsAdded: this.dealsAdded,
        dealsSkipped: this.skippedDeals 
      };

    } catch (error) {
      console.error('Scraper error:', error);
      
      // Update log with error if we have a database connection
      if (this.db && logId) {
        await this.db.get(`
          UPDATE scrape_logs 
          SET status = 'error',
              error = '${error.message.replace(/'/g, "''")}',
              timestamp = datetime('now')
          WHERE id = ${logId}
        `);
      }
      
      return { success: false, error: error.message };
    }
  }

  async fetchDeals() {
    console.log('Fetching HUKD hot deals page...');
    const response = await fetch('https://www.hotukdeals.com/hot', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const html = await response.text();
    console.log('Page fetched successfully');

    const threadDataRegex = /data-vue2='(\{"name":"ThreadMainListItemNormalizer","props":\{"thread":[^']+\}})'/g;
    const deals = [];
    let match;

    while ((match = threadDataRegex.exec(html)) !== null) {
      let currentDealData = null;
      
      try {
        const jsonStr = match[1].replace(/&quot;/g, '"');
        const vueData = JSON.parse(jsonStr);
        currentDealData = vueData.props.thread;
        
        // Increment total deals counter as soon as we find a deal
        this.totalDealsFound++;
        
        if (!currentDealData) {
          this.skippedDeals++;
          continue;
        }

        // Check for URL before processing
        if (!currentDealData.link) {
          console.log(`Skipping deal: "${currentDealData.title}" - No URL provided`);
          this.skippedDeals++;
          continue;
        }

        try {
          const url = new URL(currentDealData.link);
          const storeName = url.hostname.replace('www.', '').split('.')[0];
          const cleanUrl = currentDealData.link.split('?')[0];
          console.log(`Processing deal: ${currentDealData.title} with url of ${cleanUrl}`);
          
          const deal = {
            title: currentDealData.title,
            description: currentDealData.description || '',
            price: parseFloat(currentDealData.price) || 0,
            original_price: parseFloat(currentDealData.nextBestPrice) || null,
            url: cleanUrl,
            image_url: currentDealData.mainImage ? 
              `https://images.hotukdeals.com/${currentDealData.mainImage.path}/${currentDealData.mainImage.uid}` : 
              null,
            store_name: currentDealData.merchant?.merchantName || storeName,
            temperature: parseFloat(currentDealData.temperature) || 0,
            category_name: currentDealData.mainGroup?.threadGroupName || 'Uncategorized',
            hukd_id: parseInt(currentDealData.threadId),
            status: 'pending',
            created_at: new Date().toISOString()
          };

          deals.push(deal);
          this.dealsFound++;

          // Insert into database if it's a new deal
          // First, try to find or create the store
          const storeResult = await this.db.get(`
            INSERT INTO stores (name, url, logoUrl)
            SELECT 
              '${deal.store_name.replace(/'/g, "''")}', 
              '${deal.url}',
              NULL
            WHERE NOT EXISTS (
              SELECT 1 FROM stores WHERE LOWER(name) LIKE LOWER('${deal.store_name}') || '%'
            )
            RETURNING id;
          `);

          const storeId = storeResult?.id || await this.db.get(`
            SELECT id FROM stores WHERE LOWER(name) LIKE LOWER('${deal.store_name}') || '%'
          `).then(result => result?.id);

          // Then, try to find or create the category
          const categoryResult = await this.db.get(`
            INSERT INTO categories (name, slug)
            SELECT 
              '${deal.category_name.replace(/'/g, "''")}',
              '${deal.category_name.toLowerCase().replace(/[^a-z0-9]+/g, "-")}'
            WHERE NOT EXISTS (
              SELECT 1 FROM categories WHERE LOWER(name) LIKE LOWER('${deal.category_name}') || '%'
            )
            RETURNING id;
          `);

          const categoryId = categoryResult?.id || await this.db.get(`
            SELECT id FROM categories WHERE LOWER(name) LIKE LOWER('${deal.category_name}') || '%'
          `).then(result => result?.id);

          // Now use the IDs in the deals insert
          const insertResult = await this.db.get(`
            INSERT INTO deals 
            (user_id, title, description, price, original_price, store_id, 
             category_id, url, image_url, created_at, updated_at, source, external_id, status)
            SELECT 
              ${this.SYSTEM_USER_ID},
              '${deal.title.replace(/'/g, "''")}',
              '${deal.description.replace(/'/g, "''")}',
              ${deal.price},
              ${deal.original_price || 'NULL'},
              ${storeId},
              ${categoryId},
              '${deal.url}',
              ${deal.image_url ? `'${deal.image_url}'` : 'NULL'},
              strftime('%Y-%m-%dT%H:%M:%fZ', 'now'),
              strftime('%Y-%m-%dT%H:%M:%fZ', 'now'),
              'hukd',
              '${deal.hukd_id}',
              '${deal.status}'
            WHERE NOT EXISTS (
              SELECT 1 FROM deals WHERE external_id = '${deal.hukd_id}'
            )
            RETURNING id
          `);

          if (insertResult && insertResult.id) {
            this.dealsAdded++;
            console.log(`Added new deal: ${deal.title}`);
          }

        } catch (urlError) {
          console.log(`Skipping deal: "${currentDealData.title}" - Invalid URL format`);
          this.skippedDeals++;
          continue;
        }

      } catch (e) {
        this.skippedDeals++;
        console.log('Skipping deal: Invalid deal data');
      }
    }

    return deals;
  }
}

module.exports = HotUKDealsScraper;