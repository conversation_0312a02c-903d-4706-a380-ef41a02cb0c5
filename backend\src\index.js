require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { rateLimit } = require('express-rate-limit');
const compression = require('compression');
const http = require('http');
const socketIo = require('socket.io');
const cron = require('node-cron');
const path = require('path');

const { initializeDatabase } = require('./models/database');
const authRoutes = require('./routes/auth');
const dealRoutes = require('./routes/deals');
const voteRoutes = require('./routes/votes');
const commentRoutes = require('./routes/comments');
//const scraperRoutes = require('./routes/scrapers');
const userRoutes = require('./routes/users');
const currentUserRoutes = require('./routes/userDeals');
const categoryRoutes = require('./routes/categories');
const storeRoutes = require('./routes/stores');
const dealAlertRoutes = require('./routes/dealAlerts');
const adminRoutes = require('./routes/admin');
const aiRoutes = require('./routes/ai');
const { processDealAlerts } = require('./controllers/dealAlerts');
const { initTransporter } = require('./utils/emailService');
const { runHotUKDealsScraper } = require('./cron/hukdScraper');

// Initialize database once at startup
let dbInstance = null;

async function initializeApp() {
  try {
    // Initialize database once
    if (!dbInstance) {
      dbInstance = await initializeDatabase();
      console.log('Database initialized successfully');
    }
    
    // Initialize Express app
    const app = express();
    const server = http.createServer(app);
    const io = socketIo(server, {
      cors: {
        origin: [process.env.FRONTEND_URL, 'https://www.nicedeals.app', 'http://localhost:3010', 'http://localhost:3000'],
        methods: ['GET', 'POST']
      }
    });

    // Trust proxy - needed for correct IP detection when behind a proxy
    app.set('trust proxy', 1);

    // Middleware
    app.use(express.json());
    app.use(cors({
      origin: [process.env.FRONTEND_URL, 'https://www.nicedeals.app', 'http://localhost:3010', 'http://localhost:3000']
    }));
    app.use(
      helmet({
        crossOriginResourcePolicy: { policy: "cross-origin" },
        crossOriginEmbedderPolicy: false
      })
    );
    app.use(compression());
    app.use(morgan('dev'));

    // Rate limiting
    const apiLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 500, // Increased from 100 to 500 requests per windowMs
      standardHeaders: true,
      legacyHeaders: false,
      message: {
        status: 429,
        error: 'Too many requests',
        message: 'You have exceeded the rate limit. Please try again later.',
        limitWindowInMinutes: 15
      }
    });
    app.use(apiLimiter);

    // Make io available in route handlers
    app.use((req, res, next) => {
      req.io = io;
      next();
    });

    // Routes
    app.use('/api/auth', authRoutes);
    app.use('/api/deals', dealRoutes);
    app.use('/api/votes', voteRoutes);
    app.use('/api/comments', commentRoutes);
    //app.use('/api/scraper', scraperRoutes);
    app.use('/api/users', userRoutes);
    app.use('/api/user', currentUserRoutes);
    app.use('/api/categories', categoryRoutes);
    app.use('/api/stores', storeRoutes);
    app.use('/api/alerts', dealAlertRoutes);
    app.use('/api/admin', adminRoutes);
    app.use('/api/ai', aiRoutes);

    // Serve static files from uploads directory with CORS headers
    app.use('/uploads', (req, res, next) => {
      res.header('Access-Control-Allow-Origin', [process.env.FRONTEND_URL, 'https://www.nicedeals.app', 'http://localhost:3010', 'http://localhost:3000']);
      res.header('Cross-Origin-Resource-Policy', 'cross-origin');
      next();
    }, express.static(path.join(__dirname, '../public/uploads')));

    // Add this middleware to log static file requests
    app.use('/uploads', (req, res, next) => {
      console.log('Static file request:', req.url);
      console.log('Full path:', path.join(__dirname, '../public/uploads', req.url));
      next();
    });

    // Health check route
    app.get('/api/health', (req, res) => {
      res.status(200).json({ status: 'OK', timestamp: new Date() });
    });

    // Initialize email service
    initTransporter().catch(err => console.error('Failed to initialize email service:', err));

    // Socket.io connection
    io.on('connection', (socket) => {
      console.log('New client connected');
      
      socket.on('disconnect', () => {
        console.log('Client disconnected');
      });
    });

    // // Schedule scraper to run every day at midnight
    // cron.schedule('0 0 * * *', async () => {
    //   console.log('Running scheduled scraper job');
    //   try {
    //     await runHotUKDealsScraper();
    //     console.log('Scraper job completed successfully');
    //   } catch (error) {
    //     console.error('Scraper job failed:', error);
    //   }
    // });

    // // Schedule deal alerts processing (daily at 8:00 AM)
    // cron.schedule('0 8 * * *', async () => {
    //   console.log('Running scheduled deal alerts processing');
    //   try {
    //     const result = await processDealAlerts();
    //     console.log('Deal alerts processing result:', result);
    //   } catch (error) {
    //     console.error('Error in scheduled deal alerts processing:', error);
    //   }
    // });

    // Error handling middleware
    app.use((err, req, res, next) => {
      console.error(err.stack);
      res.status(500).json({
        error: 'Server error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'An unexpected error occurred'
      });
    });

    // Start server
    const PORT = process.env.PORT || 5010;
    server.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
    });
  } catch (error) {
    console.error('Error initializing app:', error);
    process.exit(1);
  }
}

initializeApp();

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
