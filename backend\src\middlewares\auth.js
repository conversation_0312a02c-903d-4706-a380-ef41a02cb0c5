const jwt = require('jsonwebtoken');
const { getDatabase } = require('../models/database');

/**
 * Authentication middleware to verify JWT token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function authMiddleware(req, res, next) {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Authorization token required' });
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify token
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'dev_secret');
      
      // Check if user still exists
      const db = await getDatabase();
      const user = await db.get('SELECT id FROM users WHERE id = ?', [decoded.id]);
      
      if (!user) {
        return res.status(401).json({ error: 'User not found' });
      }
      
      // Add user info to request
      req.user = decoded;
      next();
      
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({ error: 'Token expired' });
      }
      
      return res.status(401).json({ error: 'Invalid token' });
    }
    
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({ error: 'Server error' });
  }
}

/**
 * Admin role middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function adminMiddleware(req, res, next) {
  // Check if user is authenticated and has ID 1 or 2 (admin access)
  if (!req.user || (req.user.id !== 1 && req.user.id !== 2)) {
    return res.status(403).json({ error: 'Admin access required' });
  }
  
  next();
}

/**
 * Moderator role middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function moderatorMiddleware(req, res, next) {
  if (!req.user || (!req.user.isAdmin && !req.user.isModerator)) {
    return res.status(403).json({ error: 'Moderator access required' });
  }
  
  next();
}

module.exports = {
  authMiddleware,
  adminMiddleware,
  moderatorMiddleware,
  authenticate: authMiddleware
};
