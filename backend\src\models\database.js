const sqlite3 = require('sqlite3').verbose();
const { open } = require('sqlite');
const path = require('path');

// Database file path
const dbPath = path.resolve(__dirname, '../../database.sqlite');

// Default data for database
const categories = [
  { name: 'Electronics', slug: 'electronics' },
  { name: 'Computers', slug: 'computers' },
  { name: 'Gaming', slug: 'gaming' },
  { name: 'Home & Garden', slug: 'home-garden' },
  { name: 'Clothing & Accessories', slug: 'clothing-accessories' },
  { name: 'Beauty & Health', slug: 'beauty-health' },
  { name: 'Toys & Kids', slug: 'toys-kids' },
  { name: 'Books & Media', slug: 'books-media' },
  { name: 'Food & Drink', slug: 'food-drink' },
  { name: 'Travel', slug: 'travel' },
  { name: 'Sports & Outdoors', slug: 'sports-outdoors' },
  { name: 'Automotive', slug: 'automotive' },
  { name: 'Services', slug: 'services' },
  { name: 'Entertainment', slug: 'entertainment' },
  { name: 'Home Improvement', slug: 'home-improvement' },
  { name: 'Office Supplies', slug: 'office-supplies' },
  { name: 'Other', slug: 'other' }
];

const stores = [
  { name: 'Unknown', url: '', logoUrl: '/uploads/stores/unknown.png' },
  { name: 'adidas', url: 'https://www.adidas.co.uk', logoUrl: '/uploads/stores/adidas.png' },
  { name: 'AO.com', url: 'https://ao.com', logoUrl: '/uploads/stores/ao.png' },
  { name: 'Argos', url: 'https://www.argos.co.uk', logoUrl: '/uploads/stores/argos.png' },
  { name: 'Birmingham Airport Parking', url: 'https://www.birminghamairport.co.uk', logoUrl: '/uploads/stores/birmingham-airport.png' },
  { name: 'Boden', url: 'https://www.boden.co.uk', logoUrl: '/uploads/stores/boden.png' },
  { name: 'boohooMAN', url: 'https://www.boohooman.com', logoUrl: '/uploads/stores/boohooman.png' },
  { name: 'Boots', url: 'https://www.boots.com', logoUrl: '/uploads/stores/boots.png' },
  { name: 'Charles Tyrwhitt', url: 'https://www.charlestyrwhitt.com', logoUrl: '/uploads/stores/charles-tyrwhitt.png' },
  { name: 'Currys', url: 'https://www.currys.co.uk', logoUrl: '/uploads/stores/currys.png' },
  { name: 'Debenhams', url: 'https://www.debenhams.com', logoUrl: '/uploads/stores/debenhams.png' },
  { name: 'easyJet Flights', url: 'https://www.easyjet.com', logoUrl: '/uploads/stores/easyjet.png' },
  { name: 'easyJet holidays', url: 'https://holidays.easyjet.com', logoUrl: '/uploads/stores/easyjet-holidays.png' },
  { name: 'Etsy', url: 'https://www.etsy.com', logoUrl: '/uploads/stores/etsy.png' },
  { name: 'Euro Car Parts', url: 'https://www.eurocarparts.com', logoUrl: '/uploads/stores/euro-car-parts.png' },
  { name: 'Expedia', url: 'https://www.expedia.co.uk', logoUrl: '/uploads/stores/expedia.png' },
  { name: 'FARFETCH', url: 'https://www.farfetch.com', logoUrl: '/uploads/stores/farfetch.png' },
  { name: 'First Choice', url: 'https://www.firstchoice.co.uk', logoUrl: '/uploads/stores/first-choice.png' },
  { name: 'FRASERS', url: 'https://www.frasers.group', logoUrl: '/uploads/stores/frasers.png' },
  { name: 'George at ASDA', url: 'https://direct.asda.com/george', logoUrl: '/uploads/stores/george-asda.png' },
  { name: 'H&M', url: 'https://www2.hm.com', logoUrl: '/uploads/stores/hm.png' },
  { name: 'Halfords', url: 'https://www.halfords.com', logoUrl: '/uploads/stores/halfords.png' },
  { name: 'Iceland', url: 'https://www.iceland.co.uk', logoUrl: '/uploads/stores/iceland.png' },
  { name: 'JD Sports', url: 'https://www.jdsports.co.uk', logoUrl: '/uploads/stores/jd-sports.png' },
  { name: 'John Lewis & Partners', url: 'https://www.johnlewis.com', logoUrl: '/uploads/stores/john-lewis.png' },
  { name: 'Marks and Spencer', url: 'https://www.marksandspencer.com', logoUrl: '/uploads/stores/marks-spencer.png' },
  { name: 'Matalan', url: 'https://www.matalan.co.uk', logoUrl: '/uploads/stores/matalan.png' },
  { name: 'Moonpig', url: 'https://www.moonpig.com', logoUrl: '/uploads/stores/moonpig.png' },
  { name: 'Morrisons', url: 'https://www.morrisons.com', logoUrl: '/uploads/stores/morrisons.png' },
  { name: 'Myprotein', url: 'https://www.myprotein.com', logoUrl: '/uploads/stores/myprotein.png' },
  { name: 'Railcard', url: 'https://www.railcard.co.uk', logoUrl: '/uploads/stores/railcard.png' },
  { name: 'River Island', url: 'https://www.riverisland.com', logoUrl: '/uploads/stores/river-island.png' },
  { name: 'Samsung', url: 'https://www.samsung.com/uk', logoUrl: '/uploads/stores/samsung.png' },
  { name: 'schuh', url: 'https://www.schuh.co.uk', logoUrl: '/uploads/stores/schuh.png' },
  { name: 'Sephora', url: 'https://www.sephora.co.uk', logoUrl: '/uploads/stores/sephora.png' },
  { name: 'SHEIN', url: 'https://www.shein.co.uk', logoUrl: '/uploads/stores/shein.png' },
  { name: 'Simba', url: 'https://simbasleep.com', logoUrl: '/uploads/stores/simba.png' },
  { name: 'Sports Direct', url: 'https://www.sportsdirect.com', logoUrl: '/uploads/stores/sports-direct.png' },
  { name: 'VistaPrint', url: 'https://www.vistaprint.co.uk', logoUrl: '/uploads/stores/vistaprint.png' },
  { name: 'Wayfair', url: 'https://www.wayfair.co.uk', logoUrl: '/uploads/stores/wayfair.png' },
  { name: 'loveholidays', url: 'https://www.loveholidays.com', logoUrl: '/uploads/stores/loveholidays.png' },
  { name: 'Amazon', url: 'https://www.amazon.co.uk', logoUrl: '/uploads/stores/amazon.png' },
  { name: 'eBay', url: 'https://www.ebay.co.uk', logoUrl: '/uploads/stores/ebay.png' },
  { name: 'GAME', url: 'https://www.game.co.uk', logoUrl: '/uploads/stores/game.png' },
  { name: 'Vodafone', url: 'https://www.vodafone.co.uk', logoUrl: '/uploads/stores/vodafone.png' },
  { name: 'Virgin Media', url: 'https://www.virginmedia.com', logoUrl: '/uploads/stores/virgin-media.png' }
];

// Get database connection
async function getDatabase() {
  return open({
    filename: dbPath,
    driver: sqlite3.Database
  });
}

// Migrate timestamps to ISO format
async function migrateToISOTimestamps(db) {
  try {
    await db.run('BEGIN TRANSACTION');

    // Update existing timestamps in deals table
    const deals = await db.all('SELECT id, created_at, updated_at, expires_at FROM deals');
    for (const deal of deals) {
      const updates = [];
      const params = [];

      if (deal.created_at && !deal.created_at.endsWith('Z')) {
        updates.push('created_at = ?');
        params.push(new Date(deal.created_at).toISOString());
      }
      
      if (deal.updated_at && !deal.updated_at.endsWith('Z')) {
        updates.push('updated_at = ?');
        params.push(new Date(deal.updated_at).toISOString());
      }
      
      if (deal.expires_at && !deal.expires_at.endsWith('Z')) {
        updates.push('expires_at = ?');
        params.push(new Date(deal.expires_at).toISOString());
      }

      if (updates.length > 0) {
        params.push(deal.id);
        await db.run(
          `UPDATE deals SET ${updates.join(', ')} WHERE id = ?`,
          params
        );
      }
    }

    // Modify the deals table to use ISO timestamps for new records
    await db.run(`DROP TRIGGER IF EXISTS set_deal_timestamp`);
    await db.run(`
      CREATE TRIGGER set_deal_timestamp
      AFTER INSERT ON deals
      FOR EACH ROW
      WHEN NEW.created_at IS NULL
      BEGIN
        UPDATE deals
        SET created_at = (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
            updated_at = (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
        WHERE id = NEW.id;
      END;
    `);

    await db.run('COMMIT');
    console.log('Successfully migrated timestamps to ISO format');
    
  } catch (error) {
    await db.run('ROLLBACK');
    console.error('Error migrating timestamps:', error);
    throw error;
  }
}

// Initialize database with tables
async function initializeDatabase() {
  const db = await getDatabase();
  
  // Enable foreign keys
  await db.exec('PRAGMA foreign_keys = ON');

  // Log initialization start
  console.log('Initializing database...');
  
  // Create tables
  await db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      avatar TEXT,
      bio TEXT,
      created_at TEXT DEFAULT (datetime('now')),
      is_admin INTEGER DEFAULT 0,
      is_moderator INTEGER DEFAULT 0,
      email_verified INTEGER DEFAULT 0
    );
    
    CREATE TABLE IF NOT EXISTS deals (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      title TEXT NOT NULL,
      description TEXT NOT NULL,
      price REAL NOT NULL,
      original_price REAL,
      store_id INTEGER NOT NULL,
      category_id INTEGER NOT NULL,
      url TEXT NOT NULL,
      image_url TEXT NOT NULL,
      thumbnail_url TEXT,
      coupon TEXT,
      created_at TEXT DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
      source TEXT DEFAULT 'manual',
      external_id TEXT,
      expires_at TEXT,
      status TEXT DEFAULT 'pending',
      updated_at TEXT DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
      FOREIGN KEY (user_id) REFERENCES users (id),
      FOREIGN KEY (category_id) REFERENCES categories (id),
      FOREIGN KEY (store_id) REFERENCES stores (id)
    );

    CREATE TABLE IF NOT EXISTS deals_backup (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      original_deal_id INTEGER NOT NULL,
      title TEXT NOT NULL,
      description TEXT,
      url TEXT,
      image_url TEXT,
      thumbnail_url TEXT,
      price REAL,
      original_price REAL,
      category_id INTEGER,
      store_id INTEGER,
      user_id INTEGER,
      status TEXT,
      upvotes INTEGER DEFAULT 0,
      downvotes INTEGER DEFAULT 0,
      coupon TEXT,
      created_at TEXT DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
      updated_at TEXT DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
      expires_at TEXT,
      FOREIGN KEY (original_deal_id) REFERENCES deals (id) ON DELETE CASCADE
    );
    
    CREATE TABLE IF NOT EXISTS votes (
      user_id INTEGER,
      deal_id INTEGER,
      vote_type INTEGER,
      created_at TEXT DEFAULT (datetime('now')),
      PRIMARY KEY (user_id, deal_id),
      FOREIGN KEY (user_id) REFERENCES users (id),
      FOREIGN KEY (deal_id) REFERENCES deals (id)
    );
    
    CREATE TABLE IF NOT EXISTS comments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      deal_id INTEGER,
      parent_id INTEGER,
      text TEXT NOT NULL,
      created_at TEXT DEFAULT (datetime('now')),
      FOREIGN KEY (user_id) REFERENCES users (id),
      FOREIGN KEY (deal_id) REFERENCES deals (id),
      FOREIGN KEY (parent_id) REFERENCES comments (id)
    );
    
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      slug TEXT UNIQUE NOT NULL
    );
    
    CREATE TABLE IF NOT EXISTS stores (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      url TEXT,
      logoUrl TEXT
    );
    
    CREATE TABLE IF NOT EXISTS deal_alerts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      keyword TEXT NOT NULL,
      created_at TEXT DEFAULT (datetime('now')),
      last_sent_at TEXT,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
      UNIQUE(user_id, keyword)
    );

    CREATE TABLE IF NOT EXISTS scrape_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      source TEXT NOT NULL,
      timestamp TEXT DEFAULT (datetime('now')),
      status TEXT,
      deals_found INTEGER DEFAULT 0,
      deals_added INTEGER DEFAULT 0,
      error TEXT,
      data_file TEXT,
      diagnostics TEXT
    );

    CREATE TABLE IF NOT EXISTS saved_deals (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      deal_id INTEGER NOT NULL,
      created_at TEXT DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
      FOREIGN KEY (deal_id) REFERENCES deals (id) ON DELETE CASCADE,
      UNIQUE(user_id, deal_id)
    );
  `);
  
  // Create indexes for performance
  await db.exec(`
    CREATE INDEX IF NOT EXISTS idx_deals_user_id ON deals (user_id);
    CREATE INDEX IF NOT EXISTS idx_deals_created_at ON deals (created_at);
    CREATE INDEX IF NOT EXISTS idx_deals_store_id ON deals (store_id);
    CREATE INDEX IF NOT EXISTS idx_deals_category_id ON deals (category_id);
    CREATE INDEX IF NOT EXISTS idx_votes_deal_id ON votes (deal_id);
    CREATE INDEX IF NOT EXISTS idx_comments_deal_id ON comments (deal_id);
    CREATE INDEX IF NOT EXISTS idx_deals_backup_original_deal_id ON deals_backup (original_deal_id);
    CREATE INDEX IF NOT EXISTS idx_saved_deals_user_id ON saved_deals (user_id);
    CREATE INDEX IF NOT EXISTS idx_saved_deals_deal_id ON saved_deals (deal_id);
  `);
  
  // Add initial system user if doesn't exist yet
  const systemUser = await db.get('SELECT id FROM users WHERE username = ?', ['system']);
  if (!systemUser) {
    await db.run(
      'INSERT OR IGNORE INTO users (username, email, password_hash, is_admin) VALUES (?, ?, ?, ?)',
      ['Boss', '<EMAIL>', 'SYSTEM_USER_NO_LOGIN', 1]
    );
  }
  
  // Add default categories
  for (const category of categories) {
    await db.run(
      'INSERT OR IGNORE INTO categories (name, slug) VALUES (?, ?)',
      [category.name, category.slug]
    );
  }
  
  // Add default stores
  for (const store of stores) {
    await db.run(
      'INSERT OR IGNORE INTO stores (name, url, logoUrl) VALUES (?, ?, ?)',
      [store.name, store.url, store.logoUrl]
    );
  }
  
  // Run migrations
  console.log('Running migrations...');
  try {
    await migrateToISOTimestamps(db);
    console.log('Migrations completed successfully');
  } catch (error) {
    console.error('Error during migrations:', error);
    throw error;
  }

  console.log('Database initialization completed');
  return db;
}

// Export database functions
module.exports = {
  getDatabase,
  initializeDatabase,
  categories,
  stores
};
