const express = require('express');
const { authMiddleware, adminMiddleware } = require('../middlewares/auth');
const { getDatabase } = require('../models/database');
const router = express.Router();
const { runHotUKDealsScraper } = require('../controllers/hotukdeals');
const { localizeImage } = require('../controllers/deals');
const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const axios = require('axios');
const sharp = require('sharp');
const { v4: uuidv4 } = require('uuid');

const DEBUGLOG = 0; // Debug flag: Set to 1 to enable console logging, 0 to disable
const DEBUGERROR = 1;
// Debug logging helper
const debugLog = (...args) => {
  if (DEBUGLOG > 0) {
    console.log(...args);
  }
};

// Debug error logging helper
const debugError = (...args) => {
  if (DEBUGERROR > 0) {
    console.error(...args);
  }
};

// Import OpenRouter API key from ai.js
const OPENROUTER_API_KEY = '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';

// Helper function to normalize paths across OS
const normalizePath = (filePath) => {
  // Remove leading slash or backslash if present
  filePath = filePath.replace(/^[/\\]+/, '');
  // Convert URL path to filesystem path
  return path.normalize(filePath);
};

// Get all users (admin only)
router.get('/users', authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const db = await getDatabase();
    const users = await db.all('SELECT * FROM users');
    
    return res.json({
      success: true,
      data: { users }
    });
  } catch (error) {
    debugError('Error fetching users:', error);  
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch users'
    });
  }
});

// Get all categories (admin only)
router.get('/categories', authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const db = await getDatabase();
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20; // Default limit
    const offset = (page - 1) * limit;

    // Query to get categories with deal counts and pagination
    const categoriesQuery = `
      SELECT c.id, c.name, c.slug, COUNT(d.id) as dealsCount
      FROM categories c
      LEFT JOIN deals d ON c.id = d.category_id
      GROUP BY c.id, c.name, c.slug
      ORDER BY c.name ASC
      LIMIT ? OFFSET ?
    `;
    const categories = await db.all(categoriesQuery, [limit, offset]);

    // Query to get total count of categories for pagination
    const totalCategoriesResult = await db.get('SELECT COUNT(*) as total FROM categories');
    const totalCategories = totalCategoriesResult.total;
    const totalPages = Math.ceil(totalCategories / limit);
    
    return res.json({
      success: true,
      data: {
        categories,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalCategories,
          itemsPerPage: limit
        }
      }
    });
  } catch (error) {
    debugError('Error fetching categories:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch categories'
    });
  }
});

// Create a new category (admin only)
router.post('/categories', authMiddleware, adminMiddleware, async (req, res) => {
  const { name, slug } = req.body;
  if (!name || !slug) {
    return res.status(400).json({ success: false, error: 'Name and slug are required' });
  }
  try {
    const db = await getDatabase();
    const result = await db.run('INSERT INTO categories (name, slug) VALUES (?, ?)', [name, slug]);
    const newCategory = await db.get('SELECT id, name, slug FROM categories WHERE id = ?', [result.lastID]);
    return res.status(201).json({ success: true, data: newCategory });
  } catch (error) {
    debugError('Error creating category:', error);
    if (error.code === 'SQLITE_CONSTRAINT' && error.message.includes('UNIQUE constraint failed: categories.slug')) {
        return res.status(409).json({ success: false, error: 'Slug already exists. Please choose a unique slug.' });
    } else if (error.code === 'SQLITE_CONSTRAINT' && error.message.includes('UNIQUE constraint failed: categories.name')) {
        return res.status(409).json({ success: false, error: 'Category name already exists. Please choose a unique name.' });
    }
    return res.status(500).json({ success: false, error: 'Failed to create category' });
  }
});

// Update an existing category (admin only)
router.put('/categories/:id', authMiddleware, adminMiddleware, async (req, res) => {
  const { id } = req.params;
  const { name, slug } = req.body;
  if (!name || !slug) {
    return res.status(400).json({ success: false, error: 'Name and slug are required' });
  }
  try {
    const db = await getDatabase();
    const existingCategory = await db.get('SELECT * FROM categories WHERE id = ?', [id]);
    if (!existingCategory) {
      return res.status(404).json({ success: false, error: 'Category not found' });
    }
    await db.run('UPDATE categories SET name = ?, slug = ? WHERE id = ?', [name, slug, id]);
    const updatedCategory = await db.get('SELECT id, name, slug FROM categories WHERE id = ?', [id]);
    return res.json({ success: true, data: updatedCategory });
  } catch (error) {
    debugError('Error updating category:', error);
     if (error.code === 'SQLITE_CONSTRAINT' && error.message.includes('UNIQUE constraint failed: categories.slug')) {
        return res.status(409).json({ success: false, error: 'Slug already exists. Please choose a unique slug.' });
    } else if (error.code === 'SQLITE_CONSTRAINT' && error.message.includes('UNIQUE constraint failed: categories.name')) {
        return res.status(409).json({ success: false, error: 'Category name already exists. Please choose a unique name.' });
    }
    return res.status(500).json({ success: false, error: 'Failed to update category' });
  }
});

// Get all stores (admin only)
router.get('/stores', authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const db = await getDatabase();
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 30; // Default limit to 30 or what you prefer
    const offset = (page - 1) * limit;

    // Query for stores with pagination and counts
    const storesData = await db.all(`
      SELECT 
        s.id,
        s.name,
        s.url,
        s.logoUrl,
        REPLACE(LOWER(s.name), ' ', '-') AS slug,  -- Simple slug generation
        COUNT(d.id) AS dealsCount
      FROM stores s
      LEFT JOIN deals d ON s.id = d.store_id
      GROUP BY s.id, s.name, s.url, s.logoUrl
      ORDER BY s.name ASC
      LIMIT ? OFFSET ?
    `, [limit, offset]);

    // Query for total number of stores for pagination
    const totalStoresResult = await db.get(`
      SELECT COUNT(id) as total FROM stores
    `);
    const totalStores = totalStoresResult.total;
    const totalPages = Math.ceil(totalStores / limit);
    
    return res.json({
      success: true,
      data: { 
        stores: storesData,
      },
      page: page,
      totalPages: totalPages,
      totalCount: totalStores
    });
  } catch (error) { // Make sure to catch the error object
    debugError('Error fetching stores:', error);  
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch stores'
    });
  }
});

// Create a new store (admin only)
router.post('/stores', authMiddleware, adminMiddleware, async (req, res) => {
  const { name, url, logoUrl } = req.body;
  if (!name) {
    return res.status(400).json({ success: false, error: 'Store name is required' });
  }

  try {
    const db = await getDatabase();
    const result = await db.run(
      'INSERT INTO stores (name, url, logoUrl) VALUES (?, ?, ?)',
      [name, url, logoUrl]
    );
    const newStoreId = result.lastID;
    const newStore = await db.get('SELECT * FROM stores WHERE id = ?', newStoreId);
    return res.status(201).json({ success: true, data: newStore });
  } catch (error) {
    debugError('Error creating store:', error);
    if (error.code === 'SQLITE_CONSTRAINT' && error.message.includes('UNIQUE constraint failed: stores.name')) {
        return res.status(409).json({ success: false, error: 'Store name already exists.' });
    }
    return res.status(500).json({ success: false, error: 'Failed to create store' });
  }
});

// Update an existing store (admin only)
router.put('/stores/:id', authMiddleware, adminMiddleware, async (req, res) => {
  const { id } = req.params;
  const { name, url, logoUrl } = req.body;

  if (!name) {
    return res.status(400).json({ success: false, error: 'Store name is required' });
  }

  try {
    const db = await getDatabase();
    const existingStore = await db.get('SELECT * FROM stores WHERE id = ?', [id]);
    if (!existingStore) {
      return res.status(404).json({ success: false, error: 'Store not found' });
    }

    await db.run(
      'UPDATE stores SET name = ?, url = ?, logoUrl = ? WHERE id = ?',
      [name, url, logoUrl, id]
    );
    const updatedStore = await db.get('SELECT * FROM stores WHERE id = ?', [id]);
    return res.json({ success: true, data: updatedStore });
  } catch (error) {
    debugError('Error updating store:', error);
    if (error.code === 'SQLITE_CONSTRAINT' && error.message.includes('UNIQUE constraint failed: stores.name')) {
        return res.status(409).json({ success: false, error: 'Store name already exists.' });
    }
    return res.status(500).json({ success: false, error: 'Failed to update store' });
  }
});

// Delete a store (admin only)
router.delete('/stores/:id', authMiddleware, adminMiddleware, async (req, res) => {
  const { id } = req.params;
  try {
    const db = await getDatabase();
    const existingStore = await db.get('SELECT * FROM stores WHERE id = ?', [id]);
    if (!existingStore) {
      return res.status(404).json({ success: false, error: 'Store not found' });
    }

    // Optional: Check if any deals are associated with this store and prevent deletion or handle accordingly
    // const dealsCount = await db.get('SELECT COUNT(*) as count FROM deals WHERE store_id = ?', [id]);
    // if (dealsCount.count > 0) {
    //   return res.status(400).json({ success: false, error: 'Cannot delete store with active deals. Please reassign or delete deals first.' });
    // }

    await db.run('DELETE FROM stores WHERE id = ?', [id]);
    return res.json({ success: true, message: 'Store deleted successfully' });
  } catch (error) {
    debugError('Error deleting store:', error);
    return res.status(500).json({ success: false, error: 'Failed to delete store' });
  }
});

// Get scraper logs
router.get('/scrapers/logs', authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const db = await getDatabase();
    const logs = await db.all(`
      SELECT 
        id,
        source,
        timestamp as created_at,
        status,
        deals_found,
        deals_added,
        error
      FROM scrape_logs 
      ORDER BY timestamp DESC 
      LIMIT 20
    `);
    
    if (!logs) {
      return res.json({
        success: true,
        data: { logs: [] }
      });
    }

    return res.json({
      success: true,
      data: { logs }
    });
  } catch (error) {
    debugError('Error fetching scraper logs:', error);  
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch scraper logs'
    });
  }
});

// Run a specific scraper
router.post('/scrapers/run', authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const { source } = req.body;
    let result;

    switch (source) {
      case 'hotukdeals':
        result = await runHotUKDealsScraper();
        break;
      // Add other scrapers here
      default:
        return res.status(400).json({
          success: false,
          error: `Unknown scraper source: ${source}`
        });
    }

    return res.json({
      success: true,
      data: result
    });
  } catch (error) {
    debugError('Error running scraper:', error);  
    return res.status(500).json({
      success: false,
      error: 'Failed to run scraper'
    });
  }
});

// Run all scrapers
router.post('/scrapers/run-all', authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const results = await Promise.all([
      runHotUKDealsScraper(),
      // Add other scrapers here
    ]);

    return res.json({
      success: true,
      data: results
    });
  } catch (error) {
    debugError('Error running all scrapers:', error);  
    return res.status(500).json({
      success: false,
      error: 'Failed to run all scrapers'
    });
  }
});

// Get dashboard stats (admin only)
router.get('/dashboard', authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const db = await getDatabase();
    
    // Get total users count
    const userCountResult = await db.get('SELECT COUNT(*) as count FROM users');
    const totalUsers = userCountResult.count;
    
    // Get total deals count
    const dealsCountResult = await db.get('SELECT COUNT(*) as count FROM deals');
    const totalDeals = dealsCountResult.count;
    
    // Get active deals count
    const activeDealsCountResult = await db.get('SELECT COUNT(*) as count FROM deals WHERE status = ?', ['active']);
    const activeDeals = activeDealsCountResult.count;
    
    // Get pending deals count
    const pendingDealsCountResult = await db.get('SELECT COUNT(*) as count FROM deals WHERE status = ?', ['pending']);
    const pendingDeals = pendingDealsCountResult.count;
    
    // Get new users today
    const today = new Date().toISOString().split('T')[0];
    const newUsersTodayResult = await db.get(
      'SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = ?',
      [today]
    );
    const newUsersToday = newUsersTodayResult.count;
    
    // Get new deals today
    const newDealsTodayResult = await db.get(
      'SELECT COUNT(*) as count FROM deals WHERE DATE(created_at) = ?',
      [today]
    );
    const newDealsToday = newDealsTodayResult.count;
    
    // Calculate growth percentages (mock data for now)
    const userGrowth = 5.2;
    const dealGrowth = 12.7;
    
    // Get recent activity
    const recentActivity = await db.all(`
      SELECT 
        'deal' as type,
        d.id,
        'added' as action,
        d.title,
        d.created_at as timestamp,
        json_object('id', u.id, 'username', u.username) as user
      FROM deals d
      JOIN users u ON d.user_id = u.id
      ORDER BY d.created_at DESC
      LIMIT 5
    `);
    
    // Format the recent activity
    const formattedActivity = recentActivity.map(activity => ({
      id: activity.id,
      type: activity.type,
      action: activity.action,
      title: activity.title,
      timestamp: activity.timestamp,
      user: JSON.parse(activity.user)
    }));
    
    return res.json({
      success: true,
      data: {
        stats: {
          totalUsers,
          totalDeals,
          activeDeals,
          pendingDeals,
          newUsersToday,
          newDealsToday,
          userGrowth,
          dealGrowth
        },
        recentActivity: formattedActivity
      }
    });
  } catch (error) {
    debugError('Error fetching dashboard stats:', error);  
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard stats'
    });
  }
});

// Get all deals (admin only)
router.get('/deals', authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 30;
    const offset = (page - 1) * limit;
    const status = req.query.status;
    const category = req.query.category;
    const store = req.query.store;
    const search = req.query.search;
    // Parse sort parameter - handle both "field" and "field_direction" formats
    let sort = req.query.sort || 'created_desc';
    let sortField = sort;
    let sortDirection = 'desc'; // Default direction
    
    // If sort has both field and direction (field_direction format)
    if (sort.includes('_')) {
      const parts = sort.split('_');
      sortField = parts[0];
      sortDirection = parts[1];
    }
    
    console.log('\n\nDEBUG SORTING - Admin deals request received:');
    console.log('- Query params:', req.query);
    console.log('- Raw sort parameter:', sort);
    console.log('- Parsed sort field:', sortField);
    console.log('- Parsed sort direction:', sortDirection);

    const db = await getDatabase();

    // Build the base query
    let query = `
      SELECT 
        d.*,
        c.name as category_name,
        s.name as store_name,
        COUNT(DISTINCT cm.id) as comment_count
      FROM deals d
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      LEFT JOIN comments cm ON d.id = cm.deal_id
    `;

    // Build WHERE clause
    const conditions = [];
    const params = [];

    if (status && status !== 'all') {
      conditions.push('d.status = ?');
      params.push(status);
    }

    if (category && category !== 'all') {
      conditions.push('d.category_id = ?');
      params.push(category);
    }

    if (store && store !== 'all') {
      conditions.push('d.store_id = ?');
      params.push(store);
    }

    if (search) {
      conditions.push('(d.title LIKE ? OR d.description LIKE ?)');
      params.push(`%${search}%`, `%${search}%`);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    // Add GROUP BY
    query += ' GROUP BY d.id';

    // Add ORDER BY based on parsed sort field and direction
    // First determine the SQL column to sort by based on sortField
    let orderByColumn;
    switch (sortField) {
      case 'created':
        orderByColumn = 'd.created_at';
        break;
      case 'updated':
        orderByColumn = 'd.updated_at';
        break;
      case 'store':
        orderByColumn = 's.name';
        break;
      case 'title':
        orderByColumn = 'd.title';
        break;
      case 'categoryName':
      case 'category':
        orderByColumn = 'c.name';
        break;
      default:
        orderByColumn = 'd.created_at';
    }
    
    // Determine sort direction (ASC or DESC)
    // When sort direction is 'asc' we want A->Z (SQL ASC)
    // When sort direction is 'desc' we want Z->A (SQL DESC)
    const sqlDirection = (sortDirection === 'asc') ? 'ASC' : 'DESC';
    
    // Build the ORDER BY clause
    query += ` ORDER BY ${orderByColumn} ${sqlDirection}`;
    
    console.log(`- Sorting by: ${orderByColumn} ${sqlDirection}`);

    // Add pagination
    query += ' LIMIT ? OFFSET ?';
    params.push(limit, offset);

    // Debug final SQL query
    console.log('- Final SQL query:', query);
    console.log('- SQL parameters:', params);

    // Execute the query
    const deals = await db.all(query, params);
    console.log(`- Query returned ${deals.length} deals`);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(DISTINCT d.id) as total
      FROM deals d
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
    `;

    if (conditions.length > 0) {
      countQuery += ` WHERE ${conditions.join(' AND ')}`;
    }

    const { total } = await db.get(countQuery, params.slice(0, -2));
    const totalPages = Math.ceil(total / limit);

    // Format deals for response
    const formattedDeals = deals.map(deal => ({
      id: deal.id,
      title: deal.title,
      description: deal.description,
      url: deal.url,
      imageUrl: deal.image_url,
      thumbnailUrl: deal.thumbnail_url,
      price: deal.price,
      originalPrice: deal.original_price,
      category: { id: deal.category_id, name: deal.category_name },
      store: { id: deal.store_id, name: deal.store_name },
      status: deal.status,
      createdAt: deal.created_at,
      updatedAt: deal.updated_at,
      expiresAt: deal.expires_at,
      commentCount: deal.comment_count,
      coupon: deal.coupon
    }));

    res.json({
      success: true,
      deals: formattedDeals,
      page,
      totalPages,
      totalCount: total
    });

  } catch (error) {
    console.error('Error fetching deals:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch deals'
    });
  }
});

router.get('/deals/:id', authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    debugLog('Fetching admin deal with ID:', id); 
    
    const db = await getDatabase();
    
    const deal = await db.get(`
      SELECT 
        d.*,
        c.name as category_name,
        s.name as store_name
      FROM deals d
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      WHERE d.id = ?
    `, [id]);

    debugLog('Deal found:', deal); 

    if (!deal) {
      return res.status(404).json({ error: 'Deal not found' });
    }

    // Transform the deal data to match frontend expectations
    const transformedDeal = {
      id: deal.id,
      title: deal.title,
      description: deal.description,
      price: deal.price,
      originalPrice: deal.original_price,
      url: deal.url,
      imageUrl: deal.image_url,
      thumbnailUrl: deal.thumbnail_url,
      status: deal.status,
      storeId: deal.store_id,
      categoryId: deal.category_id,
      expiresAt: deal.expires_at,
      createdAt: deal.created_at,
      coupon: deal.coupon,
      store: {
        id: deal.store_id,
        name: deal.store_name
      },
      category: {
        id: deal.category_id,
        name: deal.category_name
      }
    };

    return res.json({
      deal: transformedDeal
    });
  } catch (error) {
    debugError('Error fetching deal:', error);  
    return res.status(500).json({ 
      error: 'Failed to fetch deal' 
    });
  }
});

router.put('/deals/:id', authMiddleware, adminMiddleware, async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      price,
      originalPrice,
      url,
      status,
      storeId,
      categoryId,
      imageUrl,
      coupon
    } = req.body;

    debugLog('Update deal request body:', {
      id,
      title,
      price,
      originalPrice,
      storeId,
      categoryId,
      status,
      coupon
    });

    const db = await getDatabase();

    // First check if deal exists
    const existingDeal = await db.get('SELECT id FROM deals WHERE id = ?', [id]);
    if (!existingDeal) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    // Validate required fields
    if (!title || !url || price === undefined || !storeId || !categoryId || !imageUrl) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    // Validate numeric fields
    const priceNum = Number(price);
    const originalPriceNum = originalPrice ? Number(originalPrice) : null;
    const storeIdNum = Number(storeId);
    const categoryIdNum = Number(categoryId);

    if (isNaN(priceNum) || isNaN(storeIdNum) || isNaN(categoryIdNum) || (originalPrice !== null && isNaN(originalPriceNum))) {
      return res.status(400).json({
        success: false,
        error: 'Invalid numeric values'
      });
    }

    // Validate foreign keys exist
    const [store, category] = await Promise.all([
      db.get('SELECT id FROM stores WHERE id = ?', [storeIdNum]),
      db.get('SELECT id FROM categories WHERE id = ?', [categoryIdNum])
    ]);

    if (!store || !category) {
      return res.status(400).json({
        success: false,
        error: 'Invalid store or category ID'
      });
    }

    // Handle null/undefined values and ensure proper types
    const descriptionValue = description || '';

    // Update the deal
   const result = await db.run(`
      UPDATE deals 
      SET 
        title = ?,
        description = ?,
        price = ?,
        original_price = ?,
        url = ?,
        status = ?,
        store_id = ?,
        category_id = ?,
        image_url = ?,
        coupon = ?,
        updated_at = strftime('%Y-%m-%dT%H:%M:%fZ', 'now')
      WHERE id = ?
    `, [
      title,
      descriptionValue,
      priceNum,
      originalPriceNum,
      url,
      status,
      storeIdNum,
      categoryIdNum,
      imageUrl,
      coupon || null,
      id
    ]);

    if (result.changes === 0) {
      throw new Error('Deal update failed - no rows affected');
    }

    // Fetch the updated deal to return
    const updatedDeal = await db.get(`
      SELECT 
        d.*,
        c.name as category_name,
        s.name as store_name
      FROM deals d
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      WHERE d.id = ?
    `, [id]);

    return res.json({ 
      success: true,
      deal: updatedDeal 
    });

  } catch (error) {
    debugError('Error updating deal:', error);  
    return res.status(500).json({ 
      success: false,
      error: 'Failed to update deal' 
    });
  }
});

router.post('/deals/localize-image', 
  authMiddleware, 
  adminMiddleware, 
  localizeImage
);

// Activate a pending deal with AI processing
router.post('/deals/:id/activate', authMiddleware, adminMiddleware, async (req, res) => {
  const { id } = req.params;
  
  try {
    const db = await getDatabase();
    
    // Check if the deal exists and is pending
    const deal = await db.get(
      'SELECT * FROM deals WHERE id = ? AND status = ?',
      [id, 'pending']
    );
    
    if (!deal) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found or not in pending status'
      });
    }
    
    debugLog(`Processing deal #${id}: ${deal.title}`);
    
    // 1. Create backup
    try {
      await db.run(`
        INSERT INTO deals_backup (
          original_deal_id, title, description, url, image_url, thumbnail_url,
          price, original_price, category_id, store_id, user_id, status,
          upvotes, downvotes, created_at, expires_at, coupon  
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        deal.id, deal.title, deal.description, deal.url, deal.image_url, deal.thumbnail_url,
        deal.price, deal.original_price, deal.category_id, deal.store_id, deal.user_id, deal.status,
        deal.upvotes, deal.downvotes, deal.created_at, deal.expires_at, deal.coupon
      ]);
      debugLog(`Backed up deal #${id}`);
    } catch (backupError) {
      debugError('Error backing up deal:', backupError);
      throw backupError;
    }
    
    let updatedTitle = deal.title;
    let updatedDescription = deal.description;
    let localizedImageUrl = deal.image_url;
    let localizedThumbnailUrl = deal.thumbnail_url;
    
    // 2. Improve title using OpenRouter API
    try {
      // Only attempt to improve title if we have a valid title to begin with
      if (deal.title && deal.title.length > 0) {
        debugLog('Improving title...');
        const titleResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
            'HTTP-Referer': process.env.FRONTEND_URL || 'http://localhost:3010',
            'X-Title': 'NiceDeals',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            'model': 'deepseek/deepseek-chat:free',
            'messages': [{
              'role': 'user',
              'content': `Can you change the wording of this title to be more concise while keeping the essential product title? The original title is: "${deal.title}". Give me ONLY the improved title with no explanations or formatting or quotes or product attributes.`
            }],
            'max_tokens': 100
          })
        });

        if (!titleResponse.ok) {
          throw new Error(`OpenRouter API responded with status ${titleResponse.status}`);
        }

        const titleResult = await titleResponse.json();
        debugLog('Title improvement response:', titleResult);

        if (titleResult.choices && titleResult.choices[0] && titleResult.choices[0].message) {
          updatedTitle = titleResult.choices[0].message.content.trim()
            .replace(/\*\*/g, '')
            .replace(/\*/g, '')
            .replace(/^[-*•]/g, '')
            .trim();
          debugLog(`Title improved: "${deal.title}" -> "${updatedTitle}"`);
        } else {
          debugError('Unexpected title improvement response structure:', titleResult);
          // Keep original title if API response is unexpected
          debugLog('Keeping original title due to API response issue');
        }
      } else {
        debugLog('Skipping title improvement as original title is empty');
      }
    } catch (titleError) {
      debugError('Error improving title:', titleError);
      // Keep original title in case of error
      debugLog('Keeping original title due to API error');
    }

    // Wait 5 seconds before description generation
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 3. Generate description if none exists or if it's very short
    if (!deal.description || deal.description.length < 50) {
      try {
        debugLog('Generating description...');
        const descResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
            'HTTP-Referer': process.env.FRONTEND_URL || 'http://localhost:3010',
            'X-Title': 'NiceDeals',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            'model': 'deepseek/deepseek-chat:free',
            'messages': [{
              'role': 'user',
              'content': `Generate a concise but informative product description for this deal. The product title is: "${updatedTitle}". The product URL is: ${deal.url}. Give me ONLY the description with no explanations or formatting.`
            }]
          })
        });

        const descResult = await descResponse.json();
        debugLog('Description generation response:', descResult);

        if (descResult.choices && descResult.choices[0] && descResult.choices[0].message) {
          updatedDescription = descResult.choices[0].message.content.trim()
            .replace(/\*\*/g, '')
            .replace(/\*/g, '')
            .replace(/^[-*•]/g, '')
            .trim();
          debugLog(`Description generated: "${updatedDescription}"`);
        } else {
          debugError('Unexpected description generation response structure:', descResult);
        }
      } catch (descError) {
        debugError('Error generating description:', descError);
      }
    }

    // Wait 5 seconds before image localization
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 4. Localize image if it's an external URL
    if (deal.image_url && deal.image_url.startsWith('http')) {
      try {
        debugLog('Localizing image...');
        const result = await localizeRemoteImage(deal.image_url);
        if (result.imageUrl) {
          localizedImageUrl = result.imageUrl;
          localizedThumbnailUrl = result.thumbnailUrl;
          debugLog(`Image localized: ${localizedImageUrl}`);
        }
      } catch (imageError) {
        debugError('Error localizing image:', imageError);
      }
    }
    
    // 5. Update the deal and set status to active
    await db.run(`
      UPDATE deals
      SET
        title = ?,
        description = ?,
        image_url = ?,
        thumbnail_url = ?,
        status = ?,
        updated_at = strftime('%Y-%m-%dT%H:%M:%fZ', 'now')
      WHERE id = ?
    `, [
      updatedTitle,
      updatedDescription,
      localizedImageUrl,
      localizedThumbnailUrl,
      'active',
      id
    ]);
    debugLog(`Deal #${id} activated successfully`);

    const updatedDeal = await db.get(`
      SELECT 
        d.*,
        c.name as category_name,
        s.name as store_name
      FROM deals d
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      WHERE d.id = ?
    `, [id]);
    
    // Format the deal to match frontend expectations
    const formattedDeal = {
      id: updatedDeal.id,
      title: updatedDeal.title,
      description: updatedDeal.description,
      url: updatedDeal.url,
      imageUrl: updatedDeal.image_url,
      thumbnailUrl: updatedDeal.thumbnail_url,
      price: updatedDeal.price,
      originalPrice: updatedDeal.original_price,
      category: { id: updatedDeal.category_id, name: updatedDeal.category_name },
      store: { id: updatedDeal.store_id, name: updatedDeal.store_name },
      status: updatedDeal.status,
      createdAt: updatedDeal.created_at,
      updatedAt: updatedDeal.updated_at,
      expiresAt: updatedDeal.expires_at,
      coupon: updatedDeal.coupon
    };
    
    return res.json({
      success: true,
      data: formattedDeal
    });
  } catch (error) {
    debugError(`Error activating deal #${id}:`, error);
    return res.status(500).json({
      success: false,
      error: `Failed to activate deal: ${error.message}`
    });
  }
});

// Route handler for getting deals count by status
router.get('/deals/count', authMiddleware, adminMiddleware, async (req, res) => {
  const db = await getDatabase();
  const status = req.query.status || 'pending';
  
  try {
    const result = await db.get(
      'SELECT COUNT(*) as count FROM deals WHERE status = ?',
      [status]
    );
    
    return res.json({
      success: true,
      count: result.count
    });
  } catch (error) {
    debugError('Error getting deals count:', error);  
    return res.status(500).json({
      success: false,
      error: 'Failed to get deals count'
    });
  }
});

// Add this route handler after the other deal routes
router.delete('/deals/:id', authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const db = await getDatabase();

    // Check if deal exists
    const deal = await db.get('SELECT * FROM deals WHERE id = ?', [id]);
    if (!deal) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    // Start transaction
    await db.run('BEGIN TRANSACTION');
    try {
      // Delete associated images if they are local
      if (deal.image_url && !deal.image_url.startsWith('http')) {
        // Remove /uploads prefix and normalize paths
        const relativePath = normalizePath(deal.image_url);
        const relativeThumbnailPath = normalizePath(deal.thumbnail_url);
        
        const imagePath = path.join(__dirname, '..', '..', 'public', relativePath);
        const thumbnailPath = path.join(__dirname, '..', '..', 'public', relativeThumbnailPath);
        
        debugLog('Deleting images:', {
          imagePath,
          thumbnailPath,
          originalImageUrl: deal.image_url,
          originalThumbnailUrl: deal.thumbnail_url
        });

        try {
          if (fsSync.existsSync(imagePath)) {
            await fs.unlink(imagePath);
            debugLog(`Deleted image: ${imagePath}`);
          }
          if (fsSync.existsSync(thumbnailPath)) {
            await fs.unlink(thumbnailPath);
            debugLog(`Deleted thumbnail: ${thumbnailPath}`);
          }
        } catch (err) {
          debugError('Error deleting image files:', err);
          debugError('Paths:', { imagePath, thumbnailPath });
        }
      }

      // Delete related data
      await db.run('DELETE FROM votes WHERE deal_id = ?', [id]);
      await db.run('DELETE FROM comments WHERE deal_id = ?', [id]);
      await db.run('DELETE FROM deals_backup WHERE original_deal_id = ?', [id]);
      await db.run('DELETE FROM deals WHERE id = ?', [id]);
      
      await db.run('COMMIT');
      debugLog(`Deal #${id} and associated data deleted successfully`);
      
      return res.json({
        success: true,
        message: 'Deal and associated data deleted successfully'
      });
    } catch (err) {
      await db.run('ROLLBACK');
      throw err;
    }
  } catch (error) {
    debugError('Error deleting deal:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to delete deal'
    });
  }
});

// Helper function to localize remote images
async function localizeRemoteImage(imageUrl) {
  try {
    debugLog(`Attempting to localize image from: ${imageUrl}`);
    
    // Validate the image URL
    if (!imageUrl || typeof imageUrl !== 'string' || !imageUrl.startsWith('http')) {
      debugError('Invalid image URL:', imageUrl);
      return { imageUrl, thumbnailUrl: imageUrl };
    }
    
    // Download the image from remote URL
    debugLog('Downloading image...');
    const response = await axios.get(imageUrl, { 
      responseType: 'arraybuffer',
      timeout: 10000, // 10 second timeout
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    const buffer = Buffer.from(response.data, 'binary');
    debugLog(`Downloaded image: ${buffer.length} bytes`);
    
    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(__dirname, '..', '..', 'public', 'uploads', 'deals');
    const thumbnailsDir = path.join(uploadsDir, 'thumbnails');
    
    // Fix: Since we're using fs.promises already (line 6: const fs = require('fs').promises), we don't need .promises anymore
    await fs.mkdir(uploadsDir, { recursive: true });
    await fs.mkdir(thumbnailsDir, { recursive: true });
    
    // Create unique filename using UUID
    const imageId = uuidv4();
    const fileName = `${imageId}.webp`;
    const filePath = path.join(uploadsDir, fileName);
    const thumbnailPath = path.join(thumbnailsDir, fileName);
    
    debugLog(`Processing image to: ${filePath}`);
    // Save main image
    await sharp(buffer)
      .resize(800, 800, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .webp({ quality: 80 })
      .toFile(filePath);
    
    debugLog(`Generating thumbnail at: ${thumbnailPath}`);
    // Generate thumbnail
    await sharp(buffer)
      .resize(200, 200, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .webp({ quality: 70 })
      .toFile(thumbnailPath);
    
    // Verify files were created
    const mainImageExists = await fs.access(filePath).then(() => true).catch(() => false);
    const thumbnailExists = await fs.access(thumbnailPath).then(() => true).catch(() => false);

    if (!mainImageExists || !thumbnailExists) {
      throw new Error('Failed to verify image file creation');
    }
    
    // Return URLs in the same format as the deals controller
    const image_url = `/uploads/deals/${fileName}`;
    const thumbnail_url = `/uploads/deals/thumbnails/${fileName}`;
    
    debugLog(`Image localized successfully:
      Original: ${imageUrl}
      Local image: ${image_url}
      Local thumbnail: ${thumbnail_url}
    `);
    
    return { imageUrl: image_url, thumbnailUrl: thumbnail_url };
  } catch (error) {
    debugError('Error localizing image:', error.message);
    if (error.response) {
      debugError('Response status:', error.response.status);
      debugError('Response headers:', error.response.headers);
    }
    // Return original URL in case of error
    return { imageUrl: imageUrl, thumbnailUrl: imageUrl };
  }
}

module.exports = router;
