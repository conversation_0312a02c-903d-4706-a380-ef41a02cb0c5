const express = require('express');
const router = express.Router();
const { authMiddleware } = require('../middlewares/auth');
const fetch = require('node-fetch');

const OPENROUTER_API_KEY = '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';
const SITE_URL = process.env.FRONTEND_URL || 'http://localhost:3010';
const SITE_NAME = 'NiceDeals';

/**
 * Helper to extract a clean title from OpenRouter's response
 * @param {string} text - The raw response text
 * @returns {string} - The cleaned title
 */
function extractCleanTitle(text) {
  // Remove any markdown formatting, option indicators, etc.
  let cleanTitle = text.trim();
  
  // If there are multiple lines with options, take the first option
  if (cleanTitle.includes('Option')) {
    const optionMatch = cleanTitle.match(/Option 1[^:]*:\s*(.+?)(?=Option|\n\n|$)/is);
    if (optionMatch && optionMatch[1]) {
      cleanTitle = optionMatch[1].trim();
    }
  }
  
  // Remove any remaining formatting characters
  cleanTitle = cleanTitle
    .replace(/\*\*/g, '')  // Remove bold markdown
    .replace(/\*/g, '')    // Remove italic markdown
    .replace(/^[-*•]/g, '') // Remove bullet points
    .trim();
  
  return cleanTitle;
}

/**
 * @route POST /api/ai/generate
 * @desc Use OpenRouter API to process text
 * @access Private (requires authentication)
 */
router.post('/generate', authMiddleware, async (req, res) => {
  try {
    const { prompt, title } = req.body;
    
    if (!prompt) {
      return res.status(400).json({
        error: 'Prompt is required'
      });
    }
    console.log('URL is : ' + prompt);
    
    // Determine if this request involves fetching web content
    const isWebContentRequest = prompt.includes('Please analyze this product') || 
                               prompt.includes('http://') || 
                               prompt.includes('https://');
    
    let finalPrompt = prompt;

    // If it's just a URL, use the template from .env and clean the URL
    if (isWebContentRequest && !prompt.includes('Please analyze this product')) {
      // Remove timestamp parameter if present
      const cleanUrl = prompt.replace(/[?&]_t=\d+/, '');
      finalPrompt = process.env.AI_DESCRIPTION_PROMPT
        .replace('{{URL}}', cleanUrl)
        .replace('{{TITLE}}', title);
    }
    console.log('Final Prompt is : ' + finalPrompt);

    // Call OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': SITE_URL,
        'X-Title': SITE_NAME,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        'model': 'deepseek/deepseek-chat:free',
        'messages': [
          {
            'role': 'user',
            'content': finalPrompt
          }
        ]
      })
    });

    const result = await response.json();
    let text = result.choices[0].message.content;
    
    // Process response based on request type
    if (prompt.includes('change the wording of this title')) {
      text = extractCleanTitle(text);
    } else if (isWebContentRequest) {
      // Clean up description content
      text = text.trim()
        .replace(/^\*\*Description:?\*\*/i, '')
        .replace(/^Description:?/i, '')
        .replace(/\*\*/g, '')
        .replace(/\*/g, '')
        .replace(/^[-*•]/g, '')
        .trim();
    }
    
    res.json({
      success: true,
      text
    });
  } catch (error) {
    console.error('Error calling OpenRouter API:', error);
    res.status(500).json({
      error: 'Failed to process with OpenRouter API',
      message: error.message
    });
  }
});

module.exports = router;