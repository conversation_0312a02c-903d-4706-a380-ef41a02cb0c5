const express = require('express');
const { register, login, getCurrentUser, verifyEmail, resendVerification } = require('../controllers/auth');
const { authMiddleware } = require('../middlewares/auth');

const router = express.Router();

// Register new user
router.post('/register', register);

// Login user
router.post('/login', login);

// Verify email
router.get('/verify-email', verifyEmail);

// Resend verification email
router.post('/resend-verification', authMiddleware, resendVerification);

// Get current user info
router.get('/me', authMiddleware, getCurrentUser);

module.exports = router;
