const express = require('express');
const { 
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory
} = require('../controllers/categories');
const { authMiddleware, adminMiddleware } = require('../middlewares/auth');

const router = express.Router();

// Get all categories (public)
router.get('/', getCategories);

// Get a single category (public)
router.get('/:id', getCategoryById);

// Create a new category (admin only)
router.post('/', authMiddleware, adminMiddleware, createCategory);

// Update a category (admin only)
router.put('/:id', authMiddleware, adminMiddleware, updateCategory);

// Delete a category (admin only)
router.delete('/:id', authMiddleware, adminMiddleware, deleteCategory);

module.exports = router;
