const express = require('express');
const { 
  getComments, 
  addComment, 
  updateComment, 
  deleteComment,
  getUserComments
} = require('../controllers/comments');
const { authMiddleware } = require('../middlewares/auth');

const router = express.Router();

// Get comments by the current user
router.get('/user/me', authMiddleware, getUserComments);

// Get comments for a deal
router.get('/:dealId', getComments);

// Add a comment
router.post('/', authMiddleware, addComment);

// Update a comment
router.put('/:id', authMiddleware, updateComment);

// Delete a comment
router.delete('/:id', authMiddleware, deleteComment);

module.exports = router;
