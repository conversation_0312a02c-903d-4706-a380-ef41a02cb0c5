const express = require('express');
const multer = require('multer');
const { 
  getDeals, 
  getDealById, 
  createDeal, 
  updateDeal, 
  deleteDeal,
  uploadDealImage,
  getNewestDeals,
  getTrendingDeals,
  getGettingWarmDeals,
  getMostCommentedDeals,
  localizeImage,
  getAdminDeal
} = require('../controllers/deals');
const { authMiddleware, moderatorMiddleware, adminMiddleware } = require('../middlewares/auth');
const { getDatabase } = require('../models/database');
const jwt = require('jsonwebtoken');

const router = express.Router();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Get all deals (public)
router.get('/', getDeals);

// Get newest deals (public)
router.get('/newest', getNewestDeals);

// Get trending deals (public)
router.get('/trending', getTrendingDeals);

// Get trending by temperature deals (public)
router.get('/getting-warm', getGettingWarmDeals);

// Get most commented deals (public)
router.get('/most-commented', getMostCommentedDeals);

// Get related deals by category (public)
router.get('/related', async (req, res) => {
  try {
    const categoryId = parseInt(req.query.category, 10);
    const excludeDealId = parseInt(req.query.excludeDealId, 10);
    const limit = parseInt(req.query.limit, 10) || 4;
    
    if (!categoryId) {
      return res.status(400).json({ error: 'Category ID is required' });
    }
    
    const db = await getDatabase();
    
    // Get deals from the same category, excluding the current deal
    const deals = await db.all(`
      SELECT 
        d.*, 
        u.username as user_username,
        c.name as category_name,
        s.name as store_name,
        s.logoUrl as store_logo_url,
        COALESCE(SUM(v.vote_type), 0) as temperature,
        COUNT(DISTINCT cm.id) as comment_count
      FROM deals d
      LEFT JOIN users u ON d.user_id = u.id
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      LEFT JOIN votes v ON d.id = v.deal_id
      LEFT JOIN comments cm ON d.id = cm.deal_id
      WHERE d.category_id = ? 
        AND d.status = 'active'
        ${excludeDealId ? 'AND d.id != ?' : ''}
      GROUP BY d.id
      ORDER BY d.updated_at DESC
      LIMIT ?
    `, excludeDealId ? [categoryId, excludeDealId, limit] : [categoryId, limit]);
    
    res.json({ data: deals });
  } catch (error) {
    console.error('Get related deals error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get saved deals for current user
router.get('/saved', authMiddleware, async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.pageSize) || 30;
  const offset = (page - 1) * pageSize;
  
  try {
    const db = await getDatabase();
    
    // Get total count of saved deals
    const totalCountResult = await db.get(
      'SELECT COUNT(*) as count FROM saved_deals WHERE user_id = ?',
      [req.user.id]
    );
    const totalCount = totalCountResult.count;
    const totalPages = Math.ceil(totalCount / pageSize);
    
    // Get paginated saved deals with all necessary information
    const deals = await db.all(
      `SELECT d.*, 
        u.username,
        c.name as category_name,
        s.name as store_name,
        (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = 'up') as upvotes,
        (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = 'down') as downvotes,
        (SELECT COUNT(*) FROM comments WHERE deal_id = d.id) as comment_count
      FROM deals d 
      JOIN users u ON d.user_id = u.id
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      JOIN saved_deals sd ON d.id = sd.deal_id
      WHERE sd.user_id = ?
      ORDER BY sd.created_at DESC
      LIMIT ? OFFSET ?`,
      [req.user.id, pageSize, offset]
    );
    
    // Transform deals to match frontend expectations
    const transformedDeals = deals.map(deal => ({
      id: deal.id,
      title: deal.title,
      description: deal.description,
      price: deal.price,
      originalPrice: deal.original_price,
      url: deal.url,
      imageUrl: deal.image_url,
      thumbnailUrl: deal.thumbnail_url,
      status: deal.status,
      createdAt: deal.created_at,
      expiresAt: deal.expires_at,
      userId: deal.user_id,
      username: deal.username,
      category: {
        id: deal.category_id,
        name: deal.category_name
      },
      store: {
        id: deal.store_id,
        name: deal.store_name
      },
      upvotes: deal.upvotes,
      downvotes: deal.downvotes,
      commentCount: deal.comment_count
    }));
    
    res.json({
      deals: transformedDeals,
      totalCount,
      page,
      pageSize,
      totalPages
    });
  } catch (error) {
    console.error('Error fetching saved deals:', error);
    res.status(500).json({ error: 'Failed to fetch saved deals' });
  }
});

// Add a test route to verify authentication
router.get('/test-auth', authMiddleware, (req, res) => {
  console.log('Auth test route - user:', req.user ? `ID: ${req.user.id}, Username: ${req.user.username}` : 'Not authenticated');
  res.json({ 
    message: 'Authentication test', 
    isAuthenticated: !!req.user,
    user: req.user ? { id: req.user.id, username: req.user.username } : null
  });
});

// Get a single deal (optionally authenticated for pending deals)
// We use an optional auth middleware to still allow public access but enable auth when present
router.get('/:id', (req, res, next) => {
  // Get token from header, but don't require it
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    // No token, continue as unauthenticated request
    console.log('No auth token provided - proceeding as unauthenticated request');
    return next();
  }
  
  // Token found, try to authenticate
  const token = authHeader.split(' ')[1];
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'dev_secret');
    req.user = decoded;
    console.log(`User authenticated from token: ID=${decoded.id}, Username=${decoded.username}`);
  } catch (error) {
    console.log('Invalid token provided:', error.message);
    // Invalid token, but continue anyway (just without authenticated user)
  }
  
  next();
}, getDealById);

// Create a new deal (authenticated)
router.post('/', authMiddleware, createDeal);

// Upload a deal image (authenticated)
router.post('/upload-image', authMiddleware, upload.single('image'), uploadDealImage);

// Update a deal (authenticated + owner or moderator)
router.put('/:id', authMiddleware, updateDeal);

// Delete a deal (authenticated + owner or moderator)
router.delete('/:id', authMiddleware, deleteDeal);

// Add this route to your deals routes
router.post('/localize-image', authMiddleware, localizeImage);

// Save a deal
router.post('/:dealId/save', authMiddleware, async (req, res) => {
  try {
    const { dealId } = req.params;
    const userId = req.user.id;
    
    const db = await getDatabase();
    
    // Check if deal exists
    const deal = await db.get('SELECT id FROM deals WHERE id = ?', [dealId]);
    if (!deal) {
      return res.status(404).json({ message: 'Deal not found' });
    }
    
    // Check if already saved
    const existingSave = await db.get(
      'SELECT id FROM saved_deals WHERE user_id = ? AND deal_id = ?',
      [userId, dealId]
    );
    
    if (existingSave) {
      return res.status(400).json({ message: 'Deal already saved' });
    }
    
    // Save the deal
    await db.run(
      'INSERT INTO saved_deals (user_id, deal_id, created_at) VALUES (?, ?, datetime("now"))',
      [userId, dealId]
    );
    
    res.json({ message: 'Deal saved successfully' });
  } catch (error) {
    console.error('Error saving deal:', error);
    res.status(500).json({ message: 'Error saving deal' });
  }
});

// Unsave a deal
router.delete('/:dealId/save', authMiddleware, async (req, res) => {
  try {
    const { dealId } = req.params;
    const userId = req.user.id;
    
    const db = await getDatabase();
    
    // Check if deal exists
    const deal = await db.get('SELECT id FROM deals WHERE id = ?', [dealId]);
    if (!deal) {
      return res.status(404).json({ message: 'Deal not found' });
    }
    
    // Remove the saved deal
    const result = await db.run(
      'DELETE FROM saved_deals WHERE user_id = ? AND deal_id = ?',
      [userId, dealId]
    );
    
    if (result.changes === 0) {
      return res.status(404).json({ message: 'Deal was not saved' });
    }
    
    res.json({ message: 'Deal unsaved successfully' });
  } catch (error) {
    console.error('Error unsaving deal:', error);
    res.status(500).json({ message: 'Error unsaving deal' });
  }
});

// Check if a deal is saved
router.get('/:id/saved', authMiddleware, async (req, res) => {
  const dealId = parseInt(req.params.id);
  const userId = req.user.id;

  try {
    const db = await getDatabase();
    const savedDeal = await db.get(
      'SELECT id FROM saved_deals WHERE user_id = ? AND deal_id = ?',
      [userId, dealId]
    );

    res.json({ saved: !!savedDeal });
  } catch (error) {
    console.error('Error checking saved status:', error);
    res.status(500).json({ error: 'Failed to check saved status' });
  }
});

module.exports = router;
