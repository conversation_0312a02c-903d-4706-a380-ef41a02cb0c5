const express = require('express');
const multer = require('multer');
const { 
  getStores,
  getStoreById,
  createStore,
  uploadStoreLogo,
  updateStore,
  deleteStore
} = require('../controllers/stores');
const { authMiddleware, adminMiddleware } = require('../middlewares/auth');

const router = express.Router();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 1 * 1024 * 1024, // 1MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Get all stores (public)
router.get('/', getStores);

// Get a single store (public)
router.get('/:id', getStoreById);

// Create a new store (admin only)
router.post('/', authMiddleware, adminMiddleware, createStore);

// Upload a store logo (admin only)
router.post('/:id/upload-logo', 
  authMiddleware, 
  adminMiddleware, 
  upload.single('logo'), 
  uploadStoreLogo
);

// Update a store (admin only)
router.put('/:id', authMiddleware, adminMiddleware, updateStore);

// Delete a store (admin only)
router.delete('/:id', authMiddleware, adminMiddleware, deleteStore);

module.exports = router;
