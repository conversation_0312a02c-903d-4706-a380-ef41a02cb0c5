const express = require('express');
const { getUserProfile, updateUserProfile, uploadAvatar, getUserDeals, getUserSavedDeals } = require('../controllers/users');
const { authMiddleware } = require('../middlewares/auth');
const { getDatabase } = require('../models/database');

const router = express.Router();

// Get current user's saved deals - This must come BEFORE the /:userId routes
router.get('/me/saved-deals', authMiddleware, async (req, res) => {
  try {
    // Ensure we have a user in the request
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

        const db = await getDatabase();
    
    const savedDeals = await db.all(`
      SELECT d.id, d.title, d.description, d.price, d.original_price, 
             d.store_id, d.category_id, d.image_url, d.url, d.status,
             d.created_at, d.updated_at, d.expires_at,
             u.username, 
             c.name as category_name,
             s.name as store_name,
             (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = 'up') as upvotes,
             (SELECT COUNT(*) FROM votes WHERE deal_id = d.id AND vote_type = 'down') as downvotes,
             (SELECT COUNT(*) FROM comments WHERE deal_id = d.id) as comment_count
      FROM saved_deals sd
      JOIN deals d ON sd.deal_id = d.id
      JOIN users u ON d.user_id = u.id
      LEFT JOIN categories c ON d.category_id = c.id
      LEFT JOIN stores s ON d.store_id = s.id
      WHERE sd.user_id = ?
      ORDER BY sd.created_at DESC
    `, [req.user.id]);
    
    // If no saved deals, return empty array instead of 404
    res.json(savedDeals || []);
  } catch (error) {
    console.error('Error getting saved deals:', error);
    res.status(500).json({ message: 'Error getting saved deals' });
  }
});

// Get user profile
router.get('/:userId/profile', getUserProfile);

// Update user profile
router.put('/:userId/profile', authMiddleware, updateUserProfile);

// Upload avatar
router.post('/:userId/avatar', authMiddleware, uploadAvatar);

// Get deals created by user
router.get('/:userId/deals', getUserDeals);

// Get deals saved by user
router.get('/:userId/saved-deals', getUserSavedDeals);

module.exports = router;
