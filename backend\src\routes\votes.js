const express = require('express');
const { vote, getUserVote } = require('../controllers/votes');
const { authMiddleware } = require('../middlewares/auth');
const { getDatabase } = require('../models/database');

const router = express.Router();

// Vote on a deal (upvote/downvote/remove vote) - new style
router.post('/', authMiddleware, vote);

// Vote on a deal (upvote/downvote) - CSR style compatibility 
router.post('/:dealId', authMiddleware, async (req, res) => {
  try {
    // Convert from CSR format to our controller format
    const dealId = parseInt(req.params.dealId);
    const { value } = req.body;
    
    // Validation
    if (!dealId) {
      return res.status(400).json({ error: 'Deal ID is required' });
    }
    
    if (value !== 1 && value !== -1) {
      return res.status(400).json({ error: 'Vote value must be 1 (upvote) or -1 (downvote)' });
    }
    
    const db = await getDatabase();
    
    // Check if deal exists
    const deal = await db.get('SELECT id FROM deals WHERE id = ?', [dealId]);
    if (!deal) {
      return res.status(404).json({ error: 'Deal not found' });
    }
    
    // Check if user has already voted
    const existingVote = await db.get(
      'SELECT vote_type FROM votes WHERE user_id = ? AND deal_id = ?',
      [req.user.id, dealId]
    );
    
    // If there's an existing vote
    if (existingVote) {
      if (existingVote.vote_type === value) {
        // Same vote, remove it (handled below)
        await db.run('DELETE FROM votes WHERE user_id = ? AND deal_id = ?', [req.user.id, dealId]);
      } else {
        // Different vote, update it
        await db.run('UPDATE votes SET vote_type = ? WHERE user_id = ? AND deal_id = ?', [value, req.user.id, dealId]);
      }
    } else {
      // No existing vote, insert new one
      await db.run(
        'INSERT INTO votes (user_id, deal_id, vote_type) VALUES (?, ?, ?)',
        [req.user.id, dealId, value]
      );
    }
    
    // Get updated vote count
    const upvotes = await db.get(
      'SELECT COUNT(*) as count FROM votes WHERE deal_id = ? AND vote_type = 1',
      [dealId]
    );
    
    const downvotes = await db.get(
      'SELECT COUNT(*) as count FROM votes WHERE deal_id = ? AND vote_type = -1',
      [dealId]
    );
    
    // Get the new user vote
    const newUserVote = await db.get(
      'SELECT vote_type FROM votes WHERE user_id = ? AND deal_id = ?',
      [req.user.id, dealId]
    );
    
    // Calculate temperature
    const temperature = upvotes.count - downvotes.count;
    
    // Note: We're not updating the temperature column as it doesn't exist in the DB schema
    
    // Notify connected clients via Socket.io
    if (req.io) {
      req.io.emit('vote-update', {
        dealId,
        upvotes: upvotes.count,
        downvotes: downvotes.count,
        temperature
      });
    }
    
    res.json({
      dealId,
      upvotes: upvotes.count,
      downvotes: downvotes.count,
      temperature,
      userVote: newUserVote ? newUserVote.vote_type : 0
    });
    
  } catch (error) {
    console.error('Vote error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Remove a vote - CSR style compatibility
router.delete('/:dealId', authMiddleware, async (req, res) => {
  try {
    const dealId = parseInt(req.params.dealId);
    
    if (!dealId) {
      return res.status(400).json({ error: 'Deal ID is required' });
    }
    
    const db = await getDatabase();
    
    // Delete the vote if it exists
    await db.run(
      'DELETE FROM votes WHERE user_id = ? AND deal_id = ?',
      [req.user.id, dealId]
    );
    
    // Get updated vote counts
    const upvotes = await db.get(
      'SELECT COUNT(*) as count FROM votes WHERE deal_id = ? AND vote_type = 1',
      [dealId]
    );
    
    const downvotes = await db.get(
      'SELECT COUNT(*) as count FROM votes WHERE deal_id = ? AND vote_type = -1',
      [dealId]
    );
    
    // Calculate temperature
    const temperature = upvotes.count - downvotes.count;
    
    // Note: We're not updating the temperature column as it doesn't exist in the DB schema
    
    // Notify connected clients via Socket.io
    if (req.io) {
      req.io.emit('vote-update', {
        dealId,
        upvotes: upvotes.count,
        downvotes: downvotes.count,
        temperature
      });
    }
    
    res.json({
      dealId,
      upvotes: upvotes.count,
      downvotes: downvotes.count,
      temperature,
      userVote: 0
    });
    
  } catch (error) {
    console.error('Remove vote error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get user's vote on a specific deal
router.get('/:dealId', authMiddleware, getUserVote);

module.exports = router;
