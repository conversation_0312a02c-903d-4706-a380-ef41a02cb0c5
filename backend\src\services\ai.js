/**
 * AI Service for backend operations
 * This service communicates with the OpenRouter AI endpoints to process data
 */

const axios = require('axios');

/**
 * Helper to extract a clean title from OpenRouter's response
 * @param {string} text - The raw response text
 * @returns {string} - The cleaned title
 */
function extractCleanTitle(text) {
  // Remove any markdown formatting, option indicators, etc.
  let cleanTitle = text.trim();
  
  // If there are multiple lines with options, take the first option
  if (cleanTitle.includes('Option')) {
    const optionMatch = cleanTitle.match(/Option 1[^:]*:\s*(.+?)(?=Option|\n\n|$)/is);
    if (optionMatch && optionMatch[1]) {
      cleanTitle = optionMatch[1].trim();
    }
  }
  
  // Remove any remaining formatting characters
  cleanTitle = cleanTitle
    .replace(/\*\*/g, '')  // Remove bold markdown
    .replace(/\*/g, '')    // Remove italic markdown
    .replace(/^[-*•]/g, '') // Remove bullet points
    .trim();
  
  return cleanTitle;
}

/**
 * Improve a product title using OpenRouter AI
 * @param {string} title - The original title to improve
 * @returns {Promise<string>} - The improved title
 */
async function improveTitle(title) {
  if (!title) return title;
  
  try {
    const prompt = `Can you change the wording of this title please. The most important thing is to keep the product title. Any superfluous words that describe the product or are attributes of the product can be removed.\n\nOriginal Title: "${title}"\n\nGive me ONLY the improved title with no explanations, options, or formatting. Return just the improved title text.`;
    
    const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
      model: "deepseek/deepseek-chat:free",
      messages: [{ role: 'user', content: prompt }]
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'HTTP-Referer': process.env.FRONTEND_URL || 'http://localhost:3010',
        'X-Title': 'NiceDeals'
      }
    });
    
    let improvedTitle = response.data.choices[0].message.content;
    improvedTitle = extractCleanTitle(improvedTitle);
    
    return improvedTitle || title;
  } catch (error) {
    console.error('Error improving title with OpenRouter:', error);
    return title; // Return original title if AI processing fails
  }
}

/**
 * Generate a product description using OpenRouter AI based on a URL
 * @param {string} url - The product URL to analyze
 * @returns {Promise<string>} - The generated description
 */
async function generateDescription(url) {
  if (!url) return "";
  
  try {
    const timestampedUrl = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;
    const prompt = process.env.AI_DESCRIPTION_PROMPT.replace('{{URL}}', timestampedUrl);
    
    const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
      model: "deepseek/deepseek-chat:free",
      messages: [{ role: 'user', content: prompt }]
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'HTTP-Referer': process.env.FRONTEND_URL || 'http://localhost:3010',
        'X-Title': 'NiceDeals'
      }
    });
    
    let description = response.data.choices[0].message.content;
    description = description
      .replace(/^\*\*Description:?\*\*/i, '')
      .replace(/^Description:?/i, '')
      .replace(/\*\*/g, '')
      .replace(/\*/g, '')
      .replace(/^[-*•]/g, '')
      .trim();
    
    return description;
  } catch (error) {
    console.error('Error generating description with OpenRouter:', error);
    return ""; // Return empty string if AI processing fails
  }
}

// Export the AI service methods
const aiService = {
  improveTitle,
  generateDescription
};

module.exports = { aiService };