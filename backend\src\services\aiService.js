const { GoogleGenerativeAI } = require('@google/generative-ai');
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);

async function improveTitle(originalTitle) {
  try {
    const model = genAI.getGenerativeModel({ model: "gemini-pro" });
    
    const prompt = `
    Improve this product deal title to be more descriptive and SEO-friendly, while keeping it concise:
    "${originalTitle}"
    
    Rules:
    - Keep important product details
    - Include brand name if present
    - Add key specifications if missing
    - Maximum 100 characters
    - Don't add price information
    - Return only the improved title, nothing else
    `;

    const result = await model.generateContent(prompt);
    const improvedTitle = result.response.text().trim();
    
    // Only return if we got a valid response
    if (improvedTitle && improvedTitle.length > 0 && improvedTitle.length <= 100) {
      return improvedTitle;
    }
    
    return originalTitle;
  } catch (error) {
    console.error('Error improving title:', error);
    return originalTitle;
  }
}

async function generateDescription(url, title) {
  try {
    const model = genAI.getGenerativeModel({ model: "gemini-pro" });
    
    const prompt = `
    Generate a concise, informative product deal description based on this title and URL:
    Title: "${title}"
    URL: ${url}
    
    Rules:
    - Focus on key features and benefits
    - Include any relevant specifications
    - Keep it between 100-200 characters
    - Make it engaging but factual
    - Don't mention specific prices
    - Return only the description, nothing else
    `;

    const result = await model.generateContent(prompt);
    const description = result.response.text().trim();
    
    // Only return if we got a valid response
    if (description && description.length >= 100 && description.length <= 200) {
      return description;
    }
    
    return `Check out this great deal on ${title}!`;
  } catch (error) {
    console.error('Error generating description:', error);
    return `Check out this great deal on ${title}!`;
  }
}

module.exports = {
  improveTitle,
  generateDescription
};