/**
 * Email Service
 * A simple placeholder for email functionality.
 * In a production environment, this would use a proper email service like Nodemailer.
 */

const nodemailer = require('nodemailer');

// Initialize the transporter
let transporter;

async function initTransporter() {
  transporter = nodemailer.createTransport({
    host: process.env.BREVO_SMTP_HOST,
    port: process.env.BREVO_PORT,
    secure: false,
    auth: {
      user: process.env.BREVO_USER,
      pass: process.env.BREVO_PASSWORD
    }
  });
}

/**
 * Send a deal alert email (placeholder implementation)
 */
async function sendDealAlert(email, username, keyword, deals) {
  console.log(`[MOCK EMAIL] Alert for ${username} (${email}) about keyword "${keyword}" with ${deals?.length || 0} deals`);
  return { messageId: 'mock-email-id-' + Date.now() };
}

async function sendVerificationEmail(email, token) {
  const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;
  
  await transporter.sendMail({
    from: '"NiceDeals" <<EMAIL>>',
    to: email,
    subject: 'NiceDeals - Verify Your Email Address',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Welcome to NiceDeals!</h1>
        <p>Please click the button below to verify your email address:</p>
        
        <div style="margin: 20px 0;">
          <a href="${verificationUrl}" 
             style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
            Verify Email Address
          </a>
        </div>

        <p style="margin-top: 20px; color: #666;">
          If the button above doesn't work, you can copy and paste this link into your browser:
        </p>
        <p style="background-color: #f3f4f6; padding: 12px; border-radius: 4px; word-break: break-all;">
          ${verificationUrl}
        </p>

        <p style="color: #666; margin-top: 20px;">
          This verification link will expire in 1 hour.
        </p>
      </div>
    `
  });
}

module.exports = {
  initTransporter,
  sendDealAlert,
  sendVerificationEmail
};