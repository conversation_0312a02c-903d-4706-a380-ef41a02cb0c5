const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;

/**
 * Process uploaded image creating both main image and thumbnail
 * @param {<PERSON>uffer} buffer - Image buffer
 * @param {string} imageId - Unique identifier for the image
 * @returns {Promise<Object>} Object containing image paths
 */
async function processImage(buffer, imageId) {
  try {
    // Use absolute paths from project root
    const uploadsDir = path.join(__dirname, '..', '..', 'public', 'uploads', 'deals');
    const thumbnailsDir = path.join(uploadsDir, 'thumbnails');
    
    console.log('Creating directories:', { uploadsDir, thumbnailsDir });

    // Ensure directories exist
    await fs.mkdir(uploadsDir, { recursive: true });
    await fs.mkdir(thumbnailsDir, { recursive: true });

    // Get image metadata
    const metadata = await sharp(buffer).metadata();
    console.log('Image metadata:', metadata);

    // Process main image
    const mainImagePath = path.join(uploadsDir, `${imageId}.webp`);
    console.log('Saving main image to:', mainImagePath);
    
    await sharp(buffer)
      .resize(800, 800, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .webp({ quality: 80 })
      .toFile(mainImagePath);

    // Process thumbnail
    const thumbnailPath = path.join(thumbnailsDir, `${imageId}.webp`);
    console.log('Saving thumbnail to:', thumbnailPath);
    
    await sharp(buffer)
      .resize(200, 200, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .webp({ quality: 70 })
      .toFile(thumbnailPath);

    // Verify files were created
    const mainImageExists = await fs.access(mainImagePath)
      .then(() => true)
      .catch(() => false);
    
    const thumbnailExists = await fs.access(thumbnailPath)
      .then(() => true)
      .catch(() => false);

    console.log('File creation verification:', {
      mainImageExists,
      thumbnailExists
    });

    if (!mainImageExists || !thumbnailExists) {
      throw new Error('Failed to verify image file creation');
    }

    // Return paths for database storage
    const imageUrl = `/uploads/deals/${imageId}.webp`;
    const thumbnailUrl = `/uploads/deals/thumbnails/${imageId}.webp`;

    return {
      image_url: imageUrl,
      thumbnail_url: thumbnailUrl
    };
  } catch (error) {
    console.error('Error processing image:', error);
    throw error;
  }
}

module.exports = {
  processImage
};
