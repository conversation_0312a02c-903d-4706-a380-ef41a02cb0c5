#!/bin/bash

# Get the current script's directory and name
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
SCRIPT_NAME="$(basename "$0")"
SCRIPT_PATH="$SCRIPT_DIR/$SCRIPT_NAME"

# Function to check if the script was updated
check_script_update() {
    # Get the current script's modification time
    local current_mtime=$(stat -c %Y "$SCRIPT_PATH" 2>/dev/null || stat -f %m "$SCRIPT_PATH")
    
    # Pull the latest changes
    echo "📥 Checking for updates to deply script..."
    git -C "$SCRIPT_DIR" pull --quiet
    
    # Get the new modification time
    local new_mtime=$(stat -c %Y "$SCRIPT_PATH" 2>/dev/null || stat -f %m "$SCRIPT_PATH")
    
    # If the script was updated, restart it
    if [ "$current_mtime" != "$new_mtime" ]; then
        echo "🔄 Script was updated. Restarting with new version..."
        exec "$SCRIPT_PATH" "$@"
        exit $?
    fi
}

# Check for updates at the start
check_script_update

# Rest of your script...
APP_DIR=$(pwd)
BACKUP_DIR="$APP_DIR/backups/$(date +%Y%m%d_%H%M%S)"

case $1 in
  setup)
    echo "🚀 Running initial setup for SSR version..."
    
    # Install dependencies
    echo "📦 Installing frontend dependencies..."
    cd "$APP_DIR/frontend"
    npm install
    
    echo "📦 Installing backend dependencies..."
    cd "$APP_DIR/backend"
    npm install
    
    # Setup environment files
    echo "⚙️ Setting up environment files..."
    [ -f "$APP_DIR/backend/.env" ] || cp "$APP_DIR/backend/.env.example" "$APP_DIR/backend/.env"
    [ -f "$APP_DIR/frontend/.env" ] || cp "$APP_DIR/frontend/.env.example" "$APP_DIR/frontend/.env"
    
    # Build the Next.js app
    echo "🏗️  Building Next.js app..."
    cd "$APP_DIR/frontend"
    if ! npm run build; then
      echo "❌ Frontend build failed!"
      exit 1
    fi
    
    # Start services using PM2
    echo "🚀 Starting services..."
    cd "$APP_DIR"
    pm2 start ecosystem-ssr.config.js
    pm2 save
    
    # Set up PM2 to start on system boot
    pm2 startup
    pm2 save
    
    echo "✅ SSR Setup complete! Your app should be running."
    echo "🔍 Check status with: pm2 status"
    echo "📝 View logs with: pm2 logs"
    ;;

  update)
    echo "🔄 Running update for SSR version..."
    
    # Backup current version (excluding node_modules)
    echo "📦 Backing up current version to $BACKUP_DIR..."
    mkdir -p "$BACKUP_DIR"
    rsync -a --exclude='node_modules' --exclude='.next' "$APP_DIR/frontend" "$BACKUP_DIR/"
    rsync -a --exclude='node_modules' "$APP_DIR/backend" "$BACKUP_DIR/"
    
    # Check for script updates again before proceeding
    check_script_update
    
    # Install dependencies
    echo "📦 Installing frontend dependencies..."
    cd "$APP_DIR/frontend"
    npm install
    
    echo "📦 Installing backend dependencies..."
    cd "$APP_DIR/backend"
    npm install --production
    
    # Rebuild frontend
    echo "🏗️  Rebuilding Next.js app..."
    cd "$APP_DIR/frontend"
    if ! npm run build; then
      echo "❌ Frontend build failed!"
      exit 1
    fi
    
    # Restart services
    echo "🔄 Restarting services..."
    cd "$APP_DIR"
    pm2 reload ecosystem-ssr.config.js
    pm2 save
    
    echo "✅ SSR Update complete!"
    pm2 ls
    ;;

  status)
    pm2 ls
    ;;

  logs)
    pm2 logs
    ;;

  *)
    echo "Usage: $0 {setup|update|status|logs}"
    echo "  setup   - Initial setup of the SSR application"
    echo "  update  - Update the application to the latest version"
    echo "  status  - Check application status"
    echo "  logs    - View application logs"
    exit 1
    ;;
esac