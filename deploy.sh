#!/bin/bash

APP_DIR=$(pwd)
BACKUP_DIR="$APP_DIR/backups/$(date +%Y%m%d_%H%M%S)"

case $1 in
  setup)
    echo "🚀 Running initial setup..."
    
    # Install dependencies
    echo "📦 Installing frontend dependencies..."
    cd "$APP_DIR/frontend"
    npm install
    
    echo "📦 Installing backend dependencies..."
    cd "$APP_DIR/backend"
    npm install
    
    # Setup environment files
    echo "⚙️ Setting up environment files..."
    cp "$APP_DIR/backend/.env.production" "$APP_DIR/backend/.env"
    cp "$APP_DIR/frontend/.env.production" "$APP_DIR/frontend/.env"
    
    # Start services
    echo "🚀 Starting services..."
    cd "$APP_DIR"
    pm2 start ecosystem.config.js
    pm2 save
    
    echo "✅ Setup complete!"
    ;;
    
  update)
    echo "🔄 Running update..."
    
    # Backup current version
    echo "📦 Backing up current version..."
    mkdir -p "$BACKUP_DIR"
    rsync -a --exclude='node_modules' "$APP_DIR/frontend" "$BACKUP_DIR/"
    rsync -a --exclude='node_modules' "$APP_DIR/backend" "$BACKUP_DIR/"
    
    # Pull latest changes
    echo "📥 Pulling latest changes..."
    cd "$APP_DIR"
    git pull

    # Setup environment files
    echo "⚙️ Setting up environment files..."
    cp "$APP_DIR/backend/.env.production" "$APP_DIR/backend/.env"
    cp "$APP_DIR/frontend/.env.production" "$APP_DIR/frontend/.env"
    
    # Install dependencies and build frontend
    echo "📦 Installing frontend dependencies..."
    cd "$APP_DIR/frontend"
    npm install
    if ! npm run build; then
      echo "❌ Frontend build failed!"
      exit 1
    fi
    
    echo "📦 Installing backend dependencies..."
    cd "$APP_DIR/backend"
    npm install --production
    
    # Restart services
    echo "🔄 Restarting services..."
    cd "$APP_DIR"
    # Stop the services first
    pm2 stop nicedeals-frontend nicedeals-backend 2>/dev/null || true
    # Delete the services
    pm2 delete nicedeals-frontend nicedeals-backend 2>/dev/null || true
    # Start the services with the updated configuration
    pm2 start ecosystem.config.js
    # Save the PM2 configuration
    pm2 save
    # Display PM2 status
    pm2 ls
    
    echo "✅ Update complete!"
    ;;
    
  *)
    echo "Usage: $0 {setup|update}"
    exit 1
    ;;
esac