module.exports = {
  apps: [{
    name: 'nicedeals-ssr',
    script: 'node_modules/next/dist/bin/next',
    args: 'start -p 3010',
    cwd: './frontend',
    instances: 'max',
    exec_mode: 'cluster',
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3010
    }
  }, {
    name: 'nicedeals-backend',
    script: 'src/index.js',
    cwd: './backend',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
};