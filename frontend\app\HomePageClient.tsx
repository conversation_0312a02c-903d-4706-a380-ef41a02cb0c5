'use client';

import { Deal } from '@/types';
import Hero from '@/components/Hero';
import CategoryList from '@/components/CategoryList';
import FeaturedDeals from '@/components/FeaturedDeals';
import InfoSection from '@/components/InfoSection';
import ScrollToTopButton from '@/components/ScrollToTopButton';

interface HomePageClientProps {
  gettingWarmDeals: Deal[];
  newestDeals: Deal[];
}

export default function HomePageClient({ gettingWarmDeals, newestDeals }: HomePageClientProps) {
  return (
    <>
      <Hero />
      <main className="space-y-12 md:space-y-16 pb-16">
        <CategoryList />
        
        <FeaturedDeals 
          gettingWarmDeals={gettingWarmDeals}
          newestDeals={newestDeals}
        />

        <InfoSection />
      </main>

      <ScrollToTopButton />
    </>
  );
}
