'use client';

import React, { useState, useEffect } from 'react';
import { AdminCategory } from '@/types/admin';
import adminService from '@/services/adminService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

export default function AdminCategoriesPage() {
  const [categories, setCategories] = useState<AdminCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentCategory, setCurrentCategory] = useState<AdminCategory | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
  });

  // Fetch categories
  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('[AdminCategoriesPage] Fetching categories for page:', page);
      const apiResult = await adminService.getCategories(page);

      console.log('[AdminCategoriesPage] Response from adminService.getCategories:', apiResult);

      // Check if categories data and pagination data exist as expected
      if (apiResult && apiResult.success && apiResult.data && Array.isArray(apiResult.data.categories) && apiResult.data.pagination) {
        console.log('[AdminCategoriesPage] Setting categories with:', apiResult.data.categories);
        setCategories(apiResult.data.categories);

        console.log('[AdminCategoriesPage] Setting totalPages with:', apiResult.data.pagination.totalPages);
        setTotalPages(apiResult.data.pagination.totalPages);
      } else {
        console.error('[AdminCategoriesPage] API response structure incorrect or data missing:', apiResult);
        setError('Failed to parse categories or pagination from API response.');
        setCategories([]); // Clear categories on unexpected structure
        setTotalPages(1); // Reset total pages
      }
    } catch (error) {
      console.error('[AdminCategoriesPage] Error fetching categories:', error);
      setError('Failed to load categories. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, [page]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    
    // Auto-generate slug from name if slug field wasn't manually edited
    if (name === 'name' && (!formData.slug || formData.slug === slugify(formData.name))) {
      setFormData(prev => ({
        ...prev,
        slug: slugify(value),
      }));
    }
  };

  // Slugify text for URLs
  const slugify = (text: string): string => {
    return text
      .toString()
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-');
  };

  // Add a new category
  const handleAddCategory = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await adminService.createCategory(formData);
      toast.success('Category created successfully!');
      fetchCategories();
      setIsModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error creating category:', error);
      toast.error('Failed to create category.');
    }
  };

  // Update existing category
  const handleUpdateCategory = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentCategory) return;

    try {
      await adminService.updateCategory(currentCategory.id, formData);
      toast.success('Category updated successfully!');
      fetchCategories();
      setIsModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error updating category:', error);
      toast.error('Failed to update category.');
    }
  };

  // Delete category
  const handleDeleteCategory = async () => {
    if (!currentCategory) return;

    try {
      await adminService.deleteCategory(currentCategory.id);
      toast.success('Category deleted successfully!');
      fetchCategories();
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting category:', error);
      toast.error('Failed to delete category.');
    }
  };

  // Open edit modal
  const openEditModal = (category: AdminCategory) => {
    setCurrentCategory(category);
    setFormData({
      name: category.name,
      slug: category.slug,
    });
    setIsModalOpen(true);
  };

  // Open delete modal
  const openDeleteModal = (category: AdminCategory) => {
    setCurrentCategory(category);
    setIsDeleteModalOpen(true);
  };

  // Open add modal
  const openAddModal = () => {
    setCurrentCategory(null);
    resetForm();
    setIsModalOpen(true);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
    });
    setCurrentCategory(null);
  };

  return (
    <>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Categories</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage the categories used to organize deals
          </p>
        </div>
        <Button onClick={openAddModal}>
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Category
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
          {error}
        </div>
      )}

      {/* Categories table */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Slug</TableHead>
              <TableHead>Deals</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : categories.length > 0 ? (
              categories.map((category) => (
                <TableRow key={category.id}>
                  <TableCell>{category.id}</TableCell>
                  <TableCell className="font-medium">{category.name}</TableCell>
                  <TableCell>{category.slug}</TableCell>
                  <TableCell>{category.dealsCount}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditModal(category)}
                      >
                        <PencilIcon className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        onClick={() => openDeleteModal(category)}
                      >
                        <TrashIcon className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                  No categories found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-between items-center mt-4">
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.max(p - 1, 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className="text-sm text-gray-600">
            Page {page} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.min(p + 1, totalPages))}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Add/Edit Category Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="">
          <DialogHeader>
            <DialogTitle>{currentCategory ? 'Edit Category' : 'Add Category'}</DialogTitle>
            <DialogDescription>
              {currentCategory
                ? 'Update the category details below.'
                : 'Enter the details for the new category.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={currentCategory ? handleUpdateCategory : handleAddCategory}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <label htmlFor="name" className="text-sm font-medium">
                  Name
                </label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Category name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="slug" className="text-sm font-medium">
                  Slug
                </label>
                <Input
                  id="slug"
                  name="slug"
                  placeholder="category-slug"
                  value={formData.slug}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" variant="default">
                {currentCategory ? 'Update' : 'Create'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Category</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the category "{currentCategory?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCategory}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
