'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'react-hot-toast';
import { 
  PLACEHOLDER_IMAGE,
  getThumbnailUrl, 
  isExternalUrl,
  handleImageError
} from '@/utils/imageUtils';

// Backend URL for uploads (port 5010)
const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL?.replace('/api', '') || 'http://localhost:5010';

// Manually adding these functions since they aren't exported from imageUtils
const getImageUrl = (url: string): string => {
  if (!url) return PLACEHOLDER_IMAGE;
  // NEVER show remote images, use PLACEHOLDER_IMAGE instead
  if (url.startsWith('http://') || url.startsWith('https://')) return PLACEHOLDER_IMAGE;
  if (url.startsWith('/uploads/')) return `${BACKEND_URL}${url}`;
  return url;
};

const getAdminImageUrl = (url: string): string => {
  if (!url) return PLACEHOLDER_IMAGE;
  // NEVER show remote images even in admin, use PLACEHOLDER_IMAGE instead
  if (url.startsWith('http://') || url.startsWith('https://')) return PLACEHOLDER_IMAGE;
  if (url.startsWith('/uploads/')) return `${BACKEND_URL}${url}`;
  return url;
};
import { useCategories } from '@/hooks/useCategories';
import { useStores } from '@/hooks/useStores';
import { useImageLocalization } from '@/hooks/useImageLocalization';
import { Category, Store } from '@/types';
import adminService from '../../../../services/adminService';
import aiService from '@/services/aiService';
import { SparklesIcon, DocumentTextIcon, TrashIcon } from '@heroicons/react/24/outline';

// SVG for Twitter/X logo
const XLogo: React.FC = () => (
  <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
);

// Declare the window interface extension for TypeScript
declare global {
  interface Window {
    isCouponEnabled?: boolean;
  }
}

// Form validation schema
const dealSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  price: z.number().min(0, 'Price must be positive'),
  originalPrice: z.number().min(0, 'Original price must be positive').nullable().optional(),
  url: z.string().url('Must be a valid URL'),
  status: z.enum(['active', 'expired', 'deleted', 'pending']),
  storeId: z.number().int().positive('Store is required'),
  categoryId: z.number().int().positive('Category is required'),
  coupon: z.string().max(50, 'Coupon code must be at most 50 characters')
    .refine(
      // Type the parameters properly
      (val: string) => {
        // Check if window is defined (client-side only)
        if (typeof window !== 'undefined' && window.isCouponEnabled && (!val || val.trim() === '')) {
          return false;
        }
        return true;
      },
      { message: 'Coupon code is required when enabled' }
    )
    .optional(),
  imageUrl: z.string()
    .min(1, 'Image URL is required')
    .refine(
      (val) => val.startsWith('http') || val.startsWith('https') || val.startsWith('/uploads/'),
      'Image URL must be a valid URL or a valid uploads path'
    )
});

type DealFormData = z.infer<typeof dealSchema>;

export default function AdminEditDealPage() {
  const params = useParams();
  const id = params.id as string;
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [currentImageUrl, setCurrentImageUrl] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [isImprovingTitle, setIsImprovingTitle] = useState(false);
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isCouponEnabled, setIsCouponEnabled] = useState(false);
  const { categories } = useCategories();
  const { stores } = useStores();
  const { localizeImage, isProcessingImage } = useImageLocalization();
  
  // Track when stores and categories are loaded
  const [categoriesLoaded, setCategoriesLoaded] = useState(false);
  const [storesLoaded, setStoresLoaded] = useState(false);
  
  // Update states when data is loaded
  useEffect(() => {
    console.log('Categories loaded:', categories?.length || 0);
    if (categories?.length > 0) setCategoriesLoaded(true);
  }, [categories]);
  
  useEffect(() => {
    console.log('Stores loaded:', stores?.length || 0);
    if (stores?.length > 0) setStoresLoaded(true);
  }, [stores]);
  
  // Debug loading state
  useEffect(() => {
    console.log('Loading state:', { 
      isLoading, 
      categoriesLoaded, 
      storesLoaded,
      id
    });
  }, [isLoading, categoriesLoaded, storesLoaded, id]);

  const { register, handleSubmit, reset, setValue, watch, getValues, formState: { errors, isSubmitting } } = useForm<DealFormData>({
    resolver: zodResolver(dealSchema)
  });

  useEffect(() => {
    // Fetch the deal regardless of category and store loading state
    // We'll handle those separately
    const fetchDeal = async () => {
      try {
        setIsLoading(true);
        
        // If it's a new deal, we don't need to fetch anything
        if (id === 'new') {
          reset({
            title: '',
            description: '',
            price: 0,
            originalPrice: null,
            url: '',
            status: 'pending',
            storeId: 0,
            categoryId: 0,
            coupon: '',
            imageUrl: ''
          });
          setIsLoading(false);
          return;
        }

        console.log(`Fetching deal ${id}...`);
        
        // Check if the deal exists first
        try {
          const dealExists = await adminService.checkDealExists(Number(id));
          if (!dealExists) {
            toast.error('Deal not found');
            router.push('/admin/deals');
            setIsLoading(false);
            return;
          }
        } catch (error) {
          console.error('Error checking if deal exists:', error);
          // Continue anyway to try fetching the deal
        }
        
        const response = await adminService.getDeal(Number(id));
        console.log('Fetched deal data:', response); // Debug log
        
        // The API response may have a different structure, handle both formats
        const dealData = response.deal || response;
        console.log('Extracted deal data:', dealData);
        
        // Make sure we have all required data before resetting the form
        if (!dealData) {
          console.error('Invalid deal structure received:', response);
          throw new Error('Invalid deal data received');
        }
        
        // Store the deal data for use in the category/store selection effect
        setLoadedDealData(dealData);
        
        // The response might have store/category as objects or just IDs
        let storeId, categoryId;
        
        console.log('Checking store data:', dealData.store, dealData.store_id);
        console.log('Checking category data:', dealData.category, dealData.category_id);
        
        // Handle both object format and direct ID format
        if (dealData.store && typeof dealData.store === 'object') {
          storeId = Number(dealData.store.id);
        } else if (dealData.store_id) {
          storeId = Number(dealData.store_id);
        } else {
          console.error('No store ID found in deal data');
          storeId = 0; // Default to avoid breaking the form
        }
        
        if (dealData.category && typeof dealData.category === 'object') {
          categoryId = Number(dealData.category.id);
        } else if (dealData.category_id) {
          categoryId = Number(dealData.category_id);
        } else {
          console.error('No category ID found in deal data');
          categoryId = 0; // Default to avoid breaking the form
        }
        
        console.log('Resolved IDs:', { storeId, categoryId });
        
        if (isNaN(storeId) || isNaN(categoryId)) {
          console.error('Invalid IDs after parsing:', { storeId, categoryId, dealData });
          // Don't throw error, just set to 0 to avoid breaking
          storeId = storeId || 0;
          categoryId = categoryId || 0;
        }
        
        // Set coupon enabled if there's a coupon code
        if (dealData.coupon && dealData.coupon.trim() !== '') {
          setIsCouponEnabled(true);
        }

        // Reset form with validated data
        // Nested API response structure matches our form fields already
        reset({
          title: dealData.title || '',
          description: dealData.description || '',
          price: dealData.price || 0,
          originalPrice: dealData.originalPrice || null,
          url: dealData.url || '',
          status: dealData.status || 'pending',
          storeId: storeId,
          categoryId: categoryId,
          coupon: dealData.coupon || '',
          imageUrl: dealData.imageUrl || ''
        });
        
        setCurrentImageUrl(dealData.imageUrl || '');
        setShowPreview(!!dealData.imageUrl);
        
        // If there's a coupon code, enable the coupon field
        if (dealData.coupon && dealData.coupon.trim() !== '') {
          setIsCouponEnabled(true);
        }
      } catch (err: any) {
        console.error('Error fetching deal:', err);
        toast.error(err.message || 'Failed to load deal data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDeal();
  }, [id, reset]);

  // Keep track of the loaded deal data
  const [loadedDealData, setLoadedDealData] = useState<any>(null);
  
  // Update the store and category selections whenever the deal data changes or the dropdowns are loaded
  useEffect(() => {
    if (!loadedDealData) return;
    console.log('Updating form with loaded deal data:', loadedDealData);
    
    // Attempt to set store and category values even if not yet in the dropdown
    // They'll be selected once the options are available
    let storeId, categoryId;
    
    if (loadedDealData.store && typeof loadedDealData.store === 'object') {
      storeId = Number(loadedDealData.store.id);
    } else if (loadedDealData.store_id) {
      storeId = Number(loadedDealData.store_id);
    }
    
    if (loadedDealData.category && typeof loadedDealData.category === 'object') {
      categoryId = Number(loadedDealData.category.id);
    } else if (loadedDealData.category_id) {
      categoryId = Number(loadedDealData.category_id);
    }
    
    if (storeId && !isNaN(storeId)) {
      console.log('Setting store ID to:', storeId);
      setValue('storeId', storeId);
    }
    
    if (categoryId && !isNaN(categoryId)) {
      console.log('Setting category ID to:', categoryId);
      setValue('categoryId', categoryId);
    }
    
    // Important: Set loading to false even if we don't have categories/stores yet
    // This will allow the form to be shown while the dropdowns are still loading
    setIsLoading(false);
    
  }, [loadedDealData, setValue, storesLoaded, categoriesLoaded]);
  
  // Watch imageUrl changes
  const imageUrl = watch('imageUrl');
  
  useEffect(() => {
    console.log('Current image URL:', imageUrl); // Debug log
    console.log('Is external URL:', isExternalUrl(imageUrl)); // Debug log
    console.log('Constructed URL:', getImageUrl(imageUrl)); // Debug log
    setCurrentImageUrl(imageUrl || '');
    setShowPreview(!!imageUrl);
  }, [imageUrl]);

  // Watch these values for conditional rendering
  const description = watch('description');
  const url = watch('url');
  const handleLocalizeImage = async () => {
    try {
      const imageUrl = getValues('imageUrl');
      if (!imageUrl) {
        toast.error('Please enter an image URL first');
        return;
      }

      const localizedImageUrl = await localizeImage(imageUrl);
      if (localizedImageUrl) {
        setValue('imageUrl', localizedImageUrl);
        setCurrentImageUrl(localizedImageUrl);
        toast.success('Image localized successfully');
      }
    } catch (error) {
      console.error('Error localizing image:', error);
      toast.error('Failed to localize image');
    }
  };

  const handleImproveTitle = async () => {
    const currentTitle = getValues('title');
    
    if (!currentTitle) {
      toast.error('Please enter a title first');
      return;
    }
    
    try {
      setIsImprovingTitle(true);
      
      // Note: In the CSR version, this accepted an object,
      // but our current implementation may expect just the title
      const improvedTitle = await aiService.improveTitle(currentTitle);
      
      if (improvedTitle) {
        setValue('title', improvedTitle);
        toast.success('Title improved!');
      } else {
        toast.error('Could not improve title');
      }
    } catch (error) {
      console.error('Error improving title:', error);
      toast.error('Failed to improve title');
    } finally {
      setIsImprovingTitle(false);
    }
  };

  const handleGenerateDescription = async () => {
    const currentTitle = getValues('title');
    const currentUrl = getValues('url');
    
    if (!currentTitle) {
      toast.error('Please enter a title first');
      return;
    }
    
    try {
      setIsGeneratingDescription(true);
      
      // Note: In the CSR version, this used currentUrl and title
      const generatedDescription = await aiService.generateDescription(currentUrl, currentTitle);
      
      if (generatedDescription) {
        setValue('description', generatedDescription);
        toast.success('Description generated!');
      } else {
        toast.error('Could not generate description');
      }
    } catch (error) {
      console.error('Error generating description:', error);
      toast.error('Failed to generate description');
    } finally {
      setIsGeneratingDescription(false);
    }
  };

  const onSubmit = async (data: DealFormData) => {
    // If the coupon field is disabled, make sure to clear the value
    if (!isCouponEnabled) {
      data.coupon = '';
    } else if (isCouponEnabled && (!data.coupon || data.coupon.trim() === '')) {
      // If coupon is enabled but empty, show an error and prevent submission
      toast.error('Coupon code is required when enabled');
      return;
    }
    try {
      if (id === 'new') {
        // For new deals
        await adminService.createDeal(data);
        toast.success('Deal created successfully');
      } else {
        // For existing deals
        await adminService.updateDeal(Number(id), data);
        toast.success('Deal updated successfully');
      }
      router.push('/admin/deals');
    } catch (error) {
      console.error('Error saving deal:', error);
      toast.error('Failed to save deal');
    }
  };
  
  const handleDelete = async () => {
    try {
      await adminService.deleteDeal(Number(id));
      toast.success('Deal deleted successfully');
      router.push('/admin/deals');
    } catch (error) {
      console.error('Error deleting deal:', error);
      toast.error('Failed to delete deal');
    }
  };

  const handlePromoteOnTwitter = () => {
    const { title, price, originalPrice, status, imageUrl, storeId } = getValues();
    
    // Get store name
    const selectedStore = stores.find((store: Store) => store.id === storeId);
    const storeName = selectedStore?.name || '';
    
    // Format the price message based on whether there's an original price
    let priceMessage;
    if (originalPrice && originalPrice > 0) {
      priceMessage = `Price currently down to £${price} from £${originalPrice}`;
    } else {
      priceMessage = `Price just £${price} currently`;
    }
    
    // Create the tweet text in the exact format requested
    const tweetText = `Nicedeals UK presents ${title} on ${storeName.replace(/\s+/g, '')}\n\n${priceMessage}\n\nhttps://www.nicedeals.app/dealDetail/${id}\n\n#deal #ukdeals #bargains #${storeName.replace(/\s+/g, '')}`;
    
    // Open Twitter intent URL
    window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(tweetText)}`, '_blank');
  };
  
  return (
      <div className="mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">
          {id === 'new' ? 'Create New Deal' : 'Edit Deal'}
        </h1>
        
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        ) : (
          <div className="bg-white shadow-md rounded-lg p-6">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Title Field Group */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
                <div className="md:col-span-3">
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                    Title <span className="text-red-500">*</span>
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <input
                      type="text"
                      id="title"
                      {...register('title')}
                      className={`block w-full rounded-md ${errors.title ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500'} sm:text-sm`}
                    />
                    {errors.title && (
                      <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-end md:mt-6">
                  <button
                    type="button"
                    onClick={handleImproveTitle}
                    disabled={isImprovingTitle}
                    className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <SparklesIcon className="h-4 w-4 mr-2" />
                    {isImprovingTitle ? 'Improving...' : 'Improve Title'}
                  </button>
                </div>
              </div>

              {/* Description Field Group */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
                <div className="md:col-span-3">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="description"
                      rows={4}
                      {...register('description')}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                    />
                    {errors.description && (
                      <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-start md:mt-8">
                  {(!description || description.trim() === '') && url && (
                    <button
                      type="button"
                      onClick={handleGenerateDescription}
                      disabled={isGeneratingDescription || !url}
                      className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Use AI to generate a description from the product URL"
                    >
                      <DocumentTextIcon className="h-4 w-4 mr-2" />
                      {isGeneratingDescription ? 'Generating...' : 'Grab a Description'}
                    </button>
                  )}
                </div>
              </div>

              {/* Price Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                    Price (£) <span className="text-red-500">*</span>
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">£</span>
                    </div>
                    <input
                      type="number"
                      id="price"
                      step="0.01"
                      min="0"
                      {...register('price', { valueAsNumber: true })}
                      className={`block w-full pl-7 rounded-md ${errors.price ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500'} sm:text-sm`}
                    />
                    {errors.price && (
                      <p className="mt-1 text-sm text-red-600">{errors.price.message}</p>
                    )}
                  </div>
                </div>
                <div>
                  <label htmlFor="originalPrice" className="block text-sm font-medium text-gray-700 mb-1">
                    Original Price (£)
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">£</span>
                    </div>
                    <input
                      type="number"
                      id="originalPrice"
                      step="0.01"
                      min="0"
                      {...register('originalPrice', { valueAsNumber: true })}
                      className="block w-full pl-7 rounded-md border-gray-300 focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                    />
                    {errors.originalPrice && (
                      <p className="mt-1 text-sm text-red-600">{errors.originalPrice.message}</p>
                    )}
                  </div>
                </div>
              </div>
              {/* URL and Coupon */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
                    Deal URL <span className="text-red-500">*</span>
                  </label>
                  <div className="mt-1">
                    <input
                      type="url"
                      id="url"
                      {...register('url')}
                      className={`block w-full rounded-md ${errors.url ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500'} sm:text-sm`}
                    />
                    {errors.url && (
                      <p className="mt-1 text-sm text-red-600">{errors.url.message}</p>
                    )}
                  </div>
                </div>
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <label htmlFor="coupon" className="block text-sm font-medium text-gray-700">
                      Coupon Code
                    </label>
                    <div className="flex items-center">
                      <span className="text-xs text-gray-500 mr-2">{isCouponEnabled ? 'Enabled' : 'Disabled'}</span>
                      <label className="inline-flex relative items-center cursor-pointer">
                        <input 
                          type="checkbox" 
                          className="sr-only peer"
                          checked={isCouponEnabled}
                          onChange={() => {
                            const newState = !isCouponEnabled;
                            // Update our state
                            setIsCouponEnabled(newState);
                            // Also set a global window variable for the schema validation
                            if (typeof window !== 'undefined') {
                              window.isCouponEnabled = newState;
                            }
                          }} 
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary-600"></div>
                      </label>
                    </div>
                  </div>
                  <div className="mt-1">
                    <input
                      type="text"
                      id="coupon"
                      {...register('coupon')}
                      disabled={!isCouponEnabled}
                      className={`block w-full rounded-md ${!isCouponEnabled ? 'bg-gray-100 text-gray-500' : ''} ${errors.coupon ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500'} sm:text-sm`}
                    />
                    {errors.coupon && (
                      <p className="mt-1 text-sm text-red-600">{errors.coupon.message}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Category and Store */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 mb-1">
                    Category <span className="text-red-500">*</span>
                  </label>
                  <div className="mt-1">
                    <select
                      id="categoryId"
                      {...register('categoryId', { valueAsNumber: true })}
                      className={`block w-full rounded-md ${errors.categoryId ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500'} sm:text-sm`}
                      defaultValue={0}
                    >
                      <option value="0">Select a category</option>
                      {categories?.map((category: Category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                    {errors.categoryId && (
                      <p className="mt-1 text-sm text-red-600">{errors.categoryId.message}</p>
                    )}
                  </div>
                </div>
                <div>
                  <label htmlFor="storeId" className="block text-sm font-medium text-gray-700 mb-1">
                    Store <span className="text-red-500">*</span>
                  </label>
                  <div className="mt-1">
                    <select
                      id="storeId"
                      {...register('storeId', { valueAsNumber: true })}
                      className={`block w-full rounded-md ${errors.storeId ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500'} sm:text-sm`}
                      defaultValue={0}
                    >
                      <option value="0">Select a store</option>
                      {stores?.map((store: Store) => (
                        <option key={store.id} value={store.id}>
                          {store.name}
                        </option>
                      ))}
                    </select>
                    {errors.storeId && (
                      <p className="mt-1 text-sm text-red-600">{errors.storeId.message}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Status Field */}
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status <span className="text-red-500">*</span>
                </label>
                <div className="mt-1">
                  <select
                    id="status"
                    {...register('status')}
                    className={`block w-full rounded-md ${errors.status ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500'} sm:text-sm`}
                  >
                    <option value="pending">Pending</option>
                    <option value="active">Active</option>
                    <option value="expired">Expired</option>
                    <option value="deleted">Deleted</option>
                  </select>
                  {errors.status && (
                    <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
                  )}
                </div>
              </div>

              {/* Image URL Field */}
              <div>
                <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-1">
                  Image URL <span className="text-red-500">*</span>
                </label>
                <div className="mt-1 flex space-x-3">
                  <div className="flex-grow">
                    <input
                      type="text"
                      id="imageUrl"
                      {...register('imageUrl')}
                      className={`block w-full rounded-md ${errors.imageUrl ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500'} sm:text-sm`}
                    />
                    {errors.imageUrl && (
                      <p className="mt-1 text-sm text-red-600">{errors.imageUrl.message}</p>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={handleLocalizeImage}
                    disabled={isProcessingImage}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                  >
                    {isProcessingImage ? 'Processing...' : 'Localize'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowPreview(!showPreview)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    {showPreview ? 'Hide Preview' : 'Show Preview'}
                  </button>
                </div>
                
                {/* Image Preview */}
                {showPreview && currentImageUrl && (
                  <div className="mt-4 border border-gray-200 rounded-lg p-4">
                    <p className="text-sm font-medium text-gray-700 mb-2">Image Preview:</p>
                    <div className="flex items-center justify-center bg-gray-100 rounded-lg overflow-hidden" style={{ height: '200px' }}>
                      <img
                        src={isExternalUrl(currentImageUrl) ? PLACEHOLDER_IMAGE : getAdminImageUrl(currentImageUrl)}
                        alt="Deal preview"
                        className="max-h-full max-w-full object-contain"
                        onError={handleImageError}
                      />
                    </div>
                    <p className="mt-2 text-xs text-gray-500 truncate">{currentImageUrl}</p>
                  </div>
                )}
              </div>
              {/* Form Buttons */}
              <div className="flex justify-between pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowDeleteModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <TrashIcon className="h-5 w-5 mr-2" />
                  Delete Deal
                </button>
                
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={() => router.push('/admin/deals')}
                    className="btn btn-secondary"
                  >
                    Cancel
                  </button>
                  {/* Only show "Promote on X" button if status is active and image is local */}
                  {watch('status') === 'active' && !isExternalUrl(currentImageUrl) && currentImageUrl && (
                    <button
                      type="button"
                      onClick={handlePromoteOnTwitter}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                    >
                      <XLogo />
                      Promote on X
                    </button>
                  )}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="btn btn-primary"
                  >
                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </div>
            </form>

            {/* Delete Confirmation Modal */}
            {showDeleteModal && (
              <div className="fixed inset-0 z-10 overflow-y-auto">
                <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                  <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                  <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                    <div className="sm:flex sm:items-start">
                      <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <TrashIcon className="h-6 w-6 text-red-600" />
                      </div>
                      <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 className="text-lg leading-6 font-medium text-gray-900">
                          Delete Deal
                        </h3>
                        <div className="mt-2">
                          <p className="text-sm text-gray-500">
                            Are you sure you want to delete this deal? This action cannot be undone and will remove all associated data including images and backup records.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                      <button
                        type="button"
                        className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                        onClick={handleDelete}
                      >
                        Delete
                      </button>
                      <button
                        type="button"
                        className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:w-auto sm:text-sm"
                        onClick={() => setShowDeleteModal(false)}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
}
