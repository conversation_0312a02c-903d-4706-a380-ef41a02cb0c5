'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { PlusIcon, PencilIcon, TrashIcon, CheckIcon } from '@heroicons/react/24/outline';
import { getCategories } from '@/services/categoryService';
import { getStores } from '@/services/storeService';
import { formatDate } from '@/utils/formatters';
import Pagination from '@/components/common/Pagination';
import AlertModal from '@/components/common/AlertModal'; // Refreshed import
import { Deal, Category, Store, DealFilters, ProcessingProgress } from '@/types';
import { getThumbnailUrl, PLACEHOLDER_IMAGE, isExternalUrl } from '@/utils/imageUtils';
import toast from 'react-hot-toast';
import adminService from '@/services/adminService';

export default function AdminDealsPage() {
  // Track initial render and prevent URL updates during init
  const initialRenderComplete = useRef(false);
  const isUpdatingFromUrl = useRef(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize state from URL, but only on component mount
  const initialPage = parseInt(searchParams.get('page') || '1', 10);
  const initialStatus = searchParams.get('status') || 'all';
  const initialCategory = searchParams.get('category') || 'all';
  const initialStore = searchParams.get('store') || 'all';
  const initialSearchTerm = searchParams.get('search') || '';
  const initialSort = searchParams.get('sort') || 'created_desc'; // Default sort parameter

  // All state variables
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [itemsPerPage] = useState(30);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deleteModalData, setDeleteModalData] = useState<{ title: string; message: string; onConfirm: () => void } | null>(null);
  const [dealToDelete, setDealToDelete] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState<ProcessingProgress>({ total: 0, completed: 0 });
  const [batchActionType, setBatchActionType] = useState<'activate' | 'delete' | null>(null);
  const [selectedDeals, setSelectedDeals] = useState<Set<string>>(new Set());
  const [categories, setCategories] = useState<Category[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [sortConfig, setSortConfig] = useState({ key: initialSort.split('_')[0], direction: initialSort.split('_')[1] });
  // New filter states
  const [statusFilter, setStatusFilter] = useState<string>(initialStatus);
  const [categoryFilter, setCategoryFilter] = useState<string>(initialCategory);
  const [storeFilter, setStoreFilter] = useState<string>(initialStore);
  const [batchDeleteModalOpen, setBatchDeleteModalOpen] = useState(false); // Added for batch delete confirmation
  const [showBatchProgressModal, setShowBatchProgressModal] = useState(false); // Added for progress modal

  // Function to sync URL with current state
  const updateUrlFromState = useCallback(() => {
    // Skip URL updates during the initial render or when updating from URL
    if (!initialRenderComplete.current || isUpdatingFromUrl.current) return;

    const params = new URLSearchParams();
    
    if (currentPage > 1) params.set('page', currentPage.toString());
    if (sortConfig.key) params.set('sort', `${sortConfig.key}_${sortConfig.direction}`);
    if (searchTerm) params.set('search', searchTerm);
    if (statusFilter !== 'all') params.set('status', statusFilter);
    if (categoryFilter !== 'all') params.set('category', categoryFilter);
    if (storeFilter !== 'all') params.set('store', storeFilter);
    
    // Use router.replace to avoid browser history build-up
    router.push(`/admin/deals?${params.toString()}`);
  }, [currentPage, sortConfig, searchTerm, statusFilter, categoryFilter, storeFilter, router]);

  // Fetch all deals based on current filters and pagination
  const fetchDeals = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Create sort parameter that includes both field and direction
      const sortParam = sortConfig.key && sortConfig.direction ? 
        `${sortConfig.key}_${sortConfig.direction}` : undefined;
        
      const adminFilters: Omit<DealFilters, 'page' | 'limit'> = {
        search: searchTerm || undefined,
        sort: sortParam as any, // Using type assertion to avoid TypeScript error
        status: statusFilter !== 'all' ? statusFilter as DealFilters['status'] : undefined,
        category: categoryFilter !== 'all' ? parseInt(categoryFilter, 10) : undefined,
        store: storeFilter !== 'all' ? parseInt(storeFilter, 10) : undefined,
      };
      
      console.log(`DEBUG: Sending sort parameter: ${sortParam}`);
      
      console.log('Fetching deals with filters:', adminFilters, 'Page:', currentPage, 'Items per page:', itemsPerPage);
      
      // Convert category and store to strings if they exist
      const filters = {
        ...adminFilters,
        category: adminFilters.category?.toString(),
        store: adminFilters.store?.toString(),
        page: currentPage,
        limit: itemsPerPage
      };
      
      const response = await adminService.getDeals(filters);
      console.log('Deals response in AdminDealsPage:', response);
      
      if (response && response.data) { // Check for response.data instead of response.deals
        console.log('Setting deals:', response.data);
        setDeals(response.data);
        setTotalPages(response.totalPages || 1);
        setTotalCount(response.totalCount || 0);
      } else {
        console.error('No deals data in response:', response);
        setError('Failed to fetch deals data');
      }
    } catch (err: any) {
      console.error('Error fetching deals:', err);
      setError(err.message || 'An error occurred while fetching deals');
    } finally {
      setLoading(false);
    }
  }, [currentPage, itemsPerPage, searchTerm, sortConfig, statusFilter, categoryFilter, storeFilter]);

  // Fetch categories and stores for filters
  const fetchCategoriesAndStores = useCallback(async () => {
    try {
      const [categoriesResponse, storesResponse] = await Promise.all([
        getCategories(),
        getStores()
      ]);

      setCategories(categoriesResponse || []);
      setStores(storesResponse || []);
    } catch (err) {
      console.error('Error fetching filter data:', err);
    }
  }, []);

  // Effect to update URL when state changes
  useEffect(() => {
    // Prevent updating URL on the initial render
    if (initialRenderComplete.current) {
      updateUrlFromState();
    }
  }, [currentPage, sortConfig, searchTerm, statusFilter, categoryFilter, storeFilter, updateUrlFromState]);

  // Initial data fetch
  useEffect(() => {
    fetchDeals();
    fetchCategoriesAndStores();
    
    // Mark initial render as complete after first fetch
    if (!initialRenderComplete.current) {
      initialRenderComplete.current = true;
    }
  }, [fetchDeals, fetchCategoriesAndStores]);

  // Delete handlers
  const handleDeleteClick = (id: string) => {
    setDealToDelete(id);
    setDeleteModalData({
      title: 'Delete Deal',
      message: 'Are you sure you want to delete this deal? This action cannot be undone.',
      onConfirm: confirmDelete
    });
    setDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (!dealToDelete) return;
    
    try {
      setDeleteModalOpen(false);
      
      await adminService.deleteDeal(parseInt(dealToDelete, 10));
      
      // Update UI by removing deleted deal
      setDeals(prevDeals => prevDeals.filter(deal => {
        const dealId = typeof deal.id === 'string' ? parseInt(deal.id, 10) : deal.id;
        return dealId !== parseInt(dealToDelete, 10);
      }));
      
      toast.success('Deal deleted successfully');
    } catch (error) {
      console.error('Error deleting deal:', error);
      toast.error('Failed to delete deal');
    } finally {
      setDealToDelete(null);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-gray-100 text-gray-800';
      case 'deleted':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };
  
  // Filter change handlers
  const handleSearchTermChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
    setCurrentPage(1);
  };

  const handleCategoryFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setCategoryFilter(e.target.value);
    setCurrentPage(1);
  };

  const handleStoreFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStoreFilter(e.target.value);
    setCurrentPage(1);
  };

  // Select/deselect all deals
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      // Select all deals
      const allDealIds = deals.map(deal => 
        typeof deal.id === 'string' ? parseInt(deal.id, 10) : deal.id
      );
      setSelectedDeals(new Set(allDealIds.map(String)));
    } else {
      // Deselect all deals
      setSelectedDeals(new Set());
    }
  };
  
  // Select/deselect a single deal
  const handleSelectDeal = (dealId: number, checked: boolean) => {
    if (checked) {
      setSelectedDeals(prev => new Set([...prev, String(dealId)]));
    } else {
      setSelectedDeals(prev => new Set([...prev].filter(id => id !== String(dealId))));
    }
  };
  
  // Sorting functionality
  const handleSort = (field: string) => {
    // Map frontend field names to backend field names
    const backendSortField = {
      'createdAt': 'created',
      'updatedAt': 'updated',
      'categoryName': 'category',
      'storeName': 'store'
    }[field] || field;
    
    // Toggle direction if clicking the same column
    // If the column is already sorted ascending, change to descending
    // If it's a different column or currently descending, start with ascending
    const direction = sortConfig.key === backendSortField && sortConfig.direction === 'asc' ? 'desc' : 'asc';
    
    console.log('\n\nDEBUG SORTING - Frontend:');
    console.log(`- Column clicked: '${field}'`);
    console.log(`- Mapped to backend field: '${backendSortField}'`);
    console.log(`- Sort direction: '${direction}'`);
    console.log(`- Final sort parameter: '${backendSortField}_${direction}'`);
    
    setSortConfig({ key: backendSortField, direction });
  };
  
  const getSortIcon = (columnName: string) => {
    const backendSortField = {
      'createdAt': 'created',
      'updatedAt': 'updated',
    }[columnName] || columnName;
    if (sortConfig.key === backendSortField) { 
      // Blue square for active sort column (CSR style)
      return <span className="ml-1 w-2 h-2 bg-blue-500 inline-block" aria-hidden="true"></span>;
    }
    return null; // No icon for non-active sortable columns
  };

  // Process the selected deals (activate)
  const handleBatchAction = async () => {
    if (selectedDeals.size === 0 || !batchActionType) {
      toast.error('Please select deals and an action.');
      return;
    }

    if (batchActionType === 'delete') {
      setBatchDeleteModalOpen(true);
      return; // Wait for confirmation before proceeding
    }

    // For 'activate', proceed directly
    setProcessing(true);
    setShowBatchProgressModal(true); // Show progress modal
    setProcessingProgress({
      total: selectedDeals.size,
      completed: 0,
      currentAction: 'Starting batch activation...',
      error: ''
    });

    const successfullyProcessedIds = new Set<number>();
    let anErrorOccurred = false;

    // Convert Set<string> to Array<string> for easier iteration with index
    const selectedDealIdsArray = Array.from(selectedDeals);

    for (let i = 0; i < selectedDealIdsArray.length; i++) {
      const dealIdString = selectedDealIdsArray[i];
      const dealIdNumber = parseInt(dealIdString, 10);

      if (isNaN(dealIdNumber)) {
        console.error(`Invalid deal ID found: ${dealIdString}`);
        setProcessingProgress(prev => ({
          ...prev,
          error: `${prev.error} Invalid deal ID: ${dealIdString}. `,
          completed: i + 1
        }));
        anErrorOccurred = true;
        continue;
      }

      setProcessingProgress(prev => ({
        ...prev,
        currentAction: `Activating deal ${i + 1} of ${selectedDealIdsArray.length}... (ID: ${dealIdNumber})`,
        completed: i
      }));

      try {
        // Use the proper activation endpoint which handles AI processing and image localization
        await adminService.activatePendingDeal(dealIdNumber);
        successfullyProcessedIds.add(dealIdNumber);
        // No need for simulated delay as the activation process is naturally longer
        // due to AI processing and image localization
      } catch (error: any) {
        console.error(`Error processing deal ${dealIdNumber} for ${batchActionType}:`, error);
        setProcessingProgress(prev => ({
          ...prev,
          error: `${prev.error}Failed to ${batchActionType} deal ${dealIdNumber}: ${error.message || 'Unknown error'}. `
        }));
        anErrorOccurred = true;
      }
      setProcessingProgress(prev => ({ ...prev, completed: i + 1 }));
    }

    setProcessingProgress(prev => ({
      ...prev,
      currentAction: batchActionType === 'activate' ? 'Batch activation complete.' : 'Batch deletion complete.'
    }));

    if (batchActionType === 'activate') {
      setDeals(prevDeals =>
        prevDeals.map(deal =>
          successfullyProcessedIds.has(deal.id) ? { ...deal, status: 'active' } : deal
        )
      );
      if (!anErrorOccurred && successfullyProcessedIds.size > 0) {
        toast.success(`${successfullyProcessedIds.size} deal(s) activated successfully.`);
      } else if (successfullyProcessedIds.size > 0) {
        toast.success(`${successfullyProcessedIds.size} deal(s) activated, but some errors occurred. Check progress details.`);
      } else if (anErrorOccurred) {
        toast.error('Batch activation failed. Check progress details.');
      } else {
        toast('No deals were activated.'); // Should not happen if selectedDeals was not empty
      }
    } else if (batchActionType === 'delete') {
      // This part will be handled by confirmBatchDelete now
      // We still need to update UI based on its outcome, handled in confirmBatchDelete
    }

    // Common cleanup for activation (delete cleanup will be in confirmBatchDelete)
    if (batchActionType === 'activate') {
      resetBatchActionState();
    }
  };

  const resetBatchActionState = () => {
    setSelectedDeals(new Set());
    setProcessing(false);
    setShowBatchProgressModal(false); // Hide progress modal on reset
    const batchActionSelect = document.getElementById('batchActionType') as HTMLSelectElement;
    if (batchActionSelect) batchActionSelect.value = '';
    setBatchActionType(null);
    // Optionally reset progress display here too if desired after a delay
    // setProcessingProgress({ total: 0, completed: 0, currentAction: '', error: '' });
  };

  const confirmBatchDelete = async () => {
    setBatchDeleteModalOpen(false);
    if (selectedDeals.size === 0) return; // Should not happen if modal was shown

    setProcessing(true);
    setShowBatchProgressModal(true); // Show progress modal
    setBatchActionType('delete'); // Ensure batchActionType is set for progress messages
    setProcessingProgress({
      total: selectedDeals.size,
      completed: 0,
      currentAction: 'Starting batch deletion...',
      error: ''
    });

    const successfullyDeletedIds = new Set<number>();
    let anErrorOccurred = false;
    const selectedDealIdsArray = Array.from(selectedDeals);

    for (let i = 0; i < selectedDealIdsArray.length; i++) {
      const dealIdString = selectedDealIdsArray[i];
      const dealIdNumber = parseInt(dealIdString, 10);

      if (isNaN(dealIdNumber)) {
        console.error(`Invalid deal ID found: ${dealIdString}`);
        setProcessingProgress(prev => ({
          ...prev,
          error: `${prev.error} Invalid deal ID: ${dealIdString}. `,
          completed: i + 1
        }));
        anErrorOccurred = true;
        continue;
      }

      setProcessingProgress(prev => ({
        ...prev,
        currentAction: `Deleting deal ${i + 1} of ${selectedDealIdsArray.length}... (ID: ${dealIdNumber})`,
        completed: i
      }));

      try {
        await adminService.deleteDeal(dealIdNumber);
        successfullyDeletedIds.add(dealIdNumber);
        await new Promise(resolve => setTimeout(resolve, 300)); // Simulate delay
      } catch (error: any) {
        console.error(`Error deleting deal ${dealIdNumber}:`, error);
        setProcessingProgress(prev => ({
          ...prev,
          error: `${prev.error}Failed to delete deal ${dealIdNumber}: ${error.message || 'Unknown error'}. `
        }));
        anErrorOccurred = true;
      }
      setProcessingProgress(prev => ({ ...prev, completed: i + 1 }));
    }

    setProcessingProgress(prev => ({
      ...prev,
      currentAction: 'Batch deletion complete.'
    }));

    setDeals(prevDeals =>
      prevDeals.filter(deal => !successfullyDeletedIds.has(deal.id))
    );

    if (!anErrorOccurred && successfullyDeletedIds.size > 0) {
      toast.success(`${successfullyDeletedIds.size} deal(s) deleted successfully.`);
    } else if (successfullyDeletedIds.size > 0) {
      toast.success(`${successfullyDeletedIds.size} deal(s) deleted, but some errors occurred. Check progress details.`);
    } else if (anErrorOccurred) {
      toast.error('Batch deletion failed. Check progress details.');
    } else {
      toast('No deals were deleted.');
    }

    resetBatchActionState();
  };

  return (
    <div className="mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Deal Management</h1>
        <div className="flex space-x-2">
          {selectedDeals.size > 0 && (
            <div className="flex items-center mr-4">
              <span className="text-sm font-medium mr-2">
                {selectedDeals.size} deals selected
              </span>
              <select 
                value={batchActionType || ''} 
                onChange={(e) => setBatchActionType(e.target.value as 'activate' | 'delete' | null)}
                className="p-2 border rounded text-sm"
              >
                <option value="">Select action</option>
                <option value="activate">Activate</option>
                <option value="delete">Delete</option>
              </select>
              <button
                onClick={handleBatchAction}
                disabled={processing}
                className="ml-2 px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <CheckIcon className="h-4 w-4 inline mr-1" />
                {batchActionType === 'activate' ? 'Activate' : batchActionType === 'delete' ? 'Delete' : 'Apply'}
              </button>
            </div>
          )}
          <Link
            href="/admin/deals/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-400"
          >
            <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            + Add Deal
          </Link>
        </div>
      </div>

      {/* Filters */}
      <div className="mt-6 border-b border-gray-200 bg-white p-6 sm:rounded-md shadow">
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-4">
          {/* Search */}
          <div className="col-span-1 sm:col-span-2">
            <form onSubmit={handleSearch}>
              <label htmlFor="search" className="block text-sm font-medium text-gray-700">
                Search
              </label>
              <div className="mt-1 flex rounded-md shadow-sm">
                <input
                  type="text"
                  name="search"
                  id="search"
                  value={searchTerm}
                  onChange={handleSearchTermChange}
                  className="flex-1 min-w-0 block w-full px-3 py-2 rounded-md border border-gray-300 focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                  placeholder="Search deals by title or description..."
                />
                <button
                  type="submit"
                  className="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-400"
                >
                  Search
                </button>
              </div>
            </form>
          </div>

          {/* Status Filter */}
          <div>
            <label htmlFor="statusFilter" className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              id="statusFilter"
              name="statusFilter"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm rounded-md"
              value={statusFilter}
              onChange={handleStatusFilterChange}
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="expired">Expired</option>
              <option value="pending">Pending</option>
            </select>
          </div>

          {/* Category Filter */}
          <div>
            <label htmlFor="categoryFilter" className="block text-sm font-medium text-gray-700">
              Category
            </label>
            <select
              id="categoryFilter"
              name="categoryFilter"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm rounded-md"
              value={categoryFilter}
              onChange={handleCategoryFilterChange}
            >
              <option value="all">All Categories</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id.toString()}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* Store Filter */}
          <div>
            <label htmlFor="storeFilter" className="block text-sm font-medium text-gray-700">
              Store
            </label>
            <select
              id="storeFilter"
              name="storeFilter"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm rounded-md"
              value={storeFilter}
              onChange={handleStoreFilterChange}
            >
              <option value="all">All Stores</option>
              {stores.map((store) => (
                <option key={store.id} value={store.id.toString()}>
                  {store.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Deals Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        ) : deals.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No deals found matching your criteria</p>
          </div>
        ) : (
          <div className="overflow-x-auto w-full">
            <table className="min-w-max w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      checked={selectedDeals.size === deals.length && deals.length > 0}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                  </th>
                  <th 
                    scope="col" 
                    className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('title')} // Sort by title for the DEAL column
                  >
                    DEAL {getSortIcon('title')}
                  </th>
                  <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ACTIONS
                  </th>
                  <th 
                    scope="col" 
                    className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('categoryName')} // Assuming categoryName is the sort field
                  >
                    CATEGORY {getSortIcon('categoryName')}
                  </th>
                  <th 
                    scope="col" 
                    className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('store')} // Using 'store' to match backend expectation
                  >
                    STORE {getSortIcon('store')}
                  </th>
                  <th 
                    scope="col" 
                    className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    STATUS
                  </th>
                  <th 
                    scope="col" 
                    className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('created')}
                  >
                    CREATED {getSortIcon('created')}
                  </th>
                  <th 
                    scope="col" 
                    className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('updated')}
                  >
                    UPDATED {getSortIcon('updated')}
                  </th>
                  <th 
                    scope="col" 
                    className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    // Not sortable based on CSR screenshot
                  >
                    EXPIRES
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {deals.map((deal) => (
                  <tr key={deal.id}>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedDeals.has(String(deal.id))}
                        onChange={(e) => handleSelectDeal(deal.id, e.target.checked)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                    </td>
                    {/* DEAL Column - Adjusted for better layout */}
                    <td className="px-3 py-2">
                      <div className="flex items-start"> {/* Changed to items-start for better vertical alignment if title wraps */} 
                        <div className="flex-shrink-0 h-10 w-10 mt-1"> {/* Added mt-1 for slight alignment adjustment if needed */}
                          <img 
                            className="h-10 w-10 rounded-sm object-cover"
                            src={isExternalUrl(deal.imageUrl) ? PLACEHOLDER_IMAGE : getThumbnailUrl(deal.imageUrl || '') || PLACEHOLDER_IMAGE}
                            alt={deal.title}
                            onError={(e) => (e.currentTarget.src = PLACEHOLDER_IMAGE)}
                          />
                        </div>
                        <div className="ml-4 flex-grow min-w-0 max-w-xs"> {/* Added temporary max-w-xs for diagnostics */}
                          <div className="text-sm font-medium text-gray-900 hover:text-primary-600 truncate">
                            {/* Applying truncate here for very long titles, but primary wrapping should occur due to td change */}
                            <Link href={`/admin/deals/${deal.id}/edit`} title={deal.title}>{deal.title}</Link>
                          </div>
                          <div className="text-xs text-gray-500 mt-0.5"> {/* Reduced to text-xs for price, mt-0.5 for spacing */}
                            {deal.price ? `$${Number(deal.price).toFixed(2)}` : 'N/A'}
                            {deal.originalPrice && Number(deal.originalPrice) > (Number(deal.price) || 0) && (
                              <span className="ml-1 line-through">
                                ${Number(deal.originalPrice).toFixed(2)}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Link
                          href={`/admin/deals/${deal.id}/edit`}
                          className="text-primary-600 hover:text-primary-900"
                          title="Edit Deal"
                        >
                          <PencilIcon className="h-5 w-5" aria-hidden="true" />
                        </Link>
                        <button
                          onClick={() => handleDeleteClick(String(deal.id))}
                          className="text-red-600 hover:text-red-900"
                          title="Delete Deal"
                        >
                          <TrashIcon className="h-5 w-5" aria-hidden="true" />
                        </button>
                      </div>
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{deal.category?.name || 'N/A'}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{deal.store?.name || 'N/A'}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm">
                      <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(
                            deal.status
                          )}`}
                        >
                          {deal.status.charAt(0).toUpperCase() + deal.status.slice(1)}
                        </span>
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{formatDate(deal.createdAt)}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{formatDate(deal.updatedAt)}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {deal.expiresAt ? formatDate(deal.expiresAt) : 'No expiry'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        
        {/* Pagination */}
        {!loading && deals.length > 0 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(currentPage * itemsPerPage, totalCount)}
                  </span>{' '}
                  of <span className="font-medium">{totalCount}</span> results
                </p>
              </div>
              <Pagination 
                currentPage={currentPage} 
                totalPages={totalPages} 
                onPageChange={setCurrentPage} 
              />
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {deleteModalData && (
        <AlertModal
          isOpen={deleteModalOpen}
          onClose={() => setDeleteModalOpen(false)}
          title={deleteModalData.title}
          message={deleteModalData.message}
          onConfirm={deleteModalData.onConfirm}
          confirmButtonType="danger"
        />
      )}

      {/* Batch Delete Confirmation Modal */}
      {batchDeleteModalOpen && (
        <AlertModal
          isOpen={batchDeleteModalOpen}
          onClose={() => setBatchDeleteModalOpen(false)}
          title="Confirm Batch Delete"
          message={`Are you sure you want to delete ${selectedDeals.size} selected deal(s)? This action cannot be undone.`}
          confirmLabel="Delete Selected"
          onConfirm={confirmBatchDelete}
          confirmButtonType="danger"
        />
      )}

      {/* Batch Progress Modal */}
      {showBatchProgressModal && (
        <AlertModal
          isOpen={showBatchProgressModal}
          onClose={() => {}}
          onConfirm={() => {}}
          title={ batchActionType === 'activate' ? "Batch Activating Deals" : "Batch Deleting Deals" }
          message={
            <div className="text-sm text-gray-600 w-full mt-1">
              <div>{processingProgress.currentAction}</div>
              {processingProgress.total > 0 && (
                <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2 mb-2">
                  <div 
                    className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${processingProgress.total > 0 ? (processingProgress.completed / processingProgress.total) * 100 : 0}%` }}
                  ></div>
                </div>
              )}
              {processingProgress.error && (
                <div className="text-red-500 text-xs mt-1 p-2 bg-red-50 rounded">
                  <strong>Errors:</strong> {processingProgress.error}
                </div>
              )}
            </div>
          }
          showConfirmButton={false}
          showCancelButton={false}
        />
      )}
    </div>
  );
}
