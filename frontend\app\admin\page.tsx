'use client';

import React from 'react';
import { 
  TagIcon, 
  BuildingStorefrontIcon,
  DocumentTextIcon,
  UserGroupIcon,
  ServerStackIcon,
  ChartBarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import Dashboard from '../components/admin/Dashboard';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

// Main Admin Dashboard Page
export default function AdminPage() {
  const adminModules = [
    {
      name: 'Categories',
      description: 'Manage deal categories',
      icon: TagIcon,
      href: '/admin/categories',
      color: 'bg-blue-500'
    },
    {
      name: 'Stores',
      description: 'Manage stores and merchants',
      icon: BuildingStorefrontIcon,
      href: '/admin/stores',
      color: 'bg-green-500'
    },
    {
      name: 'Deals',
      description: 'Manage and moderate deals',
      icon: DocumentTextIcon,
      href: '/admin/deals',
      color: 'bg-yellow-500'
    },
    {
      name: 'Users',
      description: 'Manage user accounts',
      icon: UserGroupIcon,
      href: '/admin/users',
      color: 'bg-purple-500'
    },
    {
      name: 'Scrapers',
      description: 'Configure and run deal scrapers',
      icon: ServerStackIcon,
      href: '/admin/scrapers',
      color: 'bg-red-500'
    }
  ];

  return (
    <div className="pb-6">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Admin Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Welcome to the NiceDeals admin panel. Monitor your platform's performance and manage all aspects of your site.
        </p>
      </div>
      
      <div className="py-4">
        <Tabs defaultValue="statistics" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="statistics">
              <ChartBarIcon className="mr-2 h-4 w-4" />
              Statistics
            </TabsTrigger>
            <TabsTrigger value="quick-access">
              <ClockIcon className="mr-2 h-4 w-4" />
              Quick Access
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="statistics" className="space-y-4">
            <Dashboard />
          </TabsContent>
          
          <TabsContent value="quick-access" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Admin Modules</CardTitle>
                <CardDescription>Quick access to all admin functions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
                  {adminModules.map((module) => (
                    <Link
                      key={module.name}
                      href={module.href}
                      className="block rounded-lg bg-white p-4 shadow transition-all duration-200 hover:shadow-lg border border-gray-100"
                    >
                      <div className="flex items-center">
                        <div className={`flex h-10 w-10 items-center justify-center rounded-md ${module.color}`}>
                          <module.icon className="h-5 w-5 text-white" aria-hidden="true" />
                        </div>
                        <div className="ml-4">
                          <h2 className="text-base font-medium text-gray-900">{module.name}</h2>
                          <p className="mt-1 text-xs text-gray-500">{module.description}</p>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
