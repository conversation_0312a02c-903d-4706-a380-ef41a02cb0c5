'use client';

import React, { useState, useEffect } from 'react';
import api from '@/services/api';
import { ScraperLog, ApiResponse } from '@/types';
import { formatDate } from '@/utils/formatters';
import toast from 'react-hot-toast';

export default function AdminScrapersPage() {
  const [scraperLogs, setScraperLogs] = useState<ScraperLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [runningScrapers, setRunningScrapers] = useState<string[]>([]);

  useEffect(() => {
    fetchScraperLogs();
  }, []);

  const fetchScraperLogs = async () => {
    try {
      setLoading(true);
      const response = await api.get<ApiResponse<{ logs: ScraperLog[] }>>('/admin/scrapers/logs');
      
      if (response.data.success && response.data.data) {
        setScraperLogs(response.data.data.logs);
      } else {
        setError(response.data.error || 'Failed to fetch scraper logs');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching scraper logs');
    } finally {
      setLoading(false);
    }
  };

  const handleRunScraper = async (source: string) => {
    try {
      setRunningScrapers((prev) => [...prev, source]);
      setError(null);
      
      const response = await api.post<ApiResponse<any>>('/admin/scrapers/run', { source });
      
      if (response.data.success) {
        // Show success message if data file was created
        if (response.data.data?.dataFile) {
          console.log(`Scraper data saved to: ${response.data.data.dataFile}`);
        }
        await fetchScraperLogs(); // Refresh logs
      } else {
        setError(response.data.error || `Failed to run ${source} scraper`);
      }
    } catch (err: any) {
      setError(err.message || `An error occurred while running ${source} scraper`);
    } finally {
      setRunningScrapers((prev) => prev.filter((s) => s !== source));
    }
  };

  const handleRunAllScrapers = async () => {
    try {
      setRunningScrapers((prev) => [...prev, 'all']);
      setError(null);
      
      const response = await api.post<ApiResponse<any>>('/admin/scrapers/run-all');
      
      if (response.data.success) {
        await fetchScraperLogs(); // Refresh logs
      } else {
        setError(response.data.error || 'Failed to run all scrapers');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while running all scrapers');
    } finally {
      setRunningScrapers((prev) => prev.filter((s) => s !== 'all'));
    }
  };

  if (loading && scraperLogs.length === 0) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Scraper Management</h1>
          <button
            onClick={handleRunAllScrapers}
            disabled={runningScrapers.includes('all')}
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {runningScrapers.includes('all') ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Running All Scrapers...
              </span>
            ) : (
              'Run All Scrapers'
            )}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <strong className="font-bold">Error:</strong>
            <span className="block sm:inline"> {error}</span>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Amazon Scraper */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Amazon Scraper</h3>
              <div>
                <button
                  onClick={() => handleRunScraper('amazon')}
                  disabled={runningScrapers.includes('amazon')}
                  className={`px-3 py-1 text-white text-sm rounded focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                    runningScrapers.includes('amazon')
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-primary-600 hover:bg-primary-700'
                  }`}
                >
                  {runningScrapers.includes('amazon') ? (
                    <>
                      <span className="animate-spin mr-2">⚡</span>
                      Running...
                    </>
                  ) : (
                    'Run Scraper'
                  )}
                </button>
              </div>
            </div>
            <div className="border-t border-gray-200">
              <div className="px-4 py-5 sm:p-6">
                <p className="text-sm text-gray-500">
                  Scrapes deals from Amazon website.
                </p>
              </div>
            </div>
          </div>

          {/* HotUKDeals Scraper */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <h3 className="text-lg leading-6 font-medium text-gray-900">HotUKDeals Scraper</h3>
              <div>
                <button
                  onClick={() => handleRunScraper('hotukdeals')}
                  disabled={runningScrapers.includes('hotukdeals')}
                  className={`px-3 py-1 text-white text-sm rounded focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                    runningScrapers.includes('hotukdeals')
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-primary-600 hover:bg-primary-700'
                  }`}
                >
                  {runningScrapers.includes('hotukdeals') ? (
                    <>
                      <span className="animate-spin mr-2">⚡</span>
                      Running...
                    </>
                  ) : (
                    'Run Scraper'
                  )}
                </button>
              </div>
            </div>
            <div className="border-t border-gray-200">
              <div className="px-4 py-5 sm:p-6">
                <p className="text-sm text-gray-500">
                  Scrapes deals from HotUKDeals website.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:px-6">
            <h2 className="text-lg leading-6 font-medium text-gray-900">Scraper Logs</h2>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">History of scraper runs and their results.</p>
          </div>
          <div className="border-t border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Source
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Deals Added
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {scraperLogs.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      No scraper logs found
                    </td>
                  </tr>
                ) : (
                  scraperLogs.map((log) => (
                    <tr key={log.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 capitalize">
                        {log.source}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          log.status === 'completed' 
                            ? 'bg-green-100 text-green-800' 
                            : log.status === 'started'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-red-100 text-red-800'
                        }`}>
                          {log.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.dealsAdded}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(log.createdAt)}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add a section to show the latest scraper data file */}
        {scraperLogs.length > 0 && scraperLogs[0].dataFile && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <p className="text-sm">
              Latest scraper data file: <code>{scraperLogs[0].dataFile}</code>
            </p>
            <p className="text-sm mt-2 text-gray-500">
              This data file contains the raw scraped data before it's processed into deals.
            </p>
          </div>
        )}
      </div>
  );
}
