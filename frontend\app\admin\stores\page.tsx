'use client';

import React, { useState, useEffect } from 'react';
import { AdminStore } from '@/types/admin';
import adminService from '@/services/adminService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import Image from 'next/image';

export default function AdminStoresPage() {
  const [stores, setStores] = useState<AdminStore[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentStore, setCurrentStore] = useState<AdminStore | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    logoUrl: '',
  });

  // Fetch stores
  const fetchStores = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('[AdminStoresPage] Fetching stores for page:', page);
      const response = await adminService.getStores(page);
      console.log('[AdminStoresPage] Raw response from adminService.getStores:', response);
      console.log('[AdminStoresPage] Detailed response.data:', JSON.stringify(response?.data, null, 2));
      console.log('[AdminStoresPage] typeof response.data.stores:', typeof response?.data?.stores);
      console.log('[AdminStoresPage] typeof response.totalPages:', typeof response?.totalPages);

      if (response && response.data && Array.isArray(response.data.stores)) {
        setStores(response.data.stores);
        if (typeof response.totalPages === 'number') {
          setTotalPages(response.totalPages);
        } else {
          setTotalPages(1); // Default if not provided
        }
      } else {
        // Even if response is null/undefined due to an error before data parsing (like 401)
        // we set an error. If data is there but malformed, also set error.
        setError('Failed to parse stores from API response.');
        setStores([]);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('Error fetching stores:', error); // This will catch the 401 AxiosError
      setError('Failed to load stores. Please try again.');
      setStores([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStores();
  }, [page]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Add a new store
  const handleAddStore = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await adminService.createStore(formData);
      toast.success('Store created successfully!');
      fetchStores();
      setIsModalOpen(false);
      resetForm();
    } catch (error: any) {
      console.error('Error creating store:', error);
      toast.error(error.response?.data?.error || 'Failed to create store.');
    }
  };

  // Update existing store
  const handleUpdateStore = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentStore) return;

    try {
      await adminService.updateStore(currentStore.id, formData);
      toast.success('Store updated successfully!');
      fetchStores();
      setIsModalOpen(false);
      resetForm();
    } catch (error: any) {
      console.error('Error updating store:', error);
      toast.error(error.response?.data?.error || 'Failed to update store.');
    }
  };

  // Delete store
  const handleDeleteStore = async () => {
    if (!currentStore) return;

    try {
      await adminService.deleteStore(currentStore.id);
      toast.success('Store deleted successfully!');
      fetchStores();
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting store:', error);
      toast.error('Failed to delete store.');
    }
  };

  // Open edit modal
  const openEditModal = (store: AdminStore) => {
    setCurrentStore(store);
    setFormData({
      name: store.name || '',
      url: store.url || '',
      logoUrl: store.logoUrl || '',
    });
    setIsModalOpen(true);
  };

  // Open delete modal
  const openDeleteModal = (store: AdminStore) => {
    setCurrentStore(store);
    setIsDeleteModalOpen(true);
  };

  // Open add modal
  const openAddModal = () => {
    setCurrentStore(null);
    resetForm();
    setIsModalOpen(true);
  };

  // Reset form
  const resetForm = () => {
    setCurrentStore(null);
    setFormData({
      name: '',
      url: '',
      logoUrl: '',
    });
  };

  return (
    <>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Stores</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage the stores and merchants
          </p>
        </div>
        <Button onClick={openAddModal}>
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Store
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
          {error}
        </div>
      )}

      {/* Stores table */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Logo</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Deals</TableHead>
              <TableHead>Website</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : stores.length > 0 ? (
              stores.map((store) => (
                <TableRow key={store.id}>
                  <TableCell>{store.id}</TableCell>
                  <TableCell>
                    {store.logoUrl ? (
                      <div className="relative h-10 w-10 rounded-full overflow-hidden">
                        <Image 
                          src={store.logoUrl}
                          alt={store.name}
                          width={40}
                          height={40}
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-500 text-xs font-medium">
                          {store.name.substring(0, 2).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="font-medium">{store.name}</TableCell>
                  <TableCell>{store.dealsCount}</TableCell>
                  <TableCell>
                    {store.url && (
                      <a 
                        href={store.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 hover:underline truncate block max-w-[200px]"
                      >
                        {new URL(store.url).hostname}
                      </a>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditModal(store)}
                      >
                        <PencilIcon className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        onClick={() => openDeleteModal(store)}
                      >
                        <TrashIcon className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                  No stores found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-between items-center mt-4">
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.max(p - 1, 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className="text-sm text-gray-600">
            Page {page} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.min(p + 1, totalPages))}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Add/Edit Store Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{currentStore ? 'Edit Store' : 'Add Store'}</DialogTitle>
            <DialogDescription>
              {currentStore
                ? 'Update the store details below.'
                : 'Enter the details for the new store.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={currentStore ? handleUpdateStore : handleAddStore}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <label htmlFor="name" className="text-sm font-medium">
                  Name
                </label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Store name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="url" className="text-sm font-medium">
                  Website URL
                </label>
                <Input
                  id="url"
                  name="url"
                  type="url"
                  placeholder="https://www.store.com"
                  value={formData.url}
                  onChange={handleInputChange}
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="logoUrl" className="text-sm font-medium">
                  Logo URL
                </label>
                <Input
                  id="logoUrl"
                  name="logoUrl"
                  type="url"
                  placeholder="https://www.store.com/logo.png"
                  value={formData.logoUrl}
                  onChange={handleInputChange}
                />
                {formData.logoUrl && (
                  <div className="mt-2">
                    <p className="text-xs text-gray-500 mb-1">Preview:</p>
                    <div className="relative h-12 w-12 rounded-full overflow-hidden border border-gray-200">
                      <Image
                        src={formData.logoUrl}
                        alt="Logo preview"
                        fill
                        className="object-cover"
                        onError={(e) => {
                          // Set a fallback for error
                          (e.target as HTMLImageElement).src = '/placeholder.png';
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                {currentStore ? 'Update' : 'Create'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Store</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the store "{currentStore?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteStore}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
