'use client'; // Mark as Client Component

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Tag, ChevronRight } from 'lucide-react';
import { categoryService } from '../services/categoryService';
import { Category } from '../types';
import { getCategoryIcon } from '@/utils/iconUtils';

const CategoryList: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const data = await categoryService.getCategories();
        setCategories(data);
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCategories();
  }, []);
  
  return (
    <div className="py-8">
      <div className="container mx-auto px-0">
        {/* Header */}
        <div className="flex items-center justify-between mb-6 bg-gradient-to-r from-deal-orange to-deal-orange-dark text-white rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-white/20 rounded-full">
              <Tag className="h-5 w-5 text-white" />
            </div>
            <h2 className="text-xl font-display font-semibold text-white">Browse Categories</h2>
          </div>
          <Link href="/dealsBrowse" className="flex items-center gap-1 text-sm font-medium text-white hover:text-gray-100 transition-colors">
            View All
            <ChevronRight className="h-4 w-4" />
          </Link>
        </div>
        
        {/* Categories Grid */}
        {loading ? (
          // Loading skeletons
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="glass rounded-xl p-4 animate-pulse">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-gray-200"></div>
                  <div className="space-y-2">
                    <div className="h-4 w-20 bg-gray-200 rounded"></div>
                    <div className="h-3 w-16 bg-gray-100 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {categories.map((category, index) => (
                <Link
                  key={category.id}
                  href={`/dealsBrowse?category=${category.id}`}
                  className="glass category-item animate-fade-in hover:shadow-md transition-shadow duration-200"
                  style={{ animationDelay: `${index * 0.05}s` }}
                >
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-deal-orange-light to-deal-orange/10 flex items-center justify-center">
                    <span className="category-icon text-deal-orange">
                      {getCategoryIcon(category.name, { className: 'h-5 w-5' })}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">{category.name}</span>
                    <div className="text-xs text-gray-500">
                      {category.dealsCount ? `${category.dealsCount} deals` : 'Browse deals'}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CategoryList;
