'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { createDealsBrowseUrl } from '../../utils/urlUtils';
import { useAuth } from '@/hooks/useAuth';

export default function Footer() {
  const { isAuthenticated, user } = useAuth();
  const [mounted, setMounted] = useState(false);
  
  // This ensures we only render after the client-side hydration
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Check if user is admin (IDs 1 or 2)
  const isAdmin = isAuthenticated && user && (user.id === 1 || user.id === 2);
  
  // Don't render anything during SSR to avoid hydration mismatch
  if (!mounted) return null;
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-white border-t border-gray-200 mt-auto">
      <div className="mx-auto max-w-7xl px-6 py-8 md:flex md:items-center md:justify-between lg:px-8">
        <div className="md:order-1">
          <p className="text-center text-sm leading-6 text-gray-500">
            &copy; {currentYear} Nice Deals Ltd. All rights reserved.
          </p>
        </div>
        
        <div className="mt-4 md:mt-0 md:order-2">
          <nav className="flex flex-wrap justify-center gap-x-6 gap-y-2" aria-label="Footer">
            <Link href="/" className="text-sm font-medium leading-6 text-gray-600 hover:text-deal-orange transition-colors">
              Home
            </Link>
            <Link href={createDealsBrowseUrl()} className="text-sm font-medium leading-6 text-gray-600 hover:text-deal-orange transition-colors">
              Browse Deals
            </Link>
            <Link href="/deals/create" className="text-sm font-medium leading-6 text-gray-600 hover:text-deal-orange transition-colors">
              Submit Deal
            </Link>
            <Link href="/about" className="text-sm font-medium leading-6 text-gray-600 hover:text-deal-orange transition-colors">
              About Us
            </Link>
            <Link href="/contact" className="text-sm font-medium leading-6 text-gray-600 hover:text-deal-orange transition-colors">
              Contact
            </Link>
            <Link href="/privacy" className="text-sm font-medium leading-6 text-gray-600 hover:text-deal-orange transition-colors">
              Privacy Policy
            </Link>
            <Link href="/terms" className="text-sm font-medium leading-6 text-gray-600 hover:text-deal-orange transition-colors">
              Terms of Service
            </Link>
            {isAdmin && (
              <Link 
                href="/admin" 
                className="text-sm font-bold leading-6 text-deal-orange hover:text-orange-600 transition-colors border-l-2 border-gray-200 pl-4 ml-2"
              >
                Admin Dashboard
              </Link>
            )}
          </nav>
          {/* Debug info - remove in production */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-2 text-xs text-gray-400 text-center">
              {user ? `Logged in as: ${user.username} (ID: ${user.id})` : 'Not logged in'}
            </div>
          )}
        </div>
      </div>
    </footer>
  );
}
