import React from 'react';

interface AdminDealFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void; // Replace 'any' with a proper DealFormData type later
  initialData?: any; // Replace 'any' with a proper Deal type later
  categories: Array<{ id: number; name: string }>; // Define more specific types as needed
  stores: Array<{ id: number; name: string }>; // Define more specific types as needed
  isSaving?: boolean;
}

const AdminDealFormModal: React.FC<AdminDealFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  categories,
  stores,
  isSaving,
}) => {
  if (!isOpen) return null;

  // Basic form structure - to be replaced with actual form fields and logic
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-lg">
        <h2 className="text-xl font-semibold mb-4">
          {initialData ? 'Edit Deal' : 'Create New Deal'}
        </h2>
        <form onSubmit={(e) => { e.preventDefault(); onSubmit({}); /* Dummy submit */ }}>
          <p className="mb-4">Admin Deal Form Modal Placeholder</p>
          <div className="flex justify-end space-x-2 mt-6">
            <button type="button" onClick={onClose} className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
              Cancel
            </button>
            <button type="submit" disabled={isSaving} className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50">
              {isSaving ? 'Saving...' : (initialData ? 'Save Changes' : 'Create Deal')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdminDealFormModal;
