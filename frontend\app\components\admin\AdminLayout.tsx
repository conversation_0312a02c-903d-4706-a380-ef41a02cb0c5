'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { 
  ChartBarIcon,
  TagIcon,
  BuildingStorefrontIcon,
  DocumentTextIcon,
  UserGroupIcon,
  ServerStackIcon,
  Bars3Icon,
  XMarkIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import withAdminAuth from './withAdminAuth';
import adminService from '@/services/adminService';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [windowWidth, setWindowWidth] = useState<number>(typeof window !== 'undefined' ? window.innerWidth : 0);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const [activeDealCount, setActiveDealCount] = useState<number>(0);
  const [pendingDealCount, setPendingDealCount] = useState<number>(0);

  // Handle window resize and check screen size
  useEffect(() => {
    // Initialize on client side
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        const width = window.innerWidth;
        setWindowWidth(width);
        // Auto-collapse sidebar when screen is small
        setIsCollapsed(width < 1400);
      };
      
      // Set initial values
      handleResize();
      
      // Add event listener
      window.addEventListener('resize', handleResize);
      
      // Cleanup
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, []);

  // Fetch deal counts on mount
  useEffect(() => {
    const fetchDealCounts = async () => {
      try {
        // Fetch counts in parallel for better performance
        const [activeCount, pendingCount] = await Promise.all([
          adminService.getActiveDealsCount(),
          adminService.getPendingDealsCount()
        ]);
        
        setActiveDealCount(activeCount);
        setPendingDealCount(pendingCount);
      } catch (error) {
        console.error('Error fetching deal counts:', error);
      }
    };

    fetchDealCounts();
  }, []);

  // Track expanded state for submenus
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({
    deals: true, // Start with deals expanded
  });

  // Toggle submenu expansion
  const toggleSubmenu = (key: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Toggle sidebar collapsed state
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: ChartBarIcon },
    { name: 'Categories', href: '/admin/categories', icon: TagIcon },
    { name: 'Stores', href: '/admin/stores', icon: BuildingStorefrontIcon },
    { 
      name: 'Deals', 
      key: 'deals',
      href: '/admin/deals', 
      icon: DocumentTextIcon,
      hasSubmenu: true,
      children: [
        { name: 'View All', href: '/admin/deals' },
        { name: 'View Pending', href: '/admin/deals?status=pending' },
        { name: 'View Active', href: '/admin/deals?status=active' },
      ] 
    },
    { name: 'Users', href: '/admin/users', icon: UserGroupIcon },
    { name: 'Scrapers', href: '/admin/scrapers', icon: ServerStackIcon },
  ];

  return (
    <div id="admin-section-root" className="min-h-screen bg-gray-50">
      {/* Collapsible sidebar - changes to icon-only when collapsed */}
      <div 
        className={`fixed inset-y-0 left-0 z-30 flex flex-col bg-white border-r border-gray-200 pt-5 pb-4 transition-all duration-300 ${isCollapsed ? 'w-16' : 'w-64'}`}
      >
        <div className={`flex flex-shrink-0 items-center ${isCollapsed ? 'justify-center px-2' : 'px-4'}`}>
          {!isCollapsed ? (
            <span className="text-xl font-bold text-orange-600">NiceDeals</span>
          ) : (
            <span className="text-xl font-bold text-orange-600">ND</span>
          )}
        </div>
        
        {/* Toggle button that appears at the top right of the sidebar */}
        <button
          onClick={toggleSidebar}
          className="absolute top-5 -right-3 bg-white rounded-full p-1 border border-gray-200 shadow-md hover:bg-gray-50"
        >
          {isCollapsed ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
          )}
        </button>
        
        <div className="mt-5 flex-1 flex flex-col overflow-y-auto">
          <nav className={`flex-1 ${isCollapsed ? 'px-2' : 'px-4'} space-y-1`}>
            {navigation.map((item) => {
              const isActive = pathname === item.href || pathname?.startsWith(`${item.href}/`) || 
                (item.hasSubmenu && item.children?.some(child => 
                  pathname.startsWith(child.href.split('?')[0]) && 
                  (pathname.includes(child.href.split('?')[1] || ''))));
              
              const isExpanded = item.key ? expandedMenus[item.key] : false;
              
              return (
                <div key={item.name} className="relative">
                  {item.hasSubmenu ? (
                    <button
                      onClick={() => isCollapsed ? window.location.href = item.href : toggleSubmenu(item.key!)}
                      className={`w-full group flex ${isCollapsed ? 'justify-center' : 'justify-between'} items-center px-2 py-2 text-sm font-medium rounded-md ${isActive
                        ? 'bg-orange-50 text-orange-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                      title={isCollapsed ? `${item.name} - Click to view all` : ''}
                    >
                      <div className="flex items-center">
                        <item.icon
                          className={`${isCollapsed ? 'mr-0' : 'mr-3'} h-6 w-6 flex-shrink-0 ${isActive ? 'text-orange-500' : 'text-gray-400 group-hover:text-gray-500'}`}
                          aria-hidden="true"
                        />
                        {!isCollapsed && item.name}
                      </div>
                      {!isCollapsed && (
                        <svg
                          className={`h-5 w-5 transform transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                    </button>
                  ) : (
                    <Link
                      href={item.href}
                      className={`group flex ${isCollapsed ? 'justify-center' : 'items-center'} px-2 py-2 text-sm font-medium rounded-md ${isActive
                        ? 'bg-orange-50 text-orange-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                      title={isCollapsed ? item.name : ''}
                    >
                      <item.icon
                        className={`${isCollapsed ? 'mr-0' : 'mr-3'} h-6 w-6 flex-shrink-0 ${isActive ? 'text-orange-500' : 'text-gray-400 group-hover:text-gray-500'}`}
                        aria-hidden="true"
                      />
                      {!isCollapsed && item.name}
                    </Link>
                  )}
                  
                  {/* Show submenu counts as small dots on collapsed mode with links */}
                  {isCollapsed && item.hasSubmenu && (
                    <div className="absolute -right-1 top-0 flex flex-col space-y-1 pt-1">
                      {item.key === 'deals' && pendingDealCount > 0 && (
                        <button 
                          onClick={() => { window.location.href = '/admin/deals?status=pending'; }}
                          className="h-3 w-3 rounded-full bg-amber-500 cursor-pointer" 
                          title={`${pendingDealCount} pending deals - Click to view`}
                        />
                      )}
                      {item.key === 'deals' && activeDealCount > 0 && (
                        <button 
                          onClick={() => { window.location.href = '/admin/deals?status=active'; }}
                          className="h-3 w-3 rounded-full bg-green-500 cursor-pointer" 
                          title={`${activeDealCount} active deals - Click to view`}
                        />
                      )}
                    </div>
                  )}
                  
                  {/* Only show submenu when not collapsed */}
                  {!isCollapsed && item.hasSubmenu && isExpanded && (
                    <div className="pl-8 pt-1 pb-1 space-y-1">
                      {item.children?.map((subItem) => {
                        const subIsActive = pathname.startsWith(subItem.href.split('?')[0]) && 
                          pathname.includes(subItem.href.split('?')[1] || '');
                        return (
                          <button
                            key={subItem.name}
                            onClick={() => {
                              // Force a refresh when navigating to ensure the page reloads with the new parameters
                              window.location.href = subItem.href;
                            }}
                            className={`group flex items-center w-full text-left px-2 py-1.5 text-xs font-medium rounded-md ${subIsActive
                              ? 'bg-orange-50 text-orange-600'
                              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                            }`}
                          >
                            {subItem.name}
                            {subItem.name === 'View Active' && (
                              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {activeDealCount}
                              </span>
                            )}
                            {subItem.name === 'View Pending' && pendingDealCount > 0 && (
                              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                                {pendingDealCount}
                              </span>
                            )}
                          </button>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </nav>
        </div>
        
        <div className={`flex flex-shrink-0 border-t border-gray-200 p-4 ${isCollapsed ? 'justify-center' : ''}`}>
          <Link
            href="/"
            className="group flex items-center text-sm font-medium text-gray-500 hover:text-gray-900"
            title={isCollapsed ? 'Back to Site' : ''}
          >
            <ArrowLeftIcon className={`${isCollapsed ? 'mr-0' : 'mr-3'} h-5 w-5 text-gray-400 group-hover:text-gray-500`} />
            {!isCollapsed && 'Back to Site'}
          </Link>
        </div>
      </div>
      
      {/* Mobile sidebar overlay - only visible when mobile menu is open */}
      {sidebarOpen && windowWidth < 768 && (
        <div className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)}></div>
      )}

      {/* Main content area - adjusted padding based on sidebar state */}
      <div className={`flex flex-1 flex-col ${isCollapsed ? 'pl-16' : 'md:pl-64'}`}>
        <div className="sticky top-0 z-10 bg-white pl-1 pt-1 sm:pl-3 sm:pt-3 flex items-center">
          {/* Optional: Show toggle button for very small screens */}
          {windowWidth < 768 && (
            <button
              type="button"
              className="-ml-0.5 -mt-0.5 inline-flex h-12 w-12 items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-orange-500"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              <span className="sr-only">{sidebarOpen ? 'Close sidebar' : 'Open sidebar'}</span>
              {sidebarOpen ? <XMarkIcon className="h-6 w-6" aria-hidden="true" /> : <Bars3Icon className="h-6 w-6" aria-hidden="true" />}
            </button>
          )}
          
          {/* Section title */}
          <div className="ml-2 text-lg font-medium text-gray-700">
            {navigation.find(item => pathname === item.href || pathname?.startsWith(`${item.href}/`))?.name || 'Admin'}
          </div>
        </div>
        
        <main className="flex-1">
          <div className="py-6">
            <div className="px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default withAdminAuth(AdminLayout);
