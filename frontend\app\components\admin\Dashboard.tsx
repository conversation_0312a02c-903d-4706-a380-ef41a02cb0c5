'use client';

import React, { useState, useEffect } from 'react';
import { 
  ArrowUpIcon, 
  ArrowDownIcon, 
  UserIcon, 
  ShoppingBagIcon, 
  ClockIcon,
  ExclamationCircleIcon,
  CheckCircleIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatDate } from '@/utils/formatters';
import adminService from '@/services/adminService';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import { DashboardStats, RecentActivity } from '@/types/admin';

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await adminService.getDashboardStats();
        setStats(data.stats);
        setRecentActivity(data.recentActivity || []);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const StatCard = ({ 
    title, 
    value, 
    change, 
    icon, 
    linkTo = null 
  }: { 
    title: string; 
    value: number; 
    change: number; 
    icon: React.ReactNode;
    linkTo?: string | null;
  }) => {
    const CardWrapper = ({ children }: { children: React.ReactNode }) => {
      if (linkTo) {
        return (
          <Link href={linkTo} className="block transition-all duration-200 hover:opacity-90">
            {children}
          </Link>
        );
      }
      return <>{children}</>;
    };

    return (
      <CardWrapper>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            <div className="h-4 w-4 text-muted-foreground">{icon}</div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">{value.toLocaleString()}</div>
                <div className="flex items-center space-x-1 text-xs">
                  {change > 0 ? (
                    <ArrowUpIcon className="h-3 w-3 text-green-500" />
                  ) : change < 0 ? (
                    <ArrowDownIcon className="h-3 w-3 text-red-500" />
                  ) : (
                    <span className="h-3 w-3">-</span>
                  )}
                  <span className={
                    change > 0 
                      ? 'text-green-500' 
                      : change < 0 
                        ? 'text-red-500' 
                        : 'text-gray-500'
                  }>
                    {change !== 0 ? `${Math.abs(change)}% from last period` : 'No change'}
                  </span>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </CardWrapper>
    );
  };

  const renderStatusSummary = () => {
    if (!stats) return null;
    
    const pendingPercentage = stats.totalDeals > 0 
      ? Math.round((stats.pendingDeals / stats.totalDeals) * 100) 
      : 0;
    
    const activePercentage = stats.totalDeals > 0 
      ? Math.round((stats.activeDeals / stats.totalDeals) * 100) 
      : 0;
    
    return (
      <Card>
        <CardHeader>
          <CardTitle>Deal Status</CardTitle>
          <CardDescription>Current breakdown of deals by status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center">
                <div className="flex items-center flex-1">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  <div className="text-sm">Active Deals</div>
                </div>
                <div>
                  <span className="text-sm font-medium">{stats.activeDeals}</span>
                  <span className="text-xs text-gray-500 ml-1">({activePercentage}%)</span>
                </div>
              </div>
              <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-green-500 rounded-full" 
                  style={{ width: `${activePercentage}%` }}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center">
                <div className="flex items-center flex-1">
                  <ClockIcon className="h-4 w-4 text-yellow-500 mr-2" />
                  <div className="text-sm">Pending Deals</div>
                </div>
                <div>
                  <span className="text-sm font-medium">{stats.pendingDeals}</span>
                  <span className="text-xs text-gray-500 ml-1">({pendingPercentage}%)</span>
                </div>
              </div>
              <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-yellow-500 rounded-full" 
                  style={{ width: `${pendingPercentage}%` }}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-4">
      {error && (
        <div className="bg-red-50 text-red-700 p-4 rounded-md mb-4">
          <div className="flex">
            <ExclamationCircleIcon className="h-5 w-5 text-red-400 mr-2" />
            <span>{error}</span>
          </div>
        </div>
      )}
      
      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">
            <ChartBarIcon className="mr-2 h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="activity">
            <ClockIcon className="mr-2 h-4 w-4" />
            Activity
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <StatCard
              title="Total Users"
              value={stats?.totalUsers || 0}
              change={stats?.userGrowth || 0}
              icon={<UserIcon className="h-4 w-4" />}
              linkTo="/admin/users"
            />
            <StatCard
              title="Total Deals"
              value={stats?.totalDeals || 0}
              change={stats?.dealGrowth || 0}
              icon={<ShoppingBagIcon className="h-4 w-4" />}
              linkTo="/admin/deals"
            />
            <StatCard
              title="Active Deals"
              value={stats?.activeDeals || 0}
              change={0}
              icon={<CheckCircleIcon className="h-4 w-4" />}
              linkTo="/admin/deals?status=active"
            />
            <StatCard
              title="Pending Deals"
              value={stats?.pendingDeals || 0}
              change={0}
              icon={<ClockIcon className="h-4 w-4" />}
              linkTo="/admin/deals?status=pending"
            />
          </div>
          
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {renderStatusSummary()}
            
            {!loading && stats && (
              <>
                <Card className="md:col-span-2">
                  <CardHeader>
                    <CardTitle>Today's Summary</CardTitle>
                    <CardDescription>New activity in the last 24 hours</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <UserIcon className="h-5 w-5 text-blue-500 mr-2" />
                          <div>
                            <div className="text-sm font-medium">New Users</div>
                            <div className="text-xs text-gray-500">Today</div>
                          </div>
                        </div>
                        <div className="text-xl font-bold">{stats?.newUsersToday || 0}</div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <ShoppingBagIcon className="h-5 w-5 text-green-500 mr-2" />
                          <div>
                            <div className="text-sm font-medium">New Deals</div>
                            <div className="text-xs text-gray-500">Today</div>
                          </div>
                        </div>
                        <div className="text-xl font-bold">{stats?.newDealsToday || 0}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </TabsContent>
        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest actions across the platform</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                Array(5)
                  .fill(0)
                  .map((_, i) => (
                    <div key={i} className="mb-4 flex items-center space-x-4">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-[250px]" />
                        <Skeleton className="h-3 w-[200px]" />
                      </div>
                    </div>
                  ))
              ) : recentActivity.length > 0 ? (
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-4">
                      <div
                        className={`rounded-full p-2 ${
                          activity.type === 'user' ? 'bg-blue-100' : 'bg-green-100'
                        }`}
                      >
                        {activity.type === 'user' ? (
                          <UserIcon className="h-4 w-4 text-blue-500" />
                        ) : (
                          <ShoppingBagIcon className="h-4 w-4 text-green-500" />
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.title}</p>
                        <p className="text-xs text-gray-500">
                          {activity.action} {activity.user && `by ${activity.user.username}`}
                        </p>
                        <p className="text-xs text-gray-400">{formatDate(activity.timestamp)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-sm text-gray-500">No recent activity found</p>
              )}
            </CardContent>
            <CardFooter>
              <p className="text-xs text-gray-500">Showing the last 10 activities</p>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
