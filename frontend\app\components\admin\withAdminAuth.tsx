'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

/**
 * Higher-order component that restricts access to admin routes
 * Only users with IDs 1 and 2 are allowed access
 * @param Component The component to be wrapped with admin authentication
 */
export const withAdminAuth = <P extends object>(Component: React.ComponentType<P>) => {
  return function WithAdminAuth(props: P) {
    const { user, isAuthenticated, loading } = useAuth();
    const router = useRouter();
    
    // React to auth changes
    React.useEffect(() => {
      // Only redirect after loading is complete and user is determined not to have access
      if (!loading) {
        // Check if user is authenticated and has admin access (userId 1 or 2)
        const hasAdminAccess = isAuthenticated && user && (user.id === 1 || user.id === 2);
        
        // Redirect unauthorized users to the home page
        if (!hasAdminAccess) {
          router.push('/');
        }
      }
    }, [isAuthenticated, loading, router, user]);
    
    // Show loading indicator while checking auth status
    if (loading) {
      return (
        <div className="flex h-screen items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary-500"></div>
        </div>
      );
    }
    
    // Check if user is authenticated and has admin access (userId 1 or 2)
    const hasAdminAccess = isAuthenticated && user && (user.id === 1 || user.id === 2);
    
    // Only render when user has access
    if (hasAdminAccess) {
      return <Component {...props} />;
    }
    
    // Render nothing during redirect
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary-500"></div>
      </div>
    );
  };
};

export default withAdminAuth;
