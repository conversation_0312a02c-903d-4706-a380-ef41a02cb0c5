'use client';

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { enGB } from 'date-fns/locale';
import toast from 'react-hot-toast';
import { useAuth } from '@/hooks/useAuth'; // Adjusted path
import { Comment } from '@/types'; // Adjusted path

const DEBUG: number = 1; // Debug flag: Set to 1 to enable console logging, 0 to disable

// Debug logging helper
const debugLog = (...args: any[]) => {
  if (DEBUG > 0) {
    console.log(...args);
  }
};

interface CommentSectionProps {
  dealId: number;
}

const CommentSection: React.FC<CommentSectionProps> = ({ dealId }) => {
  const { isAuthenticated, user } = useAuth();
  const [newComment, setNewComment] = useState('');
  const queryClient = useQueryClient();

  // Fetch comments for the deal
  const {
    data: comments,
    isPending: isLoadingComments,
    isError,
  } = useQuery<Comment[]>({ // Specify Comment[] for better type safety
    queryKey: ['comments', dealId],
    queryFn: async () => {
      const response = await fetch(`/api/comments/${dealId}`);
      if (!response.ok && response.status !== 404) { // Allow 404 for no comments
        throw new Error('Failed to fetch comments');
      }
      // If 404, return empty array, otherwise parse JSON
      const data = response.status === 404 ? [] : await response.json();
      debugLog('[fetchComments] Data received:', data);
      return data as Comment[];
    },
    enabled: !!dealId, // Only run query if dealId is present
  });

  // Add comment mutation
  const addCommentMutation = useMutation<Comment, Error, void>({
    mutationFn: async () => {
      if (!newComment.trim()) {
        throw new Error('Comment cannot be empty');
      }

      debugLog('[addComment] Posting comment for dealId:', dealId);

      const response = await fetch(`/api/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`, // Assumes token is in localStorage
        },
        body: JSON.stringify({
          dealId: parseInt(dealId.toString(), 10), // Ensure dealId is number
          text: newComment.trim()
        }),
      });

      if (!response.ok) {
        const error = await response.json(); // Try to parse error message
        debugLog('[addComment] Error response:', error);
        throw new Error(error.error || 'Failed to add comment');
      }

      const newCommentData = await response.json();
      debugLog('[addComment] Success response:', newCommentData);
      return newCommentData as Comment;
    },
    onSuccess: (data: Comment) => {
      debugLog('[addComment] Mutation success, data:', data);
      setNewComment('');
      queryClient.invalidateQueries({ queryKey: ['comments', dealId] });
      toast.success('Comment added successfully');
    },
    onError: (error: Error) => {
      debugLog('[addComment] Mutation error:', error);
      toast.error(error.message || 'Failed to add comment');
    },
  });

  // Delete comment mutation
  const deleteCommentMutation = useMutation<unknown, Error, number>({
    mutationFn: async (commentId: number) => {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`, // Assumes token is in localStorage
        },
      });

      if (!response.ok) {
        const errorText = await response.text(); // Get text for more details
        debugLog(`[deleteComment] Failed for comment ${commentId}:`, response.status, errorText);
        throw new Error('Failed to delete comment');
      }
      // Check if response has content before parsing JSON
      if (response.headers.get('content-length') === '0' || response.status === 204) {
        debugLog(`[deleteComment] No content for comment ${commentId}, assuming success.`);
        return; // No content, successful deletion
      }
      try {
        return await response.json();
      } catch (e) {
        debugLog(`[deleteComment] No JSON body for comment ${commentId}, assuming success.`);
        return; // If JSON parsing fails but status was ok
      }
    },
    onSuccess: (data, commentId) => {
      debugLog(`[deleteComment] Success for comment ${commentId}:`, data);
      queryClient.invalidateQueries({ queryKey: ['comments', dealId] });
      toast.success('Comment deleted successfully');
    },
    onError: (error: Error, commentId) => {
      debugLog(`[deleteComment] Error for comment ${commentId}:`, error);
      toast.error(error.message || 'Failed to delete comment');
    },
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim()) {
      toast.error('Comment cannot be empty.');
      return;
    }
    addCommentMutation.mutate();
  };

  // Handle comment deletion
  const handleDeleteComment = (commentId: number) => {
    if (window.confirm('Are you sure you want to delete this comment?')) {
      deleteCommentMutation.mutate(commentId);
    }
  };

  return (
    <div className="mt-8 rounded-lg bg-white p-6 shadow-lg">
      <h3 className="text-xl font-semibold text-gray-800">Comments</h3>

      {/* Comment form */} 
      {isAuthenticated ? (
        <form onSubmit={handleSubmit} className="mt-4">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Write your comment..."
            rows={3}
            className="w-full rounded-md border border-gray-300 p-3 text-sm text-gray-700 focus:border-primary-500 focus:ring-primary-500 shadow-sm"
            disabled={addCommentMutation.isPending}
          />
          <div className="mt-2 flex justify-end">
            <button
              type="submit"
              className="rounded-md bg-primary-600 px-4 py-2 text-sm font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-md transition-colors duration-150 ease-in-out disabled:opacity-50"
              disabled={addCommentMutation.isPending}
            >
              {addCommentMutation.isPending ? 'Posting...' : 'Post Comment'}
            </button>
          </div>
        </form>
      ) : (
        <div className="mt-4 rounded-md bg-gray-50 p-4 text-center">
          <p className="text-sm text-gray-700">
            Please{' '}
            <a href="/login" className="font-medium text-primary-600 hover:text-primary-500">
              sign in
            </a>{' '}
            to join the discussion
          </p>
        </div>
      )}

      {/* Comments list */} 
      <div className="mt-6 divide-y divide-gray-200">
        {isLoadingComments ? (
          // Loading state
          <div className="space-y-4 py-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 rounded-full bg-gray-200"></div>
                  <div className="h-4 w-24 rounded bg-gray-200"></div>
                </div>
                <div className="mt-2 h-4 w-3/4 rounded bg-gray-200"></div>
                <div className="mt-1 h-4 w-1/2 rounded bg-gray-200"></div>
              </div>
            ))}
          </div>
        ) : isError ? (
          // Error state
          <div className="py-4 text-center">
            <p className="text-sm text-red-600">Failed to load comments. Please try again.</p>
          </div>
        ) : comments?.length === 0 ? (
          // Empty state
          <div className="py-4 text-center">
            <p className="text-sm text-gray-500">No comments yet. Be the first to comment!</p>
          </div>
        ) : (
          // Comments list
          comments?.map((comment: Comment) => (
            <div key={comment.id} className="py-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div className="h-8 w-8 overflow-hidden rounded-full bg-gray-200">
                    {comment.avatarUrl ? (
                      <img
                        src={comment.avatarUrl}
                        alt={`${comment.username}'s avatar`}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center bg-gray-100 text-gray-400">
                        {comment.username.charAt(0).toUpperCase()}
                      </div>
                    )}
                  </div>

                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">
                      {comment.username || 'Unknown User'}
                    </p>
                    <p className="text-xs text-gray-500">
                      {comment.created_at ? (
                        format(new Date(comment.created_at), 'PP p', { locale: enGB }) // Added locale
                      ) : (
                        'Unknown date'
                      )}
                    </p>
                  </div>
                </div>

                {/* Delete button (only for own comments or moderators/admins) */}
                {(user?.id === comment.userId || user?.role === 'admin' || user?.role === 'moderator') && (
                  <button
                    onClick={() => handleDeleteComment(comment.id)}
                    className="text-sm text-gray-400 hover:text-red-600"
                    aria-label="Delete comment"
                  >
                    Delete
                  </button>
                )}
              </div>

              <div className="mt-2 text-sm text-gray-700">
                <p>{comment.text}</p>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default CommentSection;
