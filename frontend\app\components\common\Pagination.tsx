'use client';

import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems?: number;
  pageSize?: number;
  itemName?: string;
  onPageChange: (page: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  pageSize = 30,
  itemName = 'items',
  onPageChange,
}) => {
  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null;
  
  // Generate page range
  const getPageRange = () => {
    const pages = [];
    const maxVisiblePages = 5; // Maximum number of page buttons to show
    
    if (totalPages <= maxVisiblePages) {
      // If total pages is less than max visible pages, show all pages
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always include first page
      pages.push(1);
      
      // Calculate start and end of the page range
      let startPage = Math.max(currentPage - 1, 2);
      let endPage = Math.min(startPage + 2, totalPages - 1);
      
      // Adjust if we're at the end of the range
      if (currentPage > totalPages - 3) {
        startPage = totalPages - 3;
        endPage = totalPages - 1;
      }
      
      // Add ellipsis if needed
      if (startPage > 2) {
        pages.push('...');
      }
      
      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      // Add ellipsis if needed
      if (endPage < totalPages - 1) {
        pages.push('...');
      }
      
      // Always include last page
      pages.push(totalPages);
    }
    
    return pages;
  };
  
  const pageRange = getPageRange();
  
  // Calculate display info
  const firstItemIndex = (currentPage - 1) * pageSize + 1;
  const lastItemIndex = Math.min(currentPage * pageSize, totalItems || currentPage * pageSize);
  
  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
      {/* Items info */}
      {totalItems && (
        <div className="text-sm text-gray-700">
          Showing{' '}
          <span className="font-medium">{firstItemIndex}</span> to{' '}
          <span className="font-medium">{lastItemIndex}</span> of{' '}
          <span className="font-medium">{totalItems}</span> {itemName}
        </div>
      )}
      
      {/* Page buttons */}
      <div className="flex items-center justify-center sm:justify-end gap-1">
        {/* Previous button */}
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`relative inline-flex items-center px-2 py-2 text-sm font-medium rounded-md ${
            currentPage === 1
              ? 'text-gray-300 cursor-not-allowed'
              : 'text-gray-700 hover:bg-gray-100'
          }`}
        >
          <span className="sr-only">Previous</span>
          <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
        </button>
        
        {/* Page numbers */}
        {pageRange.map((page, index) => (
          <React.Fragment key={index}>
            {typeof page === 'number' ? (
              <button
                onClick={() => onPageChange(page)}
                className={`relative inline-flex items-center px-4 py-2 text-sm font-medium ${
                  currentPage === page
                    ? 'bg-primary-600 text-white hover:bg-primary-700'
                    : 'text-gray-700 hover:bg-gray-100'
                } rounded-md`}
              >
                {page}
              </button>
            ) : (
              <span className="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-700">
                {page}
              </span>
            )}
          </React.Fragment>
        ))}
        
        {/* Next button */}
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`relative inline-flex items-center px-2 py-2 text-sm font-medium rounded-md ${
            currentPage === totalPages
              ? 'text-gray-300 cursor-not-allowed'
              : 'text-gray-700 hover:bg-gray-100'
          }`}
        >
          <span className="sr-only">Next</span>
          <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
        </button>
      </div>
    </div>
  );
};

export default Pagination;
