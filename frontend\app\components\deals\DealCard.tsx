'use client';

import React, { useMemo, useState, useEffect } from 'react';
import Link from 'next/link'; // Import next/link
import { useRouter, usePathname, useSearchParams } from 'next/navigation'; // Import Next.js navigation hooks
import { format } from 'date-fns';
import { 
  ChevronUpIcon, 
  ChevronDownIcon,
  ChatBubbleLeftIcon,
  ClockIcon,
  LinkIcon,
  FireIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import { Deal } from '@/types';
import { useAuth } from '@/hooks/useAuth';
import api from '@/services/api';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { getThumbnailUrl, handleImageError } from '@/utils/imageUtils'; // Adjust path if needed
import { getAffiliateUrl } from '@/utils/formatters';
import { saveDeal, unsaveDeal, checkIfDealSaved } from '@/services/dealService';

// Hot threshold for deal temperature
const GETTING_WARM_TEMPERATURE = 1; // We'll use this as default since it's 1 in .env

interface DealCardProps {
  deal: Deal;
  showActions?: boolean;
}

const DealCard: React.FC<DealCardProps> = ({ deal, showActions = true }) => {
  const { isAuthenticated, user } = useAuth();
  const queryClient = useQueryClient();
  const router = useRouter(); // Use Next.js router
  const pathname = usePathname(); // Use Next.js pathname
  const searchParams = useSearchParams(); // Use Next.js search params
  const [isSaved, setIsSaved] = useState(false);
  
  // Check if deal is saved when component mounts
  useEffect(() => {
    const checkSavedStatus = async () => {
      if (isAuthenticated && deal.id && user?.id) {
        try {
          const saved = await checkIfDealSaved(deal.id);
          setIsSaved(saved);
        } catch (error) {
          console.error('Error checking saved status:', error);
          setIsSaved(false);
        }
      }
    };
    
    checkSavedStatus();
  }, [isAuthenticated, deal.id, user?.id]);

  // Handle save deal
  const handleSave = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent link/navigation
    e.stopPropagation(); // Prevent event bubbling
    
    if (!isAuthenticated || !user?.id) {
      toast.error('Please log in to save deals');
      return;
    }

    try {
      if (isSaved) {
        await unsaveDeal(deal.id);
      } else {
        await saveDeal(deal.id);
      }
      setIsSaved(!isSaved);
      toast.success(isSaved ? "Removed from saved deals" : "Deal saved successfully");
      // Invalidate queries to refetch saved deal IDs and the list of saved deals
      queryClient.invalidateQueries({ queryKey: ['savedDealIds'] });
      queryClient.invalidateQueries({ queryKey: ['savedDeals'] });
    } catch (error: any) {
      toast.error(error.message || 'Failed to save deal');
    }
  };

  // Function to update URL with new filter
  const updateUrlWithFilter = (filterType: 'store' | 'category', value: string) => {
    const currentParams = new URLSearchParams(searchParams.toString());
    const currentValue = currentParams.get(filterType);
    
    // Only update if the value is different
    if (currentValue !== value) {
      currentParams.set(filterType, value);
      // Always include status=active and sort=newest
      currentParams.set('status', 'active');
      currentParams.set('sort', 'newest');
      router.push(`${pathname}?${currentParams.toString()}`);
    }
  };

  // Format the discount percentage
  const discountPercentage = deal.originalPrice && deal.price
    ? Math.round(((deal.originalPrice - deal.price) / deal.originalPrice) * 100)
    : null;
  
  // Format the date
  const formattedDate = deal.updatedAt ? 
    format(new Date(deal.updatedAt), 'MMM d, yyyy') : 
    'Just now';
  
  // Check if deal is expired
  const isExpired = deal.status === 'expired' || 
    (deal.expiresAt && typeof deal.expiresAt === 'string' && new Date(deal.expiresAt) < new Date());
  
  // Memoize the thumbnail URL calculation to prevent recalculation on every render
  const thumbnailUrl = useMemo(() => getThumbnailUrl(deal.imageUrl || ''), [deal.imageUrl]);
  
  // Vote mutations
  const voteMutation = useMutation({
    mutationFn: async ({ dealId, value }: { dealId: number; value: 1 | -1 }) => {
      const response = await api.post(`/deals/${dealId}/vote`, { value });
      return response.data;
    },
    onSuccess: (data) => {
      // Update all queries that contain this deal
      const updateDealInCache = (oldData: any) => {
        if (!oldData) return oldData;

        // Handle paginated data (like from dealsBrowse)
        if (oldData.deals && Array.isArray(oldData.deals)) {
          return {
            ...oldData,
            deals: oldData.deals.map((d: Deal) => 
              d.id === deal.id ? { ...d, temperature: data.newTemperature, userVote: data.userVote } : d
            ),
          };
        }
        // Handle single deal data (like from dealDetail)
        if (oldData.id === deal.id) {
          return { 
            ...oldData, 
            temperature: data.newTemperature, 
            userVote: data.userVote 
          };
        }
        
        // Handle array of deals (less common top-level structure but possible)
        if (Array.isArray(oldData)) {
            return oldData.map((d: Deal) => 
                d.id === deal.id ? { ...d, temperature: data.newTemperature, userVote: data.userVote } : d
            );
        }

        return oldData;
      };

      queryClient.setQueryData(['deal', deal.id], updateDealInCache);
      queryClient.invalidateQueries({ queryKey: ['deals'] }); // Invalidate all deal lists
      queryClient.invalidateQueries({ queryKey: ['userDeals'] });

      toast.success('Vote recorded!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to record vote');
    },
  });

  // Handle vote
  const handleVote = (value: 1 | -1) => {
    if (!isAuthenticated) {
      toast.error('Please log in to vote');
      // Optionally, redirect to login page or show login modal
      // router.push('/login?redirect=' + encodeURIComponent(pathname + searchParams.toString()));
      return;
    }
    if (deal.userVote !== undefined) {
      toast.error('You have already voted on this deal.');
      return;
    }
    voteMutation.mutate({ dealId: deal.id, value });
  };

  // Determine vote button styles
  const getUpvoteButtonStyle = () => {
    if (deal.userVote === 1) return 'text-white bg-deal-orange';
    return 'text-gray-400 hover:text-deal-orange hover:bg-deal-orange/10';
  };

  const getDownvoteButtonStyle = () => {
    if (deal.userVote === -1) return 'text-white bg-deal-blue';
    return 'text-gray-400 hover:text-deal-blue hover:bg-deal-blue/10';
  };

  // Temperature color based on deal temperature (votes)
  const getTemperatureColor = () => {
    if (deal.temperature >= GETTING_WARM_TEMPERATURE) return 'text-deal-orange';
    if (deal.temperature < 0) return 'text-deal-blue';
    return 'text-gray-500';
  };

  return (
    <div 
      className={`relative glass rounded-xl overflow-hidden flex flex-col transition-all duration-300 ease-in-out card-hover shadow-sm ${isExpired ? 'opacity-60' : ''}`}
    >
      {isExpired && (
        <div className="absolute inset-0 bg-gray-500/50 z-10 flex items-center justify-center">
          <span className="bg-red-500 text-white px-3 py-1 rounded-md font-semibold text-sm">EXPIRED</span>
        </div>
      )}
      
      {/* Image Section */} 
      <Link href={`/dealDetail/${deal.id}`} className="block relative aspect-[4/3] overflow-hidden">
        <img 
          src={thumbnailUrl} 
          alt={deal.title} 
          onError={handleImageError} 
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          loading="lazy"
        />
        {discountPercentage !== null && (
          <span className="absolute top-2 right-2 bg-deal-orange text-white text-xs font-semibold px-2 py-1 rounded">
            {discountPercentage}% OFF
          </span>
        )}
        {isAuthenticated && (
          <button
            onClick={handleSave}
            className={`absolute top-2 left-2 p-1.5 rounded-full transition-all duration-200 z-20 ${isSaved ? 'bg-pink-500/80 hover:bg-pink-600/80' : 'bg-black/30 hover:bg-black/50'}`}
            aria-label={isSaved ? "Unsave deal" : "Save deal"}
            title={isSaved ? "Unsave deal" : "Save deal"}
          >
            {isSaved ? (
              <HeartIconSolid className="w-4 h-4 text-white" />
            ) : (
              <HeartIcon className="w-4 h-4 text-white" />
            )}
          </button>
        )}
      </Link>
      
      {/* Content Section */}
      <div className="p-3 sm:p-4 flex-grow flex flex-col">
        {/* Category & Date */} 
        <div className="flex items-center text-xs text-gray-500 mb-1.5 flex-wrap">
          {deal.categoryId && deal.categoryName && (
            <button
              onClick={() => updateUrlWithFilter('category', deal.categoryId!.toString())}
              className="mr-2 hover:text-deal-orange hover:underline"
            >
              {deal.categoryName}
            </button>
          )}
          
          {/* Date badge - shows today or formatted date */}
          <span className="text-gray-500 px-2 py-0.5">
            {deal.updatedAt && new Date(deal.updatedAt).toDateString() === new Date().toDateString() 
              ? 'Today' 
              : formattedDate}
          </span>
        </div>
        
        {/* Title */} 
        <Link href={`/dealDetail/${deal.id}`} className="block mb-2">
          <h2 className="font-medium text-gray-900 leading-tight line-clamp-2 hover:text-deal-orange transition-colors duration-200">
            {deal.title}
          </h2>
        </Link>
        
        {/* Store name with "from" prefix */} 
        {deal.storeId && deal.storeName && (
          <div className="text-sm text-gray-500 mb-2">
            from <button
              onClick={() => updateUrlWithFilter('store', deal.storeId!.toString())}
              className="text-deal-orange hover:underline"
            >
              {deal.storeName}
            </button>
          </div>
        )}
        
        {/* Price section */} 
        <div className="flex items-baseline mb-3">
          <span className="text-deal-orange text-xl font-bold mr-2">
            £{deal.price?.toFixed(2)}
          </span>
          {deal.originalPrice && (
            <span className="text-gray-400 text-sm line-through">
              £{deal.originalPrice.toFixed(2)}
            </span>
          )}
        </div>
        
        {/* Footer with temp/votes/comments */} 
        <div className="flex items-center justify-between text-sm border-t border-gray-100 pt-2">
          {/* Temperature */} 
          <div className="flex items-center gap-2">
            <div className="flex items-center">
              <FireIcon className={`h-4 w-4 ${getTemperatureColor()}`} />
              <span className={`font-medium ${getTemperatureColor()}`}>
                {deal.temperature}°
              </span>
            </div>
            
            {deal.commentCount !== undefined && (
              <div className="flex items-center text-gray-500">
                <ChatBubbleLeftIcon className="h-4 w-4 mr-1" />
                <span>{deal.commentCount}</span>
              </div>
            )}
          </div>
          
          {/* Voting or View Deal */} 
          <div className="flex items-center">
            {showActions ? (
              <div className="flex">
                <button
                  onClick={() => handleVote(1)}
                  disabled={deal?.userVote !== undefined}
                  title={deal?.userVote !== undefined ? `You've already voted on this deal` : "Vote up"}
                  className={`p-1 rounded transition-colors ${getUpvoteButtonStyle()} ${deal?.userVote !== undefined ? 'cursor-not-allowed opacity-50' : ''}`}
                >
                  <ChevronUpIcon className="h-5 w-5" />
                </button>
                
                <button
                  onClick={() => handleVote(-1)}
                  disabled={deal?.userVote !== undefined}
                  title={deal?.userVote !== undefined ? `You've already voted on this deal` : "Vote down"}
                  className={`p-1 rounded transition-colors ${getDownvoteButtonStyle()} ${deal?.userVote !== undefined ? 'cursor-not-allowed opacity-50' : ''}`}
                >
                  <ChevronDownIcon className="h-5 w-5" />
                </button>
              </div>
            ) : (
              <Link 
                href={`/dealDetail/${deal.id}`}
                className="text-deal-orange hover:underline text-sm font-medium flex items-center"
              >
                View Deal <LinkIcon className="h-3.5 w-3.5 ml-1" />
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Export with React.memo to prevent unnecessary re-renders when parent components update
export default React.memo(DealCard);
