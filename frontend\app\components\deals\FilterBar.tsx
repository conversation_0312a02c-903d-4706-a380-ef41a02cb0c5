'use client';

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { ArrowUpDown, Filter } from 'lucide-react';
import { Category, Store, DealFilters } from '../../types';
import api from '../../services/api';

interface FilterBarProps {
  filters: DealFilters;
  onFilterChange: (newFilters: DealFilters) => void;
}

const FilterBar: React.FC<FilterBarProps> = ({ filters, onFilterChange }) => {
  const [searchInput, setSearchInput] = useState(filters.search || '');
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [isSortOpen, setIsSortOpen] = useState(false);
  const [tempSort, setTempSort] = useState<DealFilters['sort']>(filters.sort || 'newest');
  
  // Fetch categories with store filter
  const { data: categoriesData } = useQuery({
    queryKey: ['categories', filters.store],
    queryFn: async () => {
      const params = filters.store ? { store: filters.store } : undefined;
      const response = await api.get('/categories', { params });
      return response.data as Category[];
    }
  });
  
  // Fetch stores with category filter
  const { data: storesData } = useQuery({
    queryKey: ['stores', filters.category],
    queryFn: async () => {
      const params = filters.category ? { category: filters.category } : undefined;
      const response = await api.get('/stores', { params });
      return response.data as Store[];
    }
  });
  
  // Update searchInput when filters.search changes
  useEffect(() => {
    setSearchInput(filters.search || '');
  }, [filters.search]);
  
  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onFilterChange({ ...filters, search: searchInput, page: 1 });
  };
  
  // Handle category change
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value === 'all' ? undefined : parseInt(e.target.value, 10);
    onFilterChange({ ...filters, category: value, page: 1 });
  };
  
  // Handle store change
  const handleStoreChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value === 'all' ? undefined : parseInt(e.target.value, 10);
    onFilterChange({ ...filters, store: value, page: 1 });
  };
  
  // Handle status change
  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value === 'all' ? 'all' : e.target.value as 'active' | 'expired';
    onFilterChange({ ...filters, status: value, page: 1 });
  };
  
  // Handle sort change
  const handleSortChange = (sortValue: DealFilters['sort']) => {
    setTempSort(sortValue);
  };
  
  // Handle apply sort
  const handleApplySort = () => {
    onFilterChange({ ...filters, sort: tempSort, page: 1 });
    setIsSortOpen(false);
  };
  
  // Handle clear filters
  const handleClearFilters = () => {
    setSearchInput('');
    onFilterChange({
      page: 1,
      pageSize: filters.pageSize,
      sort: filters.sort, // Preserve the current sort
      status: 'active',
      category: undefined,
      store: undefined,
      search: undefined
    });
  };

  // Get current sort display text
  const getSortDisplayText = () => {
    switch(filters.sort) {
      case 'newest': return 'Newest';
      case 'hottest': return 'Hottest';
      case 'price-asc': return 'Price: Low to High';
      case 'price-desc': return 'Price: High to Low';
      case 'most-commented': return 'Most Commented';
      case 'getting-warm': return 'Getting Warm';
      case 'trending': return 'Trending';
      default: return 'Sort: Newest';
    }
  };
  
  return (
    <div className="w-full">
      {/* Search row with full-width bar and buttons */}
      <div className="mb-4 w-full flex flex-col sm:flex-row items-center gap-2">
        {/* Full-width search bar */}
        <form onSubmit={handleSearchSubmit} className="w-full relative">
          <div className="relative w-full">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon
                className="h-5 w-5 text-gray-400"
                aria-hidden="true"
              />
            </div>
            <input
              type="text"
              name="search"
              id="search"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="block w-full rounded-md border-0 py-2 pl-10 pr-24 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"
              placeholder="Search for deals..."
            />
            <div className="absolute inset-y-0 right-0 flex items-center">
              <button
                type="submit"
                className="rounded-r-md px-3.5 py-2 text-sm font-semibold text-white shadow-sm bg-primary-600 hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 h-full"
              >
                Search
              </button>
            </div>
          </div>
        </form>
        
        {/* Filter button on mobile */}
        <button
          type="button"
          className="sm:hidden w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          onClick={() => setIsFiltersOpen(!isFiltersOpen)}
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
        </button>
        
        {/* Sort button */}
        <div className="relative sm:w-auto w-full">
          <button
            type="button"
            onClick={() => setIsSortOpen(!isSortOpen)}
            className="w-full sm:w-auto flex items-center justify-center gap-1 bg-white px-4 py-2 rounded-md border border-gray-300 text-sm text-gray-700 hover:bg-gray-50"
          >
            <ArrowUpDown className="h-4 w-4" />
            <span>{getSortDisplayText()}</span>
          </button>
          
          {/* Sort dropdown */}
          {isSortOpen && (
            <div className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="py-1">
                <button
                  onClick={() => handleSortChange('newest')}
                  className={`w-full text-left block px-4 py-2 text-sm ${
                    tempSort === 'newest' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                  } hover:bg-gray-100`}
                >
                  Newest
                </button>
                <button
                  onClick={() => handleSortChange('hottest')}
                  className={`w-full text-left block px-4 py-2 text-sm ${
                    tempSort === 'hottest' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                  } hover:bg-gray-100`}
                >
                  Hottest
                </button>
                <button
                  onClick={() => handleSortChange('price-asc')}
                  className={`w-full text-left block px-4 py-2 text-sm ${
                    tempSort === 'price-asc' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                  } hover:bg-gray-100`}
                >
                  Price: Low to High
                </button>
                <button
                  onClick={() => handleSortChange('price-desc')}
                  className={`w-full text-left block px-4 py-2 text-sm ${
                    tempSort === 'price-desc' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                  } hover:bg-gray-100`}
                >
                  Price: High to Low
                </button>
                <button
                  onClick={() => handleSortChange('most-commented')}
                  className={`w-full text-left block px-4 py-2 text-sm ${
                    tempSort === 'most-commented' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                  } hover:bg-gray-100`}
                >
                  Most Commented
                </button>
                <button
                  onClick={() => handleSortChange('getting-warm')}
                  className={`w-full text-left block px-4 py-2 text-sm ${
                    tempSort === 'getting-warm' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                  } hover:bg-gray-100`}
                >
                  Getting Warm
                </button>
                <button
                  onClick={() => handleSortChange('trending')}
                  className={`w-full text-left block px-4 py-2 text-sm ${
                    tempSort === 'trending' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                  } hover:bg-gray-100`}
                >
                  Trending
                </button>
                
                <div className="px-4 py-2 border-t border-gray-100">
                  <button
                    onClick={handleApplySort}
                    className="w-full text-center px-4 py-1.5 text-sm bg-primary-500 hover:bg-primary-600 text-white rounded-md"
                  >
                    Apply Sort
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Mobile filters - show only when isFiltersOpen is true */}
      <div className={`sm:hidden mb-4 ${isFiltersOpen ? 'block' : 'hidden'}`}>
        <div className="bg-white rounded-md border border-gray-200 p-4 shadow-sm">
          <div className="mb-4 flex justify-between items-center">
            <h3 className="text-sm font-medium">Filters</h3>
            <button
              onClick={handleClearFilters}
              className="text-xs text-primary-600 hover:text-primary-800"
            >
              Clear All
            </button>
          </div>
          
          {/* Category filter */}
          <div className="mb-4">
            <label htmlFor="category-mobile" className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              id="category-mobile"
              name="category"
              value={filters.category || 'all'}
              onChange={handleCategoryChange}
              className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base text-gray-900 focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
            >
              <option value="all">All Categories</option>
              {categoriesData?.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
          
          {/* Store filter */}
          <div className="mb-4">
            <label htmlFor="store-mobile" className="block text-sm font-medium text-gray-700 mb-1">
              Store
            </label>
            <select
              id="store-mobile"
              name="store"
              value={filters.store || 'all'}
              onChange={handleStoreChange}
              className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base text-gray-900 focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
            >
              <option value="all">All Stores</option>
              {storesData?.map((store) => (
                <option key={store.id} value={store.id}>
                  {store.name}
                </option>
              ))}
            </select>
          </div>
          
          {/* Status filter */}
          <div className="mb-4">
            <label htmlFor="status-mobile" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status-mobile"
              name="status"
              value={filters.status}
              onChange={handleStatusChange}
              className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base text-gray-900 focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
            >
              <option value="active">Active Deals</option>
              <option value="expired">Expired Deals</option>
              <option value="all">All Deals</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* Desktop horizontal filter bar - only visible on sm+ screens */}
      <div className="hidden sm:flex mb-4 bg-white rounded-md border border-gray-200 p-2 shadow-sm justify-between items-center">
        {/* Category filter */}
        <div className="flex-1 px-2">
          <select
            id="category-desktop"
            name="category"
            value={filters.category || 'all'}
            onChange={handleCategoryChange}
            className="block w-full rounded-md border-gray-300 py-1.5 pl-3 pr-10 text-gray-900 focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
          >
            <option value="all">All Categories</option>
            {categoriesData?.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
        
        {/* Store filter */}
        <div className="flex-1 px-2">
          <select
            id="store-desktop"
            name="store"
            value={filters.store || 'all'}
            onChange={handleStoreChange}
            className="block w-full rounded-md border-gray-300 py-1.5 pl-3 pr-10 text-gray-900 focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
          >
            <option value="all">All Stores</option>
            {storesData?.map((store) => (
              <option key={store.id} value={store.id}>
                {store.name}
              </option>
            ))}
          </select>
        </div>
        
        {/* Status filter */}
        <div className="px-2">
          <select
            id="status-desktop"
            name="status"
            value={filters.status}
            onChange={handleStatusChange}
            className="block w-full rounded-md border-gray-300 py-1.5 pl-3 pr-10 text-gray-900 focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
          >
            <option value="active">Active Deals</option>
            <option value="expired">Expired Deals</option>
            <option value="all">All Deals</option>
          </select>
        </div>
        
        {/* Clear filters button */}
        {(filters.search || filters.category || filters.store || filters.status !== 'active') && (
          <div className="px-2">
            <button
              onClick={handleClearFilters}
              className="py-1.5 px-2 text-xs text-primary-600 hover:text-primary-800 hover:underline"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
      
      {/* Active filters display */}
      {(filters.search || filters.category || filters.store || filters.status !== 'active') && (
        <div className="mt-4 flex flex-wrap gap-2">
          {filters.search && (
            <span className="inline-flex items-center rounded-full bg-primary-100 px-3 py-1 text-xs font-medium text-primary-800">
              Search: {filters.search}
              <button
                type="button"
                className="ml-1.5 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-primary-800 hover:text-primary-900 focus:outline-none"
                onClick={() => onFilterChange({ ...filters, search: undefined, page: 1 })}
              >
                <span className="sr-only">Remove search filter</span>
                <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                  <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
                </svg>
              </button>
            </span>
          )}
          
          {filters.category && categoriesData && (
            <span className="inline-flex items-center rounded-full bg-secondary-100 px-3 py-1 text-xs font-medium text-secondary-800">
              Category: {categoriesData.find(c => c.id === filters.category)?.name || 'Unknown'}
              <button
                type="button"
                className="ml-1.5 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-secondary-800 hover:text-secondary-900 focus:outline-none"
                onClick={() => onFilterChange({ ...filters, category: undefined, page: 1 })}
              >
                <span className="sr-only">Remove category filter</span>
                <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                  <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
                </svg>
              </button>
            </span>
          )}
          
          {filters.store && storesData && (
            <span className="inline-flex items-center rounded-full bg-accent-100 px-3 py-1 text-xs font-medium text-accent-800">
              Store: {storesData.find(s => s.id === filters.store)?.name || 'Unknown'}
              <button
                type="button"
                className="ml-1.5 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-accent-800 hover:text-accent-900 focus:outline-none"
                onClick={() => onFilterChange({ ...filters, store: undefined, page: 1 })}
              >
                <span className="sr-only">Remove store filter</span>
                <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                  <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
                </svg>
              </button>
            </span>
          )}
          
          {filters.status && filters.status !== 'active' && (
            <span className="inline-flex items-center rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-800">
              Status: {filters.status.charAt(0).toUpperCase() + filters.status.slice(1)}
              <button
                type="button"
                className="ml-1.5 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-gray-800 hover:text-gray-900 focus:outline-none"
                onClick={() => onFilterChange({ ...filters, status: 'active', page: 1 })}
              >
                <span className="sr-only">Remove status filter</span>
                <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                  <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
                </svg>
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default FilterBar;
