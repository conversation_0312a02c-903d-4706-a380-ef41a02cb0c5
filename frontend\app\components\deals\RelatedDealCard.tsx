'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image'; // Use Next.js Image for optimization
import { Deal } from '@/src/types'; // Use @/ alias for src directory
import { getFullImageUrl, handleImageError } from '@/src/utils/imageUtils'; // Use @/ alias for src directory

interface RelatedDealCardProps {
  deal: Deal;
  currentUserId?: number | null; // Optional user ID for image URL logic
}

const RelatedDealCard: React.FC<RelatedDealCardProps> = ({ deal, currentUserId }) => {
  const imageUrl = getFullImageUrl(deal, {
    currentUserId: currentUserId,
    forceOwner: deal.userId === currentUserId
  });

  // Construct the deal detail URL
  const dealUrl = `/dealDetail/${deal.id}`;

  return (
    <Link href={dealUrl} className="flex gap-3 group border border-transparent hover:border-gray-200 hover:bg-gray-50 rounded-lg p-2 transition-all duration-200">
      <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 relative">
        <Image
          src={imageUrl}
          alt={deal.title}
          fill // Use fill to make the image cover the container
          style={{ objectFit: 'cover' }} // Ensure the image covers the area
          className="group-hover:scale-105 transition-transform duration-300"
          onError={handleImageError} // Use the existing error handler
          sizes="(max-width: 768px) 10vw, (max-width: 1200px) 8vw, 80px" // Provide sizes for responsive images
          priority={false} // Related deals are likely not above the fold
        />
      </div>
      <div>
        <h4 className="text-sm font-medium text-gray-800 group-hover:text-orange-500 transition-colors line-clamp-2">
          {deal.title}
        </h4>
        <div className="text-sm font-semibold text-orange-500 mt-1">
          {deal.price ? `£${deal.price.toFixed(2)}` : ''}
          {deal.originalPrice && deal.originalPrice > (deal.price || 0) && (
            <span className="ml-1 text-xs line-through text-gray-400">
              £{deal.originalPrice.toFixed(2)}
            </span>
          )}
        </div>
        <div className="text-xs text-gray-500 mt-0.5">
          {deal.store?.name || deal.storeName || 'Unknown Store'}
        </div>
      </div>
    </Link>
  );
};

export default RelatedDealCard;
