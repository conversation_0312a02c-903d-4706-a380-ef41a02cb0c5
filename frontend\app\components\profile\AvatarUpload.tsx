import React, { useState, useRef } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { UserCircleIcon, CameraIcon } from '@heroicons/react/24/outline';
import { uploadAvatar } from '@/services/userService';
import { useAuth } from '@/hooks/useAuth';

interface AvatarUploadProps {
  currentAvatar?: string;
  size?: 'sm' | 'md' | 'lg';
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({ 
  currentAvatar,
  size = 'md'
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const { user, updateUser } = useAuth();
  const queryClient = useQueryClient();
  
  // Set size classes
  const sizeClasses = {
    sm: 'h-12 w-12',
    md: 'h-20 w-20',
    lg: 'h-32 w-32',
  };
  
  const iconSizeClasses = {
    sm: 'h-10 w-10',
    md: 'h-16 w-16',
    lg: 'h-24 w-24',
  };
  
  const uploadBtnClasses = {
    sm: 'h-6 w-6 p-1',
    md: 'h-8 w-8 p-1.5',
    lg: 'h-10 w-10 p-2',
  };
  
  // Upload avatar mutation
  const uploadAvatarMutation = useMutation({
    mutationFn: (file: File) => uploadAvatar(user!.id, file),
    onSuccess: (data) => {
      // Update user in auth context
      updateUser(data);
      // Invalidate user profile cache
      queryClient.invalidateQueries({ queryKey: ['userProfile', user?.id] });
      toast.success('Avatar uploaded successfully');
      // Clear preview
      setPreviewUrl(null);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to upload avatar');
    }
  });
  
  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('File too large. Maximum size is 2MB.');
      return;
    }
    
    // Check file type
    if (!file.type.startsWith('image/')) {
      toast.error('Only image files are allowed.');
      return;
    }
    
    // Create preview
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);
    
    // Upload file
    uploadAvatarMutation.mutate(file);
    
    // Reset input
    event.target.value = '';
  };
  
  return (
    <div className="relative">
      <div 
        className={`${sizeClasses[size]} overflow-hidden rounded-full bg-gray-200 cursor-pointer`}
        onClick={handleAvatarClick}
      >
        {previewUrl ? (
          <img 
            src={previewUrl}
            alt="Avatar preview" 
            className="h-full w-full object-cover"
          />
        ) : currentAvatar ? (
          <img 
            src={currentAvatar}
            alt="User avatar" 
            className="h-full w-full object-cover"
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-gray-100">
            <UserCircleIcon className={`${iconSizeClasses[size]} text-gray-400`} />
          </div>
        )}
      </div>
      
      <button
        type="button"
        className={`${uploadBtnClasses[size]} absolute bottom-0 right-0 rounded-full bg-deal-orange text-white shadow-sm hover:bg-deal-orange-dark focus:outline-none focus:ring-2 focus:ring-deal-orange focus:ring-offset-2`}
        onClick={handleAvatarClick}
      >
        <CameraIcon className="h-full w-full" />
      </button>
      
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        className="hidden"
        onChange={handleFileChange}
      />
      
      {uploadAvatarMutation.isPending && (
        <div className="mt-2 text-xs text-gray-500">
          Uploading...
        </div>
      )}
    </div>
  );
};

export default AvatarUpload;
