'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  ChevronUpIcon,
  ChevronDownIcon,
  ShareIcon,
  HeartIcon,
  TrashIcon,
  PencilIcon,
  ArrowLeftIcon,
  HandThumbUpIcon,
  UsersIcon,
  ShoppingBagIcon,
  CalendarIcon,
  ChatBubbleLeftIcon,
  ArrowTopRightOnSquareIcon,
  GiftIcon,
  ClipboardIcon,
  CheckIcon,
  ClockIcon,
  LinkIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartIconSolid,
} from '@heroicons/react/24/solid';
import toast from 'react-hot-toast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '@/hooks/useAuth';
import { Deal, User, Category, Store } from '@/types';
import { saveDeal, unsaveDeal, fetchSavedDealIds, upvoteDeal, downvoteDeal, removeVote, deleteDeal } from '@/services/dealService';
import CommentSection from '@/components/comments/CommentSection';
import { getFullImageUrl, handleImageError } from '@/utils/imageUtils';
import { Tabs, TabsList, TabsContent, TabsTrigger } from "@/components/ui/tabs";
import { formatPrice, formatRelativeTime, getAffiliateUrl } from '@/utils/formatters';
import { format } from 'date-fns';

interface DealDetailClientProps {
  initialDeal: Deal;
  relatedDeals: Deal[];
}

const DEBUG = 0;
const debugLog = (...args: any[]) => {
  if (DEBUG > 0) {
    console.log(...args);
  }
};

const logVoteCounts = (deal: Deal | undefined) => {
  if (!deal || DEBUG === 0) return;
  debugLog('Deal vote details:', {
    id: deal.id,
    temperature: deal.temperature,
    upvotes: deal.upvotes,
    downvotes: deal.downvotes,
    totalVotes: deal.totalVotes,
    userVote: deal.userVote,
  });
};

const DealDetailClient: React.FC<DealDetailClientProps> = ({ initialDeal, relatedDeals }) => {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const queryClient = useQueryClient();

  const [deal, setDeal] = useState<Deal>(initialDeal);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [copied, setCopied] = useState(false);
  const [mainImage, setMainImage] = useState<string | undefined>(undefined);
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  useEffect(() => {
    setDeal(initialDeal);
    setShowDeleteModal(false);
    setCopied(false);
    if (initialDeal?.imageUrl) {
      setMainImage(getFullImageUrl(initialDeal));
    } else {
      setMainImage(undefined);
    }
    logVoteCounts(initialDeal);
  }, [initialDeal]);

  const { data: savedDealIds = [], isLoading: isLoadingSavedDeals } = useQuery({
    queryKey: ['savedDealIds'],
    queryFn: fetchSavedDealIds,
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  const isSaved = React.useMemo(() => {
    if (!deal?.id || !isAuthenticated) return false;
    return savedDealIds.includes(deal.id);
  }, [savedDealIds, deal?.id, isAuthenticated]);

  // Define the expected response type for vote operations
  interface VoteResponse {
    upvotes: number;
    downvotes: number;
    userVote: 1 | -1 | 0;
    temperature: number;
  }

  const voteMutation = useMutation<Deal, Error, { voteValue: 1 | -1 }, { previousDeal?: Deal }>({
    mutationFn: async ({ voteValue }: { voteValue: 1 | -1 }) => {
      if (!isAuthenticated || !user?.id || !deal?.id) throw new Error('Please log in to vote.');
      if (deal.userId === user.id) throw new Error('You cannot vote on your own deal.');

      const currentVote = deal.userVote;
      let newUpvotes = deal.upvotes || 0;
      let newDownvotes = deal.downvotes || 0;
      let newUserVote: 1 | -1 | 0 = voteValue;

      // Calculate new vote counts based on the action
      if (currentVote === voteValue) {
        // Removing vote
        newUserVote = 0;
        if (voteValue === 1) {
          newUpvotes = Math.max(0, newUpvotes - 1);
        } else {
          newDownvotes = Math.max(0, newDownvotes - 1);
        }
      } else {
        // Changing or adding vote
        // First remove existing vote if any
        if (currentVote === 1) {
          newUpvotes = Math.max(0, newUpvotes - 1);
        } else if (currentVote === -1) {
          newDownvotes = Math.max(0, newDownvotes - 1);
        }
        
        // Then add the new vote
        if (voteValue === 1) {
          newUpvotes += 1;
        } else {
          newDownvotes += 1;
        }
      }

      // Make the API call
      try {
        if (currentVote === voteValue) {
          debugLog(`Removing vote for deal ${deal.id}`);
          await removeVote(deal.id);
        } else if (voteValue === 1) {
          debugLog(`Upvoting deal ${deal.id}`);
          await upvoteDeal(deal.id);
        } else {
          debugLog(`Downvoting deal ${deal.id}`);
          await downvoteDeal(deal.id);
        }

        // Return the updated deal object
        return {
          ...deal,
          upvotes: newUpvotes,
          downvotes: newDownvotes,
          userVote: newUserVote,
          temperature: newUpvotes - newDownvotes
        } as Deal;
      } catch (error) {
        console.error('Vote operation failed:', error);
        throw error;
      }
    },
    // When the vote mutation starts, immediately update the UI optimistically
    onMutate: async ({ voteValue }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['deal', deal?.id] });
      
      // Snapshot the previous value
      const previousDeal = queryClient.getQueryData<Deal>(['deal', deal?.id]);
      debugLog("Previous deal state onMutate:", previousDeal);

      if (previousDeal) {
        // Calculate new vote counts and user vote state
        let newUserVote: 1 | -1 | 0 = voteValue;
        let newUpvotes = previousDeal.upvotes ?? 0;
        let newDownvotes = previousDeal.downvotes ?? 0;
        const currentVote = previousDeal.userVote;

        // Logic for toggling votes - clicking the same button twice removes the vote
        if (currentVote === voteValue) {
          // If clicking the same vote type, remove the vote
          newUserVote = 0;
          if (voteValue === 1) {
            newUpvotes = Math.max(0, newUpvotes - 1);
          } else {
            newDownvotes = Math.max(0, newDownvotes - 1);
          }
          debugLog(`Optimistic: Removing vote. New counts: Up=${newUpvotes}, Down=${newDownvotes}`);
        } else {
          // If changing vote or adding new vote
          
          // First remove existing vote if any
          if (currentVote === 1) {
            newUpvotes = Math.max(0, newUpvotes - 1);
          } else if (currentVote === -1) {
            newDownvotes = Math.max(0, newDownvotes - 1);
          }

          // Then add the new vote
          if (voteValue === 1) {
            newUpvotes += 1;
          } else {
            newDownvotes += 1;
          }
          
          debugLog(`Optimistic: Adding/Changing vote to ${voteValue}. New counts: Up=${newUpvotes}, Down=${newDownvotes}`);
        }

        // Calculate new temperature based on vote counts
        const newTemperature = newUpvotes - newDownvotes;

        // Update the deal state immediately with our optimistic values
        const updatedDeal = { 
          ...previousDeal, 
          temperature: newTemperature, 
          upvotes: newUpvotes, 
          downvotes: newDownvotes, 
          userVote: newUserVote 
        };
        
        // Update the UI with optimistic values
        setDeal(updatedDeal);
        
        // Also update the React Query cache with our optimistic data
        queryClient.setQueryData(['deal', deal?.id], updatedDeal);
        
        logVoteCounts(updatedDeal);
      }
      
      return { previousDeal };
    },
    onError: (error: any, variables, context) => {
      // Check if this is the expected 'already voted' error from the backend
      if (error.response?.data?.error === 'You have already voted on this deal') {
        toast('You have already voted on this deal', {
          icon: '⚠️',
          style: {
            background: '#fef3c7',
            color: '#92400e',
            border: '1px solid #f59e0b',
          },
        });
      } else {
        toast.error(error.message || 'Failed to process vote.');
      }
      
      if (context?.previousDeal) {
        debugLog("Vote error, rolling back to:", context.previousDeal);
        setDeal(context.previousDeal);
        logVoteCounts(context.previousDeal);
      }
    },
    onSuccess: (updatedDeal) => {
      debugLog("Vote successful, updated deal:", updatedDeal);
      
      // Update the deal state with the new data
      if (updatedDeal) {
        setDeal(updatedDeal);
        logVoteCounts(updatedDeal);
        
        // Show appropriate toast based on the user's vote
        if (updatedDeal.userVote === 0) {
          toast.success('Vote removed!');
        } else if (updatedDeal.userVote === 1) {
          toast.success('Deal upvoted!');
        } else if (updatedDeal.userVote === -1) {
          toast.success('Deal downvoted!');
        }
      }
    },
    onSettled: () => {
      debugLog("Invalidating deal query after vote settled.");
      queryClient.invalidateQueries({ queryKey: ['deal', deal?.id] });
    },
  });

  const saveToggleMutation = useMutation({
    mutationFn: async (action: 'save' | 'unsave') => {
      if (!isAuthenticated || !user?.id || !deal?.id) throw new Error('Please log in to save deals.');

      if (action === 'save') {
        debugLog(`Saving deal ${deal.id}`);
        return saveDeal(deal.id);
      } else {
        debugLog(`Unsaving deal ${deal.id}`);
        return unsaveDeal(deal.id);
      }
    },
    onError: (error: any, action, context) => {
      toast.error(error.message || `Failed to ${action} deal.`);
      debugLog(`${action} mutation error:`, error);
    },
    onSuccess: (data, action) => {
      queryClient.invalidateQueries({ queryKey: ['savedDealIds'] });
      toast.success(`Deal ${action === 'save' ? 'saved' : 'unsaved'}!`);
      debugLog(`${action} mutation successful.`);
    },
  });

  const handleSaveDeal = () => {
    if (!isAuthenticated) {
      toast.error('Please log in to save deals.');
      return;
    }
    const action = isSaved ? 'unsave' : 'save';
    saveToggleMutation.mutate(action);
  };

  const deleteDealMutation = useMutation({
    mutationFn: async () => {
      if (!deal?.id) throw new Error('Deal ID is missing.');
      await deleteDeal(deal.id);
    },
    onSuccess: () => {
      toast.success('Deal deleted successfully!');
      queryClient.invalidateQueries({ queryKey: ['deals'] });
      router.push('/');
    },
    onError: (error: any) => {
      toast.error(`Failed to delete deal: ${error.message}`);
      debugLog('Delete mutation error:', error);
    },
    onSettled: () => {
      setShowDeleteModal(false);
    },
  });

  const handleVote = (voteValue: 1 | -1) => {
    debugLog(`Vote button clicked: ${voteValue}`);
    
    // Client-side validation to match CSR implementation
    if (!isAuthenticated) {
      toast.error('Please log in to vote');
      return;
    }

    if (deal?.userId === user?.id) {
      toast.error('You cannot vote on your own deal');
      return;
    }

    // If already voted with the same vote type, allow toggling (removing) the vote
    // If voted differently, allow changing the vote
    // If not voted, allow voting
    voteMutation.mutate({ voteValue });
  };

  const handleDeleteDeal = () => {
    debugLog("Confirm delete button clicked.");
    deleteDealMutation.mutate();
  };

  const copyCode = (text: string | undefined | null) => {
    if (!text) return;
    navigator.clipboard.writeText(text)
      .then(() => {
        setCopied(true);
        toast.success(`Code "${text}" copied to clipboard!`);
        setTimeout(() => setCopied(false), 3000);
      })
      .catch(err => {
        console.error('Failed to copy code: ', err);
        toast.error('Failed to copy code.');
      });
  };

  const toggleSection = (sectionTitle: string) => {
    setExpandedSection(prev => prev === sectionTitle ? null : sectionTitle);
    debugLog(`Toggled description section: ${sectionTitle}`);
  };

  const parseDescription = (description: string | undefined): Array<{ title: string | null; content: string[] }> => {
    if (!description) return [{ title: null, content: ['No description available.'] }];

    const sections = description.split(/\n\s*\n/);

    if (sections.length <= 1 && description.trim()) {
      return [{ title: null, content: description.split('\n').filter(line => line.trim() !== '') }];
    }

    return sections.map((sectionText, index) => ({
      title: `Section ${index + 1}`,
      content: sectionText.split('\n').filter(line => line.trim() !== '')
    }));
  };

  if (!deal) {
    return <div className="container mx-auto px-4 py-8 text-center">Loading deal details...</div>;
  }

  const descriptionSections = parseDescription(deal.description);
  const dealUrl = deal.url ? getAffiliateUrl(deal) : '#';
  const canEdit = isAuthenticated && user?.id === deal.userId;
  const temperatureColor = (deal.temperature || 0) > 50 ? 'text-red-600' : (deal.temperature || 0) > 10 ? 'text-orange-500' : 'text-blue-600';
  const upvoteStyle = `p-2 rounded-full transition-colors ${deal.userVote === 1 ? "bg-orange-100 text-orange-600" : "text-gray-500 hover:bg-gray-100 hover:text-gray-700"}`;
  const downvoteStyle = `p-2 rounded-full transition-colors ${deal.userVote === -1 ? "bg-blue-100 text-blue-600" : "text-gray-500 hover:bg-gray-100 hover:text-gray-700"}`;

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-0 lg:px-8">
        <button onClick={() => router.back()} className="mb-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-orange-500 transition-colors">
          <ArrowLeftIcon className="mr-2 h-5 w-5" />
          Back to Deals
        </button>

        <div className="flex flex-col gap-6">
          <div className="glass rounded-xl bg-gradient-to-r from-orange-500/90 to-orange-600/90 text-white p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="bg-white/20 rounded-full p-2">
                <HandThumbUpIcon className="w-6 h-6" />
              </div>
              <div>
                <div>
                  <h2 className="text-xl font-bold">Deal Score: {deal?.temperature || 0}</h2>
                  <p className="text-sm text-white/80">Based on community votes</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className="flex flex-col items-center">
                <button
                  className={`rounded-full p-2 transition-colors ${
                    deal?.userVote === 1
                      ? 'bg-white text-orange-500'
                      : 'text-white hover:bg-white/20'
                  }`}
                  onClick={() => handleVote(1)}
                  disabled={voteMutation.isPending}
                  title={!isAuthenticated ? "Please log in to vote" :
                    deal?.userId === user?.id ? "You cannot vote on your own deal" : 
                    deal?.userVote === 1 ? "Click to remove your upvote" : 
                    deal?.userVote === -1 ? "Change your vote to an upvote" : 
                    "Vote up"}
                  aria-label="Upvote"
                >
                  <ChevronUpIcon className="w-5 h-5" />
                </button>
                <span className="text-sm font-medium">
                  {deal?.upvotes || 0}
                </span>
              </div>

              <div className="flex flex-col items-center">
                <button
                  className={`rounded-full p-2 transition-colors ${
                    deal?.userVote === -1
                      ? 'bg-white text-orange-500'
                      : 'text-white hover:bg-white/20'
                  }`}
                  onClick={() => handleVote(-1)}
                  disabled={voteMutation.isPending}
                  title={!isAuthenticated ? "Please log in to vote" :
                    deal?.userId === user?.id ? "You cannot vote on your own deal" : 
                    deal?.userVote === -1 ? "Click to remove your downvote" : 
                    deal?.userVote === 1 ? "Change your vote to a downvote" : 
                    "Vote down"}
                  aria-label="Downvote"
                >
                  <ChevronDownIcon className="w-5 h-5" />
                </button>
                <span className="text-sm font-medium">
                  {deal?.downvotes || 0}
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
            <div className="lg:col-span-2">
              <div className="glass rounded-xl overflow-hidden">
                <div className="aspect-square relative overflow-hidden rounded-t-xl">
                  {mainImage ? (
                    <Image
                      src={mainImage}
                      alt={deal.title || 'Deal image'}
                      fill
                      style={{ objectFit: 'contain' }}
                      onError={(e: React.SyntheticEvent<HTMLImageElement>) => handleImageError(e)}
                      priority
                      unoptimized={process.env.NODE_ENV === 'development'}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full bg-gray-100 text-gray-400">
                      <ShoppingBagIcon className="w-24 h-24" />
                    </div>
                  )}
                  <div className="absolute top-4 left-4">
                    {/* Status badges */}
                    {/* Add badges here */}
                  </div>
                  <button
                    className="absolute top-4 right-4 p-2 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white transition-colors"
                    onClick={handleSaveDeal}
                    disabled={saveToggleMutation.isPending || !isAuthenticated}
                    title={!isAuthenticated ? "Please log in to save deals" : (isSaved ? "Remove from saved deals" : "Save this deal")}
                    aria-label={isSaved ? 'Unsave Deal' : 'Save Deal'}
                  >
                    {isSaved ?
                      <HeartIconSolid className="w-5 h-5 text-red-500" /> :
                      <HeartIcon className="w-5 h-5 text-gray-600" />
                    }
                  </button>
                </div>
              </div>
              <div className="mt-4 flex items-center gap-4">
                {/* Add Share functionality if needed */}
                <button className="text-gray-500 hover:text-orange-500 transition-colors flex items-center gap-1 text-sm">
                  <ShareIcon className="w-4 h-4" />
                  Share
                </button>
                {/* Link to comments tab (will be handled in Part 2) */}
                <button className="text-gray-500 hover:text-orange-500 transition-colors flex items-center gap-1 text-sm" onClick={() => {/* TODO: Scroll/Switch to comments tab */}}>
                  <ChatBubbleLeftIcon className="w-4 h-4" />
                  Comment ({deal.commentCount || 0})
                </button>
              </div>
            </div>

            <div className="lg:col-span-3">
              <div className="glass rounded-xl p-6">
                <div className="flex items-start justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      {deal.category && (
                        <Link href={`/deals?category=${deal.categoryId}`} className="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full hover:bg-blue-200 transition-colors">
                          {deal.category.name}
                        </Link>
                      )}
                      <div className="text-xs text-gray-500">
                        Posted {deal.createdAt ? format(new Date(deal.createdAt), 'PPP') : 'N/A'} by {deal.userUsername || 'System'}
                      </div>
                    </div>

                    <h1 className="text-2xl font-bold text-gray-800 mb-2">{deal.title}</h1>

                    <div className="flex items-center gap-2">
                      {deal.store && (
                        <div className="flex items-center gap-1">
                          {deal.store.logoUrl && (
                            <Image
                              src={deal.store.logoUrl}
                              alt={deal.store.name || 'Store logo'}
                              width={24}
                              height={24}
                              className="w-6 h-6 rounded-full"
                              onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                                e.currentTarget.src = '/images/placeholder-store.png';
                              }}
                            />
                          )}
                          <span className="text-sm text-gray-600">{deal.store.name}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-1 text-sm">
                        <UsersIcon className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-600">{(deal.upvotes || 0) + (deal.downvotes || 0)} votes</span>
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    {deal.price && (
                      <div className="text-3xl font-bold text-orange-500">£{deal.price.toFixed(2)}</div>
                    )}
                    {deal.originalPrice && deal.originalPrice > (deal.price || 0) && (
                      <div className="text-sm line-through text-gray-400">£{deal.originalPrice.toFixed(2)}</div>
                    )}
                    {/* Add savings amount if needed */}
                  </div>
                </div>

                {/* Deal Status - Expiry Date */}
                <div className="mt-6 flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    {deal.expiresAt && (
                      <div className="flex items-center gap-1 text-sm">
                        <CalendarIcon className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-600">Expires: {format(new Date(deal.expiresAt), 'PPP')}</span>
                      </div>
                    )}
                  </div>
                  {/* Placeholder for potential future status icons */}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 mt-6">
                {deal.url && (
                  <a
                    href={getAffiliateUrl(deal)} // Pass the whole deal object
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center rounded-md bg-orange-500 px-4 py-3 text-sm font-medium text-white shadow-sm hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 flex-1 h-12"
                  >
                    <ArrowTopRightOnSquareIcon className="w-5 h-5 mr-2" />
                    Get This Deal
                  </a>
                )}

                <button
                  onClick={handleSaveDeal} 
                  className={`inline-flex items-center justify-center rounded-md px-4 py-3 text-sm font-medium shadow-sm flex-1 h-12 border ${
                    isSaved
                      ? 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100'
                      : 'border-orange-500 text-orange-500 hover:bg-orange-50'
                  } transition-colors duration-200`}
                  disabled={saveToggleMutation.isPending || !isAuthenticated} 
                  title={!isAuthenticated ? "Please log in to save deals" : ""}
                >
                  {isSaved ? (
                    <CheckIcon className="w-5 h-5 mr-2" />
                  ) : (
                    <HeartIcon className="w-5 h-5 mr-2" />
                  )}
                  {isSaved ? 'Saved' : 'Save Deal'}
                </button>
              </div>

            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 mt-8">
            <div className="lg:col-span-3">
              <Tabs defaultValue="description" className="glass rounded-xl p-6">
                <TabsList className="grid w-full grid-cols-3 mb-6">
                  <TabsTrigger value="description">Description</TabsTrigger>
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="comments">Comments ({deal.commentCount || 0})</TabsTrigger>
                </TabsList>

                <TabsContent value="description" className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 flex items-center mb-3">
                      <ShoppingBagIcon className="w-5 h-5 mr-2 text-orange-500" />
                      Deal Description
                    </h3>

                    <div className="pl-7 prose max-w-none text-gray-700 whitespace-pre-wrap">
                      {deal.description || 'No description provided.'}
                    </div>
                  </div>

                  {canEdit && (
                    <div className="flex justify-end gap-3 pl-7 pt-3 border-t border-gray-200">
                      <Link
                        href={`/deals/edit/${deal.id}`}
                        className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                      >
                        <PencilIcon className="mr-2 h-4 w-4 text-gray-500" /> Edit
                      </Link>
                      <button
                        onClick={() => setShowDeleteModal(true)}
                        className="inline-flex items-center rounded-md border border-transparent bg-red-100 px-4 py-2 text-sm font-medium text-red-700 hover:bg-red-200"
                      >
                        <TrashIcon className="mr-2 h-4 w-4 text-red-500" /> Delete
                      </button>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="details" className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 flex items-center mb-3">
                      <ClockIcon className="w-5 h-5 mr-2 text-orange-500" />
                      Deal Details
                    </h3>

                    <ul className="pl-7 space-y-2 text-gray-600">
                      {deal.store && (
                        <li className="flex items-start">
                          <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Available at: {deal.store.name}</span>
                        </li>
                      )}
                      {deal.category && (
                        <li className="flex items-start">
                          <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Category: {deal.category.name}</span>
                        </li>
                      )}
                      {deal.expiresAt && (
                        <li className="flex items-start">
                          <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Expires on: {format(new Date(deal.expiresAt), 'PPP')}</span>
                        </li>
                      )}
                      {deal.userUsername && (
                        <li className="flex items-start">
                          <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Posted by: {deal.userUsername}</span>
                        </li>
                      )}
                      {deal.createdAt && (
                        <li className="flex items-start">
                          <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Posted on: {format(new Date(deal.createdAt), 'PPP')}</span>
                        </li>
                      )}

                    </ul>
                  </div>
                </TabsContent>

                <TabsContent value="comments" className="space-y-6">
                  <CommentSection dealId={deal.id} />
                </TabsContent>
              </Tabs>
            </div>

            <div className="lg:col-span-2">
              <div className="glass rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <ShoppingBagIcon className="w-5 h-5 mr-2 text-orange-500" />
                  Related Deals
                </h3>

                <div className="space-y-4">
                  {relatedDeals && relatedDeals.length > 0 ? (
                    relatedDeals.map((relatedDeal) => (
                      <Link
                        key={relatedDeal.id}
                        href={`/dealDetail/${relatedDeal.id}`}
                        className="flex gap-3 group"
                      >
                        <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 relative">
                          <Image
                            src={getFullImageUrl(relatedDeal, { currentUserId: user?.id, forceOwner: relatedDeal.userId === user?.id })}
                            alt={relatedDeal.title}
                            fill
                            style={{ objectFit: 'cover' }}
                            className="group-hover:scale-105 transition-transform"
                            onError={handleImageError}
                          />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-800 group-hover:text-orange-500 transition-colors line-clamp-2">
                            {relatedDeal.title}
                          </h4>
                          <div className="text-sm font-semibold text-orange-500 mt-1">
                            {relatedDeal.price ? `£${relatedDeal.price.toFixed(2)}` : ''}
                            {relatedDeal.originalPrice && relatedDeal.originalPrice > (relatedDeal.price || 0) && (
                              <span className="ml-1 text-xs line-through text-gray-400">
                                £{relatedDeal.originalPrice.toFixed(2)}
                              </span>
                            )}
                          </div>
                          <div className="text-xs text-gray-500">
                            {relatedDeal.store?.name || 'Unknown Store'}
                          </div>
                        </div>
                      </Link>
                    ))
                  ) : (
                    <div className="text-sm text-gray-500 italic">
                      No related deals found
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DealDetailClient;
