'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { CheckCircleIcon } from '@heroicons/react/24/outline';
import { Deal } from '@/types';
import { useAuth } from '@/hooks/useAuth';
import api from '@/services/api';

interface PendingDealClientProps {
  dealId: number;
}

export default function PendingDealClient({ dealId }: PendingDealClientProps) {
  const { isAuthenticated, user, token, loading } = useAuth();
  const router = useRouter();
  const [deal, setDeal] = useState<Deal | null>(null);
  const [loadingState, setLoadingState] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (loading) { 
      return;
    }

    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    const fetchDeal = async () => {
      try {
        setLoadingState(true);
        
        // Set the auth token directly in headers for this specific request
        const headers: Record<string, string> = {};
        if (token) {
          headers.Authorization = `Bearer ${token}`;
          console.log('Setting authorization header with token for deal', dealId);
        }

        // Make a direct request to the API
        const response = await api.get(`/deals/${dealId}`, { 
          headers,
          params: { userId: user?.id }  
        });
        
        console.log('API response:', response.data);
        
        const dealData = response.data.data || response.data;
        setDeal(dealData);
        setError(null);
      } catch (err: any) {
        console.error('Error fetching pending deal:', err);
        
        if (err.response?.data?.isPendingDeal) {
          setError("You don't have permission to view this pending deal.");
        } else {
          setError('Failed to load deal details. Please try again.');
        }
      } finally {
        setLoadingState(false);
      }
    };

    fetchDeal();
  }, [dealId, isAuthenticated, router, user?.id, token, loading]);

  if (loading || loadingState) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-deal-orange"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-8">
        <div className="bg-red-50 p-4 rounded-md mb-8">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Deal</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <Link 
                  href="/user/deals" 
                  className="text-sm font-medium text-deal-orange hover:text-deal-orange-dark"
                >
                  Return to My Deals
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!deal) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-8">
        <div className="bg-yellow-50 p-4 rounded-md mb-8">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">No Deal Found</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>The deal you're looking for could not be found.</p>
              </div>
              <div className="mt-4">
                <Link 
                  href="/user/deals" 
                  className="text-sm font-medium text-deal-orange hover:text-deal-orange-dark"
                >
                  Return to My Deals
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-6xl px-4 py-8">
      <div className="rounded-md bg-green-50 p-4 mb-8">
        <div className="flex">
          <div className="flex-shrink-0">
            <CheckCircleIcon className="h-5 w-5 text-green-400" aria-hidden="true" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-green-800">Pending Deal</h3>
            <div className="mt-2 text-sm text-green-700">
              <p>
                This deal is currently awaiting approval by our moderators. Once approved, it will be visible to all users.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="overflow-hidden bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Pending Deal Details</h3>
          
          <div className="mt-5 border-t border-gray-200">
            <dl className="divide-y divide-gray-200">
              <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                <dt className="text-sm font-medium text-gray-500">Title</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{deal.title}</dd>
              </div>
              
              <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                <dt className="text-sm font-medium text-gray-500">Price</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                  {deal?.price !== undefined ? `£${deal.price.toFixed(2)}` : 'Price not available'} 
                  {deal?.originalPrice && deal.originalPrice > (deal?.price || 0) ? 
                    `(was £${deal.originalPrice.toFixed(2)})` : ''}
                </dd>
              </div>
              
              <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                <dt className="text-sm font-medium text-gray-500">Status</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                  <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                    Pending Review
                  </span>
                </dd>
              </div>
              
              {deal.url && (
                <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt className="text-sm font-medium text-gray-500">URL</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                    <a href={deal.url} target="_blank" rel="noopener noreferrer" className="text-deal-orange hover:underline">
                      {deal.url}
                    </a>
                  </dd>
                </div>
              )}
              
              {deal.description && (
                <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt className="text-sm font-medium text-gray-500">Description</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0 whitespace-pre-line">
                    {deal.description}
                  </dd>
                </div>
              )}
            </dl>
          </div>
          
          <div className="mt-8 flex space-x-4">
            <Link
              href="/user/deals"
              className="inline-flex items-center rounded-md border border-transparent bg-deal-orange px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-deal-orange-dark focus:outline-none focus:ring-2 focus:ring-deal-orange focus:ring-offset-2"
            >
              Back to My Deals
            </Link>
            
            <Link
              href="/"
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-deal-orange focus:ring-offset-2"
            >
              Browse Deals
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
