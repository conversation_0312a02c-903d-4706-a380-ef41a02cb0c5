import { Metadata, ResolvingMetadata } from 'next';
import { notFound } from 'next/navigation';
import React from 'react';
import { getDealById as getServerDealById } from '@/services/dealService.server';
import { Deal } from '@/types';
import { getFullImageUrl } from '@/utils/imageUtils';
import DealDetailClient from './DealDetailClient';
import PendingDealClient from './PendingDealClient';
import { cookies } from 'next/headers';

type Props = {
  params: { dealId: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

// --- 1. Generate Metadata --- 
export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const id = parseInt(params.dealId, 10);
  if (isNaN(id)) {
    return {
      title: 'Invalid Deal',
    };
  }

  try {
    // For metadata, we only attempt to get published deals
    // Pending deals will just have a generic title
    const deal = await getServerDealById(id);
    if (!deal) {
      return {
        title: 'Deal Not Found',
      };
    }

    const ogImage = deal.imageUrl ? getFullImageUrl(deal) : '/placeholder.png'; 
    const description = deal.description?.substring(0, 160) || 'Check out this great deal!'; // Truncate description

    return {
      title: `${deal.title} - Nice Deals`,
      description: description,
      openGraph: {
        title: deal.title,
        description: description,
        url: `${process.env.NEXT_PUBLIC_BASE_URL || ''}/dealDetail/${deal.id}`,
        siteName: 'Nice Deals',
        images: [
          {
            url: ogImage,
            width: 800, // Adjust dimensions as needed
            height: 600,
            alt: deal.title,
          },
        ],
        locale: 'en_GB',
        type: 'article', // or 'product' if more appropriate
      },
      twitter: {
        card: 'summary_large_image',
        title: deal.title,
        description: description,
        images: [ogImage],
      },
    };
  } catch (error: any) {
    // If it's a pending deal, just return a generic title
    // The client component will handle the permissions
    if (error.isPendingDeal) {
      return {
        title: 'Pending Deal - Nice Deals'
      };
    }
    
    console.error('[generateMetadata] Error fetching deal:', error);
    return {
      title: 'Error Loading Deal',
    };
  }
}

// --- 2. Page Component (Server Component) ---
export default async function DealDetailPage({ params }: Props) {
  const id = parseInt(params.dealId, 10);
  if (isNaN(id)) {
    notFound();
  }

  // Get user ID from cookies if available - for pending deal ownership verification
  const userCookie = cookies().get('user');
  let userId: number | undefined = undefined;
  
  if (userCookie?.value) {
    try {
      const userData = JSON.parse(userCookie.value);
      userId = userData.id;
    } catch (e) {
      console.error('[DealDetailPage] Error parsing user cookie:', e);
    }
  }

  try {
    // Get the deal data
    const deal = await getServerDealById(id);
    
    if (!deal) {
      notFound();
    }

    // If it's a pending deal, render the PendingDealClient
    if (deal.status === 'pending') {
      return (
        <div className="container mx-auto px-0 py-8">
          <PendingDealClient dealId={id} />
        </div>
      );
    }

    // For published deals, get related deals and render the DealDetailClient
    // Note: We'll need to implement getRelatedDeals in the server service if needed
    const relatedDeals: Deal[] = []; // Temporarily empty until we implement getRelatedDeals in the server service
    
    return (
      <div className="container mx-auto px-0 py-8">
        <DealDetailClient initialDeal={deal} relatedDeals={relatedDeals} />
      </div>
    );
    
  } catch (error: any) {
    console.error('[DealDetailPage] Error:', error);
    
    // If it's a pending deal but we couldn't fetch it, still show the PendingDealClient
    // The client component will handle the authentication
    if (error.isPendingDeal) {
      return (
        <div className="container mx-auto px-0 py-8">
          <PendingDealClient dealId={id} />
        </div>
      );
    }
    
    notFound();
  }
}
