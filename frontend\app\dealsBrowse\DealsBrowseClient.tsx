'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { PlusIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { 
  LayoutGrid, 
  List, 
  ArrowUpCircle, 
  Filter, 
  ChevronDown,
  ArrowUpDown
} from 'lucide-react';
import { getDeals } from '../services/dealService';
import { Deal, DealFilters, DealListResponse, Category, Store } from '../types';
import dynamic from 'next/dynamic';
import { useAuth } from '../hooks/useAuth';
// Using client-side hooks again as server-side rendering approach isn't working
import { useCategories } from '../hooks/useCategories';
import { useStores } from '../hooks/useStores';

// Import components with SSR disabled to avoid hydration issues
const GridDealCard = dynamic(() => import('../components/deals/GridDealCard'), { ssr: false });
const ListDealCard = dynamic(() => import('../components/deals/ListDealCard'), { ssr: false });
const PaginationComponent = dynamic(() => import('../components/common/Pagination'), { ssr: false });

// Price ranges for sidebar filtering
const priceRanges = [
  { name: 'Under £25', min: 0, max: 25 },
  { name: '£25 - £50', min: 25, max: 50 },
  { name: '£50 - £100', min: 50, max: 100 },
  { name: '£100 - £200', min: 100, max: 200 },
  { name: 'Over £200', min: 200, max: null }
];

// Deal types for sidebar filtering
const dealTypes = [
  { name: 'Hot Deals', filter: 'hot' },
  { name: 'Warm', filter: 'warm' },
  { name: 'Free', filter: 'free' },
  { name: 'Discounted', filter: 'discounted' }
];

// Hot threshold for deal temperature
const GETTING_WARM_TEMPERATURE = 1;

export interface DealsBrowseClientProps {
  initialFilters: DealFilters;
  initialDealsData: DealListResponse;
  initialCategories: Category[];
  initialStores: Store[];
}

export default function DealsBrowseClient({ 
  initialFilters, 
  initialDealsData,
  initialCategories,
  initialStores 
}: DealsBrowseClientProps) {
  const router = useRouter();
  const pathname = usePathname();
  const currentSearchParams = useSearchParams();
  const { isAuthenticated } = useAuth();
  
  // Refs
  const isInitialMount = useRef(true);
  
  // State for filters and UI
  const [filters, setFilters] = useState<DealFilters>(initialFilters);
  const [deals, setDeals] = useState<Deal[]>(initialDealsData?.deals || []);
  interface PaginationState {
    totalCount: number;
    page: number;
    pageSize: number;
    totalPages: number;
    from?: number;
    to?: number;
    currentPage?: number;
  }
  const [pagination, setPagination] = useState<PaginationState>({
    totalCount: initialDealsData?.totalCount || 0,
    page: initialDealsData?.page || 1,
    pageSize: initialDealsData?.pageSize || 30,
    totalPages: initialDealsData?.totalPages || 1,
    from: 0,
    to: 0,
    currentPage: initialDealsData?.page || 1
  });
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [showScrollButton, setShowScrollButton] = useState(false);

  // State for collapsible filter sections
  const [isCategoriesOpen, setIsCategoriesOpen] = useState(true);
  const [isPriceRangeOpen, setIsPriceRangeOpen] = useState(true);
  const [isStoresOpen, setIsStoresOpen] = useState(true);
  const [isDealTypeOpen, setIsDealTypeOpen] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  
  // We'll use a hybrid approach, prioritizing client-side fetching for now
  // but keeping the infrastructure for server-side rendering
  console.log('Initial categories from server:', initialCategories?.length || 0);
  console.log('Initial stores from server:', initialStores?.length || 0);
  
  // Use React Query hooks for client-side fetching
  const { categories: fetchedCategories, loading: categoriesLoading } = useCategories();
  const { stores: fetchedStores, loading: storesLoading } = useStores(filters.category);
  
  // Initialize state with server data if available, otherwise use empty arrays
  const [categories, setCategories] = useState<Category[]>(initialCategories ?? []);
  const [stores, setStores] = useState<Store[]>(initialStores ?? []);
  
  // Update categories from client-side hook
  useEffect(() => {
    if (initialCategories) {
      setCategories(initialCategories);
    } else if (fetchedCategories) {
      setCategories(fetchedCategories);
    }
  }, [initialCategories, fetchedCategories]);
  
  // Update stores from client-side hook
  useEffect(() => {
    if (initialStores) {
      setStores(initialStores);
    } else if (fetchedStores) {
      setStores(fetchedStores);
    }
  }, [initialStores, fetchedStores, filters.category]);
  
  // Log whenever categories or stores change
  useEffect(() => {
    console.log(`Categories state updated, now have ${categories.length} categories`);
  }, [categories]);
  
  useEffect(() => {
    console.log(`Stores state updated, now have ${stores.length} stores`);
  }, [stores]);
  
  // Effect to update deals and pagination when initialDealsData prop changes
  useEffect(() => {
    if (initialDealsData) {
      console.log('DealsBrowseClient: initialDealsData prop changed. Updating deals and pagination.');
      setDeals(initialDealsData.deals || []);
      setPagination({
        totalCount: initialDealsData.totalCount || 0,
        page: initialDealsData.page || 1,
        pageSize: initialDealsData.pageSize || 30,
        totalPages: initialDealsData.totalPages || 1,
        from: 0,
        to: 0,
        currentPage: initialDealsData.page || 1
      });
    }
  }, [initialDealsData]);

  // Effect to synchronize filters state with URL searchParams
  useEffect(() => {
    console.log('DealsBrowseClient: currentSearchParams changed. Synchronizing filters state.');
    const newFiltersFromURL: DealFilters = {
      page: parseInt(currentSearchParams.get('page') || initialFilters.page?.toString() || '1', 10),
      pageSize: parseInt(currentSearchParams.get('pageSize') || initialFilters.pageSize?.toString() || '30', 10),
      status: (currentSearchParams.get('status') as DealFilters['status']) || initialFilters.status || 'active',
      sort: (currentSearchParams.get('sort') as DealFilters['sort']) || initialFilters.sort || 'newest',
      search: currentSearchParams.get('search') || initialFilters.search || undefined,
      category: currentSearchParams.get('category') ? parseInt(currentSearchParams.get('category')!, 10) : initialFilters.category || undefined,
      store: currentSearchParams.get('store') ? parseInt(currentSearchParams.get('store')!, 10) : initialFilters.store || undefined,
      minPrice: currentSearchParams.get('minPrice') ? parseInt(currentSearchParams.get('minPrice')!, 10) : initialFilters.minPrice || undefined,
      maxPrice: currentSearchParams.get('maxPrice') ? parseInt(currentSearchParams.get('maxPrice')!, 10) : initialFilters.maxPrice || undefined,
      dealType: currentSearchParams.get('dealType') || initialFilters.dealType || undefined,
    };
    
    // Only update if there's a meaningful difference to avoid loops. 
    // A proper deep comparison function would be better for objects/arrays within filters if they exist.
    if (JSON.stringify(filters) !== JSON.stringify(newFiltersFromURL)) {
        console.log('DealsBrowseClient: Filters state updated from URL:', newFiltersFromURL);
        setFilters(newFiltersFromURL);
    }
  }, [currentSearchParams, initialFilters, filters]);

  // Sidebar state
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [selectedPriceRanges, setSelectedPriceRanges] = useState<string[]>([]);
  const [selectedDealTypes, setSelectedDealTypes] = useState<string[]>([]);
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  
  // Load selected price ranges and deal types from filters
  useEffect(() => {
    if (filters.minPrice !== undefined && filters.maxPrice !== undefined) {
      const matchingRange = priceRanges.find(
        range => range.min === filters.minPrice && range.max === filters.maxPrice
      );
      if (matchingRange) {
        setSelectedPriceRanges([matchingRange.name]);
      }
    } else if (filters.minPrice !== undefined) {
      // Only min price is set
      const matchingRange = priceRanges.find(
        range => range.min === filters.minPrice && range.max === null
      );
      if (matchingRange) {
        setSelectedPriceRanges([matchingRange.name]);
      }
    }
    
    if (filters.dealType) {
      const matchingType = dealTypes.find(type => type.filter === filters.dealType);
      if (matchingType) {
        setSelectedDealTypes([matchingType.name]);
      }
    }
  }, []);
  
  // Scroll to top on component mount or when page in filters changes
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [filters.page]);

  // State for sort dropdown
  const [isSortOpen, setIsSortOpen] = useState(false);
  const [tempSort, setTempSort] = useState<DealFilters['sort']>(filters.sort || 'newest');
  
  // Update tempSort when filters.sort changes (e.g., from URL)
  useEffect(() => {
    setTempSort(filters.sort || 'newest');
  }, [filters.sort]);
  
  // Handle sort change
  const handleSortChange = (sortValue: DealFilters['sort']) => {
    setTempSort(sortValue);
  };

  // Helper function to create SSR-friendly URLs
  const createSsrUrl = (params: Partial<DealFilters>) => {
    const newSearchParams = new URLSearchParams();
  
    // Include existing filters
    const baseFilters = { ...filters, ...params };
  
    // Add parameters to URL
    if (baseFilters.search) newSearchParams.set('search', baseFilters.search);
    if (baseFilters.category) newSearchParams.set('category', baseFilters.category.toString());
    if (baseFilters.store) newSearchParams.set('store', baseFilters.store.toString());
  
    // Always include status and sort parameters for consistent SSR navigation
    newSearchParams.set('status', baseFilters.status || 'active');
    newSearchParams.set('sort', baseFilters.sort || 'newest');
  
    if (baseFilters.page && baseFilters.page !== 1) newSearchParams.set('page', baseFilters.page.toString());
    if (baseFilters.pageSize && baseFilters.pageSize !== 30) newSearchParams.set('pageSize', baseFilters.pageSize.toString());
    if (baseFilters.minPrice !== undefined) newSearchParams.set('minPrice', baseFilters.minPrice.toString());
    if (baseFilters.maxPrice !== undefined) newSearchParams.set('maxPrice', baseFilters.maxPrice.toString());
    if (baseFilters.dealType) newSearchParams.set('dealType', baseFilters.dealType);
  
    return `${pathname}?${newSearchParams.toString()}`;
  };

  // Handle apply sort - using full SSR navigation
  const handleApplySort = () => {
    // Use router.push() with { scroll: true } to trigger a full page navigation
    // This ensures we get proper SSR behavior
    router.push(createSsrUrl({ ...filters, sort: tempSort, page: 1 }), { scroll: true });
    setIsSortOpen(false);
  };
  
  // Get current sort display text
  const getSortDisplayText = () => {
    const sortOption = filters.sort || 'newest';
    switch (sortOption) {
      case 'newest': return 'Newest';
      case 'hottest': return 'Hottest';
      case 'price-asc': return 'Price: Low to High';
      case 'price-desc': return 'Price: High to Low';
      case 'most-commented': return 'Most Commented';
      case 'getting-warm': return 'Getting Warm';
      case 'trending': return 'Trending';
      default: return 'Newest';
    }
  };
  
  // Handle scroll events for scroll-to-top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollButton(window.scrollY > 500);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // Update URL when filters change - with smarter SSR handling
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }
    
    // For major sorting/filtering changes, we want full SSR navigation
    // For minor changes like pagination, we can use client-side updates
    const isMajorChange = (
      // Whether this is a change to sorting or filtering that should trigger SSR
      filters.sort !== initialFilters.sort || 
       filters.status !== initialFilters.status ||
       filters.dealType !== initialFilters.dealType ||
       filters.category !== initialFilters.category ||
       filters.store !== initialFilters.store
    );
    
    // Update our reference to current filters
    // prevRef.current = { ...filters };
    
    // Build the new URL
    const newUrl = createSsrUrl(filters);
    
    if (isMajorChange) {
      // Use router.push with scroll: true for major changes to ensure SSR
      router.push(newUrl, { scroll: true });
    } else {
      // Use router.replace for minor updates (pagination, UI state)
      // This updates the URL without triggering a full navigation
      router.replace(newUrl);
    }
  }, [filters, pathname, router]);

  // Using server-side rendered categories and stores data instead of client-side hooks
  // This data is now passed as props from the server and initialized in state above
  
  // Fetch deals with React Query
  const queryClient = useQueryClient();
  const { data: dealsResponse, isLoading, isError } = useQuery({
    queryKey: ['deals', filters],
    queryFn: () => getDeals(filters),
    initialData: initialDealsData,
  });
  
  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };
  
  // Handle filter change
  const handleFilterChange = (newFilters: DealFilters) => {
    // For major navigation changes like sort changes, we want to use the setFilters
    // approach which will trigger the useEffect to handle proper SSR navigation
    setFilters(newFilters);
    scrollToTop();
  };
  
  // Handle page change from Pagination component
  const handlePageChange = (newPage: number) => {
    console.log('DealsBrowseClient: Page changed to:', newPage);
    // Update URL to trigger SSR with new page, using current filters from state
    router.push(createSsrUrl({ ...filters, page: newPage }), { scroll: true });
    scrollToTop(); // Scroll to top on page change
  };

  // Generic function to apply filter changes and trigger SSR navigation
  const applySsrFilterChange = (newFilterParams: Partial<DealFilters>) => {
    // Merge with current filters from state, reset page to 1
    router.push(createSsrUrl({ ...filters, ...newFilterParams, page: 1 }), { scroll: true });
  };

  // Example for category change (to be connected to UI elements)
  const handleCategoryChange = (categoryId: number | undefined) => {
    // When category changes, reset store filter as well
    applySsrFilterChange({ category: categoryId, store: undefined });
  };

  // Example for store change
  const handleStoreChange = (storeId: number | undefined) => {
    applySsrFilterChange({ store: storeId, page: 1 });
  };
  
  // Example for price range change
  const handlePriceRangeChange = (min?: number, max?: number | null) => {
    // If both min and max are undefined, it means "All Prices"
    if (min === undefined && max === undefined) {
      applySsrFilterChange({ minPrice: undefined, maxPrice: undefined, page: 1 });
    } else {
      applySsrFilterChange({ minPrice: min, maxPrice: max === null ? undefined : max, page: 1 });
    }
  };

  // Example for deal type change
  const handleDealTypeChange = (dealTypeFilter?: string) => {
    // Update selectedDealTypes state for UI consistency
    const newSelectedDealTypes = [];
    if (dealTypeFilter) {
        const matchingType = dealTypes.find(t => t.filter === dealTypeFilter);
        if (matchingType) newSelectedDealTypes.push(matchingType.name);
    }
    setSelectedDealTypes(newSelectedDealTypes);
    applySsrFilterChange({ dealType: dealTypeFilter, page: 1 });
  };

  // Handle search input change
  const handleSearchSubmit = (searchTerm: string) => { 
    applySsrFilterChange({ search: searchTerm || undefined });
  };

  // Clear all filters button handler
  const handleClearFilters = () => {
    setSelectedPriceRanges([]);
    setSelectedDealTypes([]);
    const defaultFilters: DealFilters = {
      page: 1,
      pageSize: filters.pageSize, // Keep current pageSize
      sort: 'newest', // Reset to default sort
      // Explicitly set other filters to undefined
      search: undefined,
      category: undefined,
      store: undefined,
      minPrice: undefined,
      maxPrice: undefined,
      dealType: undefined,
      status: 'active' // Assuming 'active' is the default status
    };
    setFilters(defaultFilters);
    router.push(createSsrUrl(defaultFilters));
  };

  // Debounced search handler
  const debouncedSearchRef = useRef<
    (...args: [string]) => void
  >();

  useEffect(() => {
    debouncedSearchRef.current = debounce((searchTerm: string) => {
        handleSearchSubmit(searchTerm);
    }, 500);
  }, []); // Empty dependency array means this effect runs once on mount

  const onSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = e.target.value;
    // Update local filter state for immediate input feedback if desired, 
    // but actual search is debounced via handleSearchSubmit
    setFilters(prev => ({...prev, search: searchTerm})); 
    if (debouncedSearchRef.current) {
        debouncedSearchRef.current(searchTerm);
    }
  };

  // Utility for debounce
  function debounce<F extends (...args: any[]) => any>(func: F, waitFor: number) {
    let timeout: NodeJS.Timeout | null = null;

    const debounced = (...args: Parameters<F>) => {
        if (timeout !== null) {
            clearTimeout(timeout);
            timeout = null;
        }
        timeout = setTimeout(() => func(...args), waitFor);
    };

    return debounced as (...args: Parameters<F>) => ReturnType<F>;
  }

  // Helper function to get category name by ID 
  const getCategoryName = (c: number): string => {
    if (!categories) return '';
    const category = categories.find(cat => cat.id === c);
    return category ? category.name : '';
  };

  // Helper function to get store name by ID
  const getStoreName = (s: number): string => {
    if (!stores) return '';
    const store = stores.find(st => st.id === s);
    return store ? store.name : '';
  };

  // Helper function to get page title based on filters
  const getPageTitle = (): string => {
    let title = 'Browse Deals';
    
    if (filters.search) {
      title = `Search Results for "${filters.search}"`;
    } else if (filters.category) {
      const categoryName = getCategoryName(filters.category);
      if (categoryName) {
        title = `${categoryName} Deals`;
      }
    } else if (filters.store) {
      const storeName = getStoreName(filters.store);
      if (storeName) {
        title = `${storeName} Deals`;
      }
    } else if (filters.dealType) {
      if (filters.dealType === 'hot') title = 'Hot Deals';
      else if (filters.dealType === 'warm') title = 'Getting Warm Deals';
      else if (filters.dealType === 'free') title = 'Free Deals';
      else if (filters.dealType === 'discounted') title = 'Discounted Deals';
    }
    
    return title;
  };

  // Toggle functions for collapsible sections
  const toggleCategoriesSection = () => setIsCategoriesOpen(!isCategoriesOpen);
  const togglePriceRangeSection = () => setIsPriceRangeOpen(!isPriceRangeOpen);
  const toggleStoresSection = () => setIsStoresOpen(!isStoresOpen);
  const toggleDealTypeSection = () => setIsDealTypeOpen(!isDealTypeOpen);
  const toggleFilters = () => setShowFilters(!showFilters);

  // Toggle view mode
  const toggleViewMode = (mode: 'grid' | 'list') => {
    setViewMode(mode);
    // Optionally, save to localStorage if persistence is desired
    // localStorage.setItem('dealsViewMode', mode);
  };

  return (
    <div className="">
      <div className="mx-auto">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{getPageTitle()}</h1>
            <p className="mt-1 text-sm text-gray-600">Find the best deals from your favorite stores</p>
          </div>
          
          <div className="flex space-x-4">
            {/* Toggle Filters Button */}
            <button
              type="button"
              onClick={toggleFilters}
              className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <Filter className="h-4 w-4 mr-2" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
            </button>
            
            {/* Add Post Deal button */}
            <Link
              href="/deals/create"
              className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700"
            >
              <PlusIcon className="mr-2 h-5 w-5" aria-hidden="true" />
              Post Deal
            </Link>
          </div>
        </div>
        
        {/* Filter Section - Now at the top */}
        {showFilters && (
          <div className="bg-white shadow rounded-lg p-4 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Filters</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Categories Section */}
                <div>
                  <div 
                    className="flex items-center justify-between cursor-pointer mb-2"
                    onClick={toggleCategoriesSection}
                  >
                    <h3 className="text-base font-medium text-gray-900">Categories</h3>
                    <ChevronDown 
                      className={`h-5 w-5 text-gray-500 transition-transform ${isCategoriesOpen ? 'rotate-180' : ''}`}
                    />
                  </div>
                  
                  {isCategoriesOpen && (
                    <div className="space-y-2 mt-2 max-h-[320px] overflow-y-auto">
                      {categories?.map(category => (
                        <div key={category.id} className="flex items-center">
                          <input
                            id={`category-${category.id}`}
                            name={`category-${category.id}`}
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            checked={filters.category === category.id}
                            onChange={() => handleCategoryChange(filters.category === category.id ? undefined : category.id)}
                          />
                          <label
                            htmlFor={`category-${category.id}`}
                            className="ml-2 text-sm text-gray-700"
                          >
                            {category.name} ({category.activeDealsCount || 0})
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Price Range Section */}
                <div>
                  <div 
                    className="flex items-center justify-between cursor-pointer mb-2"
                    onClick={togglePriceRangeSection}
                  >
                    <h3 className="text-base font-medium text-gray-900">Price Range</h3>
                    <ChevronDown 
                      className={`h-5 w-5 text-gray-500 transition-transform ${isPriceRangeOpen ? 'rotate-180' : ''}`}
                    />
                  </div>
                  
                  {isPriceRangeOpen && (
                    <div className="space-y-2 mt-2">
                      {priceRanges.map(range => (
                        <div key={range.name} className="flex items-center">
                          <input
                            id={`price-${range.name}`}
                            name="price-range"
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            checked={selectedPriceRanges.includes(range.name)}
                            onChange={() => {
                              const isSelected = selectedPriceRanges.includes(range.name);
                              if (isSelected) {
                                // Deselecting
                                handlePriceRangeChange(undefined, undefined);
                              } else {
                                // Selecting
                                handlePriceRangeChange(range.min, range.max);
                              }
                            }}
                          />
                          <label
                            htmlFor={`price-${range.name}`}
                            className="ml-2 text-sm text-gray-700"
                          >
                            {range.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Stores Section */}
                <div>
                  <div 
                    className="flex items-center justify-between cursor-pointer mb-2"
                    onClick={toggleStoresSection}
                  >
                    <h3 className="text-base font-medium text-gray-900">Stores</h3>
                    <ChevronDown 
                      className={`h-5 w-5 text-gray-500 transition-transform ${isStoresOpen ? 'rotate-180' : ''}`}
                    />
                  </div>
                  
                  {isStoresOpen && (
                    <div className="space-y-2 mt-2 max-h-[320px] overflow-y-auto">
                      {stores?.map(store => (
                        <div key={store.id} className="flex items-center">
                          <input
                            id={`store-${store.id}`}
                            name={`store-${store.id}`}
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            checked={filters.store === store.id}
                            onChange={() => handleStoreChange(filters.store === store.id ? undefined : store.id)}
                          />
                          <label
                            htmlFor={`store-${store.id}`}
                            className="ml-2 text-sm text-gray-700"
                          >
                            {store.name} ({store.dealsCount || 0})
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Deal Type Section */}
                <div>
                  <div 
                    className="flex items-center justify-between cursor-pointer mb-2"
                    onClick={toggleDealTypeSection}
                  >
                    <h3 className="text-base font-medium text-gray-900">Deal Type</h3>
                    <ChevronDown 
                      className={`h-5 w-5 text-gray-500 transition-transform ${isDealTypeOpen ? 'rotate-180' : ''}`}
                    />
                  </div>
                  
                  {isDealTypeOpen && (
                    <div className="space-y-2 mt-2">
                      {dealTypes.map(type => (
                        <div key={type.name} className="flex items-center">
                          <input
                            id={`deal-type-${type.filter}`}
                            name="deal-type"
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            checked={selectedDealTypes.includes(type.name)}
                            onChange={() => {
                              const isSelected = selectedDealTypes.includes(type.name);
                              if (isSelected) {
                                // Deselecting
                                handleDealTypeChange(undefined);
                              } else {
                                // Selecting
                                handleDealTypeChange(type.filter);
                              }
                            }}
                          />
                          <label
                            htmlFor={`deal-type-${type.filter}`}
                            className="ml-2 text-sm text-gray-700"
                          >
                            {type.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            
            <div className="mt-4">
              <button
                type="button"
                onClick={handleClearFilters}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Clear All Filters
              </button>
            </div>
          </div>
        )}

        <div className="mt-8">
          
          {/* Main content area */}
          <div>
            {/* Search bar, Sort, and Filter buttons */}
            <div className="mb-6">
              <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-2">
                <div className="relative flex-grow">
                  <input
                    type="text"
                    placeholder="Search for amazing deals..."
                    value={filters.search || ''}
                    onChange={onSearchInputChange}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearchSubmit(filters.search || '')}
                    className="w-full p-2 pl-10 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                  </div>
                </div>
                
                {/* Search, Sort and Filter buttons */}
                <div className="flex space-x-2 w-full sm:w-auto">
                  <button
                    type="button"
                    onClick={() => handleFilterChange({ ...filters, page: 1 })}
                    className="flex-1 sm:flex-none px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Search
                  </button>
                  
                  {/* Sort dropdown */}
                  <div className="relative inline-block text-left">
                    <div>
                      <button
                        type="button"
                        onClick={() => setIsSortOpen(!isSortOpen)}
                        className="flex-1 sm:flex-none inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        <ArrowUpDown className="h-4 w-4 mr-1" />
                        {getSortDisplayText()}
                      </button>
                    </div>
                    
                    {isSortOpen && (
                      <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 focus:outline-none z-10">
                        <div className="py-1">
                          <button
                            onClick={() => handleSortChange('newest')}
                            className={`${
                              tempSort === 'newest' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                            } block px-4 py-2 text-sm w-full text-left hover:bg-gray-50`}
                          >
                            Newest
                          </button>
                          <button
                            onClick={() => handleSortChange('hottest')}
                            className={`${
                              tempSort === 'hottest' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                            } block px-4 py-2 text-sm w-full text-left hover:bg-gray-50`}
                          >
                            Hottest
                          </button>
                          <button
                            onClick={() => handleSortChange('getting-warm')}
                            className={`${
                              tempSort === 'getting-warm' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                            } block px-4 py-2 text-sm w-full text-left hover:bg-gray-50`}
                          >
                            Getting Warm
                          </button>
                          <button
                            onClick={() => handleSortChange('price-asc')}
                            className={`${
                              tempSort === 'price-asc' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                            } block px-4 py-2 text-sm w-full text-left hover:bg-gray-50`}
                          >
                            Price: Low to High
                          </button>
                          <button
                            onClick={() => handleSortChange('price-desc')}
                            className={`${
                              tempSort === 'price-desc' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                            } block px-4 py-2 text-sm w-full text-left hover:bg-gray-50`}
                          >
                            Price: High to Low
                          </button>
                          <button
                            onClick={() => handleSortChange('most-commented')}
                            className={`${
                              tempSort === 'most-commented' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                            } block px-4 py-2 text-sm w-full text-left hover:bg-gray-50`}
                          >
                            Most Commented
                          </button>
                          <button
                            onClick={() => handleSortChange('trending')}
                            className={`${
                              tempSort === 'trending' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                            } block px-4 py-2 text-sm w-full text-left hover:bg-gray-50`}
                          >
                            Trending
                          </button>
                        </div>
                        <div className="py-1">
                          <button
                            onClick={handleApplySort}
                            className="bg-primary-600 text-white block w-full text-left px-4 py-2 text-sm hover:bg-primary-700"
                          >
                            Apply Sort
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {isLoading ? (
              // Show loading state
              <div className="w-full flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
              </div>
            ) : deals?.length === 0 ? (
              // Show empty state
              <div className="w-full bg-white rounded-lg shadow p-8 text-center">
                <h3 className="text-xl font-medium text-gray-900 mb-2">No deals found</h3>
                <p className="text-gray-600 mb-4">
                  We couldn't find any deals that match your search criteria.
                </p>
                {isAuthenticated && (
                  <div className="mt-6">
                    <Link
                      href="/deals/create"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700"
                    >
                      <PlusIcon className="mr-2 h-5 w-5" aria-hidden="true" />
                      Submit Deal
                    </Link>
                  </div>
                )}
              </div>
            ) : (
              // Deals list
              <div> 
                {/* Results found indicator and view toggle */}
                <div className="mb-4 pb-2 border-b border-gray-200 flex justify-between items-center">
                  <p className="text-sm text-gray-600">
                    Showing{' '}
                    <span className="font-medium">
                      {pagination?.from || 0}
                    </span>{' '}
                    to{' '}
                    <span className="font-medium">
                      {pagination?.to || 0}
                    </span> of <span className="font-semibold text-primary-700">
                      {pagination?.totalCount || 0}
                    </span> Deals
                  
                    {filters.search ? ` matching "${filters.search}"` : ''}
                  </p>

                  {/* View toggle buttons */}
                  <div className="flex space-x-2">
                    <button 
                      onClick={() => toggleViewMode('grid')}
                      className={`p-2 rounded-md ${viewMode === 'grid' 
                        ? 'bg-primary-100 text-primary-700' 
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
                      title="Grid view"
                    >
                      <LayoutGrid size={16} />
                    </button>
                    <button 
                      onClick={() => toggleViewMode('list')}
                      className={`p-2 rounded-md ${viewMode === 'list' 
                        ? 'bg-primary-100 text-primary-700' 
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
                      title="List view"
                    >
                      <List size={16} />
                    </button>
                  </div>
                </div>
                
                {viewMode === 'grid' ? (
                  // Grid view
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {deals?.map((deal: Deal) => (
                      <GridDealCard key={deal.id} deal={deal} />
                    ))}
                  </div>
                ) : (
                  // List view
                  <div className="space-y-4">
                    {deals?.map((deal: Deal) => (
                      <ListDealCard key={deal.id} deal={deal} />
                    ))}
                  </div>
                )}
            
                {/* Pagination with improved styling */}
                <div className="mt-8">
                  <PaginationComponent
                    currentPage={pagination?.currentPage || 1}
                    totalPages={pagination?.totalPages || 1}
                    onPageChange={handlePageChange}
                    totalItems={pagination?.totalCount}
                    pageSize={filters.pageSize}
                    itemName="Deals"
                  />
                </div>
              </div> 
            )}
          </div>
        </div>
        
        {/* Scroll to top button */}
        {showScrollButton && (
          <button
            onClick={scrollToTop}
            className="fixed bottom-8 right-8 p-3 rounded-full bg-primary-500 text-white shadow-lg hover:bg-primary-600 transition-all duration-300 z-50 animate-bounce-subtle"
            title="Scroll to top"
          >
            <ArrowUpCircle size={24} />
          </button>
        )}
      </div>
    </div>
  );
}
