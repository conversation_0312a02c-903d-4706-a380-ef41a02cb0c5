import React from 'react';
import { DealFilters, DealListResponse, Deal, Category, Store, PaginationInfo } from '@/types'; 
import { getDeals, getCategoryById, getStoreById } from '@/services/dealService.server';
import { getCategories } from '@/services/categoryService';
import { getStores } from '@/services/storeService';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import DealsBrowseClient from './DealsBrowseClient'; 
import type { DealsBrowseClientProps } from './DealsBrowseClient'; 

interface DealsBrowsePageProps {
  searchParams: {
    page?: string;
    limit?: string;
    category?: string;
    store?: string;
    minPrice?: string;
    maxPrice?: string;
    search?: string;
    status?: string;
    sort?: string;
    promotionType?: string;
  };
}

export async function generateMetadata({ searchParams }: DealsBrowsePageProps): Promise<Metadata> {
  const page = searchParams.page ? parseInt(searchParams.page, 10) : 1;
  const limit = searchParams.limit ? parseInt(searchParams.limit, 10) : 30; // Default to 30 as per API

  const filters: DealFilters = {
    page,
    limit,
    category: searchParams.category ? parseInt(searchParams.category, 10) : undefined,
    store: searchParams.store ? parseInt(searchParams.store, 10) : undefined,
    minPrice: searchParams.minPrice ? parseFloat(searchParams.minPrice) : undefined,
    maxPrice: searchParams.maxPrice ? parseFloat(searchParams.maxPrice) : undefined,
    search: searchParams.search,
    status: searchParams.status as DealFilters['status'],
    sort: searchParams.sort as DealFilters['sort'],
    dealType: searchParams.promotionType as DealFilters['dealType'],
  };

  let category: Category | null = null;
  let store: Store | null = null;

  if (filters.category) {
    try {
      console.log(`[generateMetadata] Fetching category: ${filters.category}`);
      category = await getCategoryById(filters.category);
      console.log(`[generateMetadata] Fetched category:`, category?.name);
    } catch (error) {
      console.error('[generateMetadata] Error fetching category:', error);
    }
  }

  if (filters.store) {
    try {
      console.log(`[generateMetadata] Fetching store: ${filters.store}`);
      store = await getStoreById(filters.store);
      console.log(`[generateMetadata] Fetched store:`, store?.name);
    } catch (error) {
      console.error('[generateMetadata] Error fetching store:', error);
    }
  }

  let title = 'Browse All Deals - NiceDeals';
  let description = 'Find the latest deals, discounts, and offers on NiceDeals. Filter by category, store, price, and more to discover amazing savings.';
  const siteName = 'NiceDeals';
  const openGraphImages: any['images'] = [{ url: '/opengraph-image.jpg' }]; // Default OG image

  const categoryName = category?.slug || category?.name;
  const storeName = store?.slug || store?.name;

  if (categoryName && storeName) {
    title = `Deals in ${categoryName} from ${storeName} - NiceDeals`;
    description = `Find the best deals in ${categoryName} from ${storeName}. Updated daily with new offers, discounts, and voucher codes on NiceDeals.`;
  } else if (categoryName) {
    title = `Deals in ${categoryName} - NiceDeals`;
    description = `Browse all deals and offers in the ${categoryName} category. Save money with the latest discounts available on NiceDeals.`;
  } else if (storeName) {
    title = `Deals from ${storeName} - NiceDeals`;
    description = `Discover all deals and special offers from ${storeName}. Updated regularly with the best savings on NiceDeals.`;
  }

  // Add search term to title and description if present
  if (filters.search) {
    const searchTitlePart = `Results for "${filters.search}"`;
    if (categoryName || storeName) {
      title = `${searchTitlePart} in ${title.replace(' - NiceDeals', '')} - NiceDeals`;
    } else {
      title = `${searchTitlePart} - NiceDeals`;
    }
    description = `Search results for "${filters.search}". ${description}`;
  }

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
      url: `/dealsBrowse?${new URLSearchParams(searchParams).toString()}`,
      siteName,
      images: openGraphImages,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: openGraphImages,
    },
  };
}

export default async function DealsBrowsePage({ searchParams }: DealsBrowsePageProps) {
  const page = searchParams.page ? parseInt(searchParams.page, 10) : 1;
  const limit = searchParams.limit ? parseInt(searchParams.limit, 10) : 24; 

  const filters: DealFilters = {
    page,
    limit,
    category: searchParams.category ? parseInt(searchParams.category, 10) : undefined,
    store: searchParams.store ? parseInt(searchParams.store, 10) : undefined,
    minPrice: searchParams.minPrice ? parseFloat(searchParams.minPrice) : undefined,
    maxPrice: searchParams.maxPrice ? parseFloat(searchParams.maxPrice) : undefined,
    search: searchParams.search,
    status: searchParams.status as DealFilters['status'], 
    sort: searchParams.sort as DealFilters['sort'], 
    dealType: searchParams.promotionType as DealFilters['dealType'],
  };

  let initialDealsResponse: DealListResponse;
  let categoriesData: Category[] = [];
  let storesData: Store[] = [];
  let errorFetching: string | null = null;

  try {
    initialDealsResponse = await getDeals(filters);
  } catch (error) {
    console.error('[DealsBrowsePage - Server] Error fetching initial deals:', error);
    initialDealsResponse = {
      deals: [],
      pagination: {
        currentPage: page,
        totalPages: 0,
        totalCount: 0,
        pageSize: limit,
        from: 0,
        to: 0,
      },
    };
    errorFetching = 'Failed to load deals. Please try again later.';
  }

  try {
    categoriesData = await getCategories(); 
  } catch (error) {
    console.error('[DealsBrowsePage - Server] Error fetching categories:', error);
    categoriesData = []; // Ensure it's an array on error
  }

  try {
    storesData = await getStores(filters.category); 
  } catch (error) {
    console.error('[DealsBrowsePage - Server] Error fetching stores:', error);
    storesData = []; // Ensure it's an array on error
  }

  const clientProps: DealsBrowseClientProps = {
    initialDealsData: initialDealsResponse,
    initialCategories: categoriesData,
    initialStores: storesData,
    initialFilters: filters, 
    searchParams: searchParams, 
    errorFetching: errorFetching,
  };

  return (
    <DealsBrowseClient {...clientProps} />
  );
}
