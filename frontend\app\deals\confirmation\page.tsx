'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useSearchParams, useRouter } from 'next/navigation';
import { CheckCircleIcon } from '@heroicons/react/24/outline';
import { getDealById } from '@/services/dealService';
import { Deal } from '@/types';
import { useAuth } from '@/hooks/useAuth';
import api from '@/services/api';

export default function DealConfirmationPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { isAuthenticated, user, token } = useAuth();
  const [deal, setDeal] = useState<Deal | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const dealId = searchParams.get('dealId');
  
  useEffect(() => {
    // Redirect if not authenticated
    if (!isAuthenticated) {
      router.replace('/login');
      return;
    }
    
    // Redirect if no deal ID provided
    if (!dealId) {
      router.replace('/deals/create');
      return;
    }
    
    // Explicitly set auth token before fetch - critical for pending deal authorization
    if (token) {
      api.setAuthToken(token);
    }
    
    // Fetch deal details
    const fetchDeal = async () => {
      try {
        setLoading(true);
        
        // Pass userId as query param for pending deal authorization as backup
        const fetchedDeal = await getDealById(parseInt(dealId), user?.id);
        setDeal(fetchedDeal);
      } catch (err: any) {
        console.error('Error fetching deal:', err);
        
        // If the error is about a pending deal that's not owned by the user,
        // create a custom error message
        if (err.response?.status === 404 && err.response?.data?.isPendingDeal) {
          setError("You don't have permission to view this pending deal.");
        } else {
          setError('Failed to load deal details. Please try again.');
        }
      } finally {
        setLoading(false);
      }
    };
    
    fetchDeal();
  }, [dealId, isAuthenticated, router, user?.id, token]);
  
  if (loading) {
    return (
      <div className="container mx-auto max-w-3xl px-4 py-16">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-deal-orange"></div>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="container mx-auto max-w-3xl px-4 py-16">
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto max-w-3xl px-4 py-16">
      <div className="rounded-md bg-green-50 p-4 mb-8">
        <div className="flex">
          <div className="flex-shrink-0">
            <CheckCircleIcon className="h-5 w-5 text-green-400" aria-hidden="true" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-green-800">Deal Submitted Successfully</h3>
            <div className="mt-2 text-sm text-green-700">
              <p>
                Your deal has been submitted and is now pending review by moderators. You'll be notified when it's approved.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Deal Details</h3>
          
          {deal && (
            <div className="mt-5 border-t border-gray-200">
              <dl className="divide-y divide-gray-200">
                <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt className="text-sm font-medium text-gray-500">Title</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{deal.title}</dd>
                </div>
                
                <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt className="text-sm font-medium text-gray-500">Price</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                    £{deal.price} (was £{deal.originalPrice})
                  </dd>
                </div>
                
                <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt className="text-sm font-medium text-gray-500">Status</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                    <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                      Pending Review
                    </span>
                  </dd>
                </div>
              </dl>
            </div>
          )}
          
          <div className="mt-8 flex space-x-4">
            <Link
              href="/user/deals"
              className="inline-flex items-center rounded-md border border-transparent bg-deal-orange px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-deal-orange-dark focus:outline-none focus:ring-2 focus:ring-deal-orange focus:ring-offset-2"
            >
              View My Deals
            </Link>
            
            <Link
              href="/deals/create"
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-deal-orange focus:ring-offset-2"
            >
              Post Another Deal
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
