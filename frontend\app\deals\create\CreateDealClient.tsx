'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { PaperClipIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { DealFormValues, Category, Store } from '@/types';
import { createDeal, uploadDealImage } from '@/services/dealService';
import { useQuery } from '@tanstack/react-query';
import { getCategories } from '@/services/categoryService';
import { getStores } from '@/services/storeService';

// Validation schema
const validationSchema = Yup.object({
  title: Yup.string().required('Title is required').max(100, 'Title must be at most 100 characters'),
  description: Yup.string().required('Description is required'),
  dealUrl: Yup.string().url('Must be a valid URL').required('Deal URL is required'),
  price: Yup.number().required('Price is required').min(0, 'Price must be positive'),
  originalPrice: Yup.number().required('Original price is required').min(0, 'Original price must be positive'),
  categoryId: Yup.number().required('Category is required').min(1, 'Please select a category'),
  storeId: Yup.number().required('Store is required').min(1, 'Please select a store'),
  coupon: Yup.string().max(50, 'Coupon code must be at most 50 characters'),
  expiresAt: Yup.mixed().when('$expirationDateEnabled', {
    is: true,
    then: () => Yup.date().nullable().min(new Date(), 'Expiration date must be in the future'),
    otherwise: () => Yup.mixed().nullable()
  }),
  // This will be validated at handleSubmit time since it's not a direct form field
  imageUrl: Yup.string()
});

export default function CreateDealClient() {
  const router = useRouter();
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [expirationDateEnabled, setExpirationDateEnabled] = useState(false);
  
  // Fetch categories
  const { data: categories = [] } = useQuery<Category[]>({
    queryKey: ['categories'],
    queryFn: () => getCategories(),
  });
  
  // Fetch stores
  const { data: stores = [] } = useQuery<Store[]>({
    queryKey: ['stores'],
    queryFn: () => getStores(),
  });
  
  // Initial form values
  const initialValues: DealFormValues = {
    title: '',
    description: '',
    dealUrl: '',
    price: 0, // Use 0 as a placeholder that will be validated by our schema
    originalPrice: 0, // Use 0 as a placeholder that will be validated by our schema
    categoryId: 0, // Use 0 as a placeholder that will be validated by our schema
    storeId: 0, // Use 0 as a placeholder that will be validated by our schema
    coupon: '',
    expiresAt: undefined,
  };
  
  // Handle image selection
  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (file.size > 2 * 1024 * 1024) {
        alert('Image size must be less than 2MB');
        return;
      }
      
      if (!file.type.startsWith('image/')) {
        alert('Selected file must be an image');
        return;
      }
      
      setSelectedImage(file);
      setImagePreview(URL.createObjectURL(file));
    }
  };
  
  // Clear selected image
  const clearImage = () => {
    setSelectedImage(null);
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview);
    }
    setImagePreview(null);
  };
  
  // Handle form submission
  const handleSubmit = async (values: DealFormValues) => {
    try {
      setSubmitError(null);
      
      // Validate image is selected
      if (!selectedImage) {
        setSubmitError('Please upload an image for the deal');
        return;
      }
      
      // Upload image first
      try {
        const { imageUrl, thumbnailUrl } = await uploadDealImage(selectedImage);
        values.imageUrl = imageUrl;
        values.thumbnailUrl = thumbnailUrl;
      } catch (error) {
        console.error('Error uploading image:', error);
        setSubmitError('Failed to upload image. Please try again.');
        return;
      }
      
      // Create the deal
      const deal = await createDeal(values);
      
      // Redirect to the deal confirmation page
      router.push('/deals/confirmation?dealId=' + deal.id);
    } catch (error: any) {
      console.error('Error creating deal:', error);
      setSubmitError(error.message || 'Failed to create deal. Please try again.');
    }
  };
  
  return (
    <div className="container mx-auto max-w-3xl px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Post a New Deal</h1>
        <p className="mt-2 text-sm text-gray-600">
          Share a great deal with the community. Your submission will be reviewed by moderators.
        </p>
      </div>
      
      {submitError && (
        <div className="mb-6 rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{submitError}</p>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          validateOnChange={true}
          validateOnBlur={true}
          validateOnMount={false}
          enableReinitialize={true}
          validateSchema={{
            expirationDateEnabled,
          }}
        >
          {({ isSubmitting, errors, touched }) => (
            <Form className="space-y-6 p-8">
              {/* Title Field */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                  Deal Title *
                </label>
                <div className="mt-1">
                  <Field
                    id="title"
                    name="title"
                    type="text"
                    placeholder="e.g., '50% off Nike Shoes at Amazon'"
                    className={`block w-full rounded-md ${
                      errors.title && touched.title ? 'border-red-300' : 'border-gray-300'
                    } px-3 py-2 shadow-sm focus:border-deal-orange focus:outline-none focus:ring-deal-orange sm:text-sm`}
                  />
                  <ErrorMessage name="title" component="p" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              {/* URL Field */}
              <div>
                <label htmlFor="dealUrl" className="block text-sm font-medium text-gray-700">
                  Deal URL *
                </label>
                <div className="mt-1">
                  <Field
                    id="dealUrl"
                    name="dealUrl"
                    type="url"
                    placeholder="https://example.com/deal"
                    className={`block w-full rounded-md ${
                      errors.dealUrl && touched.dealUrl ? 'border-red-300' : 'border-gray-300'
                    } px-3 py-2 shadow-sm focus:border-deal-orange focus:outline-none focus:ring-deal-orange sm:text-sm`}
                  />
                  <ErrorMessage name="dealUrl" component="p" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              {/* Category Field */}
              <div>
                <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700">
                  Category *
                </label>
                <div className="mt-1">
                  <Field
                    as="select"
                    id="categoryId"
                    name="categoryId"
                    className={`block w-full rounded-md ${
                      errors.categoryId && touched.categoryId ? 'border-red-300' : 'border-gray-300'
                    } px-3 py-2 shadow-sm focus:border-deal-orange focus:outline-none focus:ring-deal-orange sm:text-sm`}
                  >
                    <option value="0">Select a category</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </Field>
                  <ErrorMessage name="categoryId" component="p" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              {/* Store Field */}
              <div>
                <label htmlFor="storeId" className="block text-sm font-medium text-gray-700">
                  Store *
                </label>
                <div className="mt-1">
                  <Field
                    as="select"
                    id="storeId"
                    name="storeId"
                    className={`block w-full rounded-md ${
                      errors.storeId && touched.storeId ? 'border-red-300' : 'border-gray-300'
                    } px-3 py-2 shadow-sm focus:border-deal-orange focus:outline-none focus:ring-deal-orange sm:text-sm`}
                  >
                    <option value="0">Select a store</option>
                    {stores.map((store) => (
                      <option key={store.id} value={store.id}>
                        {store.name}
                      </option>
                    ))}
                  </Field>
                  <ErrorMessage name="storeId" component="p" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              {/* Price Fields */}
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                    Deal Price (£) *
                  </label>
                  <div className="mt-1">
                    <Field
                      id="price"
                      name="price"
                      type="number"
                      min="0"
                      step="0.01"
                      className={`block w-full rounded-md ${
                        errors.price && touched.price ? 'border-red-300' : 'border-gray-300'
                      } px-3 py-2 shadow-sm focus:border-deal-orange focus:outline-none focus:ring-deal-orange sm:text-sm`}
                    />
                    <ErrorMessage name="price" component="p" className="mt-1 text-sm text-red-600" />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="originalPrice" className="block text-sm font-medium text-gray-700">
                    Original Price (£) *
                  </label>
                  <div className="mt-1">
                    <Field
                      id="originalPrice"
                      name="originalPrice"
                      type="number"
                      min="0"
                      step="0.01"
                      className={`block w-full rounded-md ${
                        errors.originalPrice && touched.originalPrice ? 'border-red-300' : 'border-gray-300'
                      } px-3 py-2 shadow-sm focus:border-deal-orange focus:outline-none focus:ring-deal-orange sm:text-sm`}
                    />
                    <ErrorMessage name="originalPrice" component="p" className="mt-1 text-sm text-red-600" />
                  </div>
                </div>
              </div>
              
              {/* Description Field */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description *
                </label>
                <div className="mt-1">
                  <Field
                    as="textarea"
                    id="description"
                    name="description"
                    rows={5}
                    placeholder="Provide details about the deal..."
                    className={`block w-full rounded-md ${
                      errors.description && touched.description ? 'border-red-300' : 'border-gray-300'
                    } px-3 py-2 shadow-sm focus:border-deal-orange focus:outline-none focus:ring-deal-orange sm:text-sm`}
                  />
                  <ErrorMessage name="description" component="p" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              {/* Coupon Code Field */}
              <div>
                <label htmlFor="coupon" className="block text-sm font-medium text-gray-700">
                  Coupon Code (Optional)
                </label>
                <div className="mt-1">
                  <Field
                    id="coupon"
                    name="coupon"
                    type="text"
                    placeholder="e.g., 'SUMMER20'"
                    className={`block w-full rounded-md ${
                      errors.coupon && touched.coupon ? 'border-red-300' : 'border-gray-300'
                    } px-3 py-2 shadow-sm focus:border-deal-orange focus:outline-none focus:ring-deal-orange sm:text-sm`}
                  />
                  <ErrorMessage name="coupon" component="p" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              {/* Expiration Date Field */}
              <div>
                <div className="flex items-center space-x-2">
                  <input
                    id="expirationDateEnabled"
                    type="checkbox"
                    checked={expirationDateEnabled}
                    onChange={(e) => setExpirationDateEnabled(e.target.checked)}
                    className="h-4 w-4 rounded border-gray-300 text-deal-orange focus:ring-deal-orange"
                  />
                  <label htmlFor="expirationDateEnabled" className="text-sm font-medium text-gray-700">
                    Set Expiration Date
                  </label>
                </div>
                <div className="mt-2">
                  <Field
                    id="expiresAt"
                    name="expiresAt"
                    type="datetime-local"
                    disabled={!expirationDateEnabled}
                    className={`block w-full rounded-md ${
                      errors.expiresAt && touched.expiresAt ? 'border-red-300' : 'border-gray-300'
                    } px-3 py-2 shadow-sm focus:border-deal-orange focus:outline-none focus:ring-deal-orange sm:text-sm ${
                      !expirationDateEnabled ? 'bg-gray-100 cursor-not-allowed' : ''
                    }`}
                  />
                  <ErrorMessage name="expiresAt" component="p" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              {/* Image Upload Field */}
              <div>
                <label className="block text-sm font-medium text-gray-700">Deal Image *</label>
                <div className="mt-1 flex flex-col items-center space-y-4">
                  {imagePreview ? (
                    <div className="relative">
                      <img
                        src={imagePreview}
                        alt="Preview"
                        className="h-48 w-auto rounded-md object-cover"
                      />
                      <button
                        type="button"
                        onClick={clearImage}
                        className="absolute -right-2 -top-2 rounded-full bg-red-100 p-1 text-red-600 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                      >
                        <XMarkIcon className="h-4 w-4" aria-hidden="true" />
                      </button>
                    </div>
                  ) : (
                    <div className="flex max-w-lg justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-5 pb-6">
                      <div className="space-y-1 text-center">
                        <PaperClipIcon className="mx-auto h-12 w-12 text-gray-400" aria-hidden="true" />
                        <div className="flex text-sm text-gray-600">
                          <label
                            htmlFor="image-upload"
                            className="relative cursor-pointer rounded-md bg-white font-medium text-deal-orange focus-within:outline-none focus-within:ring-2 focus-within:ring-deal-orange focus-within:ring-offset-2 hover:text-deal-orange-dark"
                          >
                            <span>Upload an image</span>
                            <input
                              id="image-upload"
                              name="image-upload"
                              type="file"
                              accept="image/*"
                              className="sr-only"
                              onChange={handleImageChange}
                            />
                          </label>
                          <p className="pl-1">or drag and drop</p>
                        </div>
                        <p className="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="mr-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-deal-orange focus:ring-offset-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="inline-flex items-center rounded-md border border-transparent bg-deal-orange px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-deal-orange-dark focus:outline-none focus:ring-2 focus:ring-deal-orange focus:ring-offset-2"
                >
                  {isSubmitting ? 'Posting...' : 'Post Deal'}
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
}
