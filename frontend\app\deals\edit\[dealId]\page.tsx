import React from 'react';
import Link from 'next/link';
import { ArrowLeftIcon, WrenchScrewdriverIcon } from '@heroicons/react/24/outline';

type Props = {
  params: { dealId: string };
};

export default function EditDealComingSoonPage({ params }: Props) {
  const { dealId } = params;

  return (
    <div className="min-h-[calc(100vh-200px)] flex flex-col items-center justify-center p-6">
      <div className="bg-white dark:bg-slate-800 shadow-xl rounded-lg p-8 md:p-12 max-w-2xl w-full text-center">
        <WrenchScrewdriverIcon className="w-20 h-20 text-orange-500 mx-auto mb-6" />
        <h1 className="text-4xl font-bold text-slate-700 dark:text-slate-200 mb-4">
          Editing Feature Coming Soon!
        </h1>
        <p className="text-lg text-slate-600 dark:text-slate-400 mb-2">
          You're trying to edit Deal ID: <span className="font-semibold text-orange-500">{dealId}</span>.
        </p>
        <p className="text-lg text-slate-600 dark:text-slate-400 mb-8">
          We're currently working hard to bring you a seamless deal editing experience. 
          Please check back later.
        </p>
        <div className="flex justify-center">
          <Link 
            href={`/dealDetail/${dealId}`}
            className="inline-flex items-center rounded-md border border-transparent bg-orange-500 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800 transition-colors duration-150"
          >
            <ArrowLeftIcon className="mr-2 h-5 w-5" />
            Back to Deal Details
          </Link>
        </div>
      </div>
    </div>
  );
}

// Optional: Add metadata if needed for SEO, though less critical for a placeholder
// export async function generateMetadata({ params }: Props) {
//   return {
//     title: `Edit Deal ${params.dealId} - Coming Soon`,
//   };
// }
