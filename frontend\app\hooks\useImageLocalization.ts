import { useState } from 'react';
import { toast } from 'react-hot-toast';
import { isExternalUrl } from '../utils/imageUtils';

interface LocalizeImageResponse {
  data: {
    imageUrl: string;
    thumbnailUrl: string;
  };
}

export function useImageLocalization() {
  const [isProcessingImage, setIsProcessingImage] = useState(false);

  const localizeImage = async (imageUrl: string, dealId?: number): Promise<string | null> => {
    if (!imageUrl || !isExternalUrl(imageUrl)) return null;

    setIsProcessingImage(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/admin/deals/localize-image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ imageUrl, dealId })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to localize image');
      }

      const data: LocalizeImageResponse = await response.json();
      toast.success('Image localized successfully');
      return data.data.imageUrl;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to localize image';
      toast.error(message);
      console.error('Error:', error);
      return null;
    } finally {
      setIsProcessingImage(false);
    }
  };

  return { localizeImage, isProcessingImage };
}