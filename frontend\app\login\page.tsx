'use client';

import React from 'react';
import Link from 'next/link'; // Import next/link
import { useRouter } from 'next/navigation'; // Import next/navigation
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { useAuth } from '@/hooks/useAuth'; // Use alias
import { LoginFormValues } from '@/types'; // Use alias

// Validation schema (remains the same)
const loginSchema = Yup.object().shape({
  credential: Yup.string()
    .required('Username or Email is required'),
  password: Yup.string()
    .required('Password is required'),
});

const LoginPage: React.FC = () => {
  const { login } = useAuth();
  const router = useRouter(); // Use Next.js router

  // Get the redirect path - simplified for now
  // TODO: Implement proper redirect based on previous page if needed (e.g., using search params)
  const redirectTo = '/';

  // Login mutation (updated for @tanstack/react-query)
  const loginMutation = useMutation({
    mutationFn: async (values: LoginFormValues) => {
      await login(values.credential, values.password);
    },
    onSuccess: () => {
      toast.success('Logged in successfully');
      router.replace(redirectTo); // Use router.replace
    },
    onError: (error: any) => {
      // Check if error has a response and custom message
      const message = error?.response?.data?.message || error.message || 'Login failed. Please check your credentials.';
      toast.error(message);
    },
  });

  // Initial form values (remains the same)
  const initialValues: LoginFormValues = {
    credential: '',
    password: '',
    // remember: false, // Add if needed by useAuth/login logic
  };

  return (
    <div className="mx-auto max-w-md py-12"> {/* Added py-12 for spacing */}
      <div className="rounded-lg bg-white p-8 shadow-lg"> {/* Increased shadow */}
        <div className="mb-6 text-center">
          {/* Use next/link */}
          <Link href="/" className="inline-flex items-center justify-center">
            {/* Ensure logo is in /public folder */}
            <img src="/nicedeals-logo.png" alt="NiceDeals" className="h-16 object-contain" />
          </Link>
          <h2 className="mt-4 text-2xl font-bold text-gray-900">Sign in to your account</h2>
          <p className="mt-2 text-sm text-gray-600">
            Or{' '}
            {/* Use next/link */}
            <Link href="/register" className="font-medium text-primary-600 hover:text-primary-500">
              create a new account
            </Link>
          </p>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={loginSchema}
          onSubmit={(values) => {
            loginMutation.mutate(values);
          }}
        >
          {({ isSubmitting }) => (
            <Form className="space-y-6">
              <div>
                <label htmlFor="credential" className="block text-sm font-medium text-gray-700">
                  Username or Email
                </label>
                <div className="mt-1">
                  <Field
                    id="credential"
                    name="credential"
                    type="text"
                    autoComplete="username email"
                    required // Added HTML5 required for basic validation
                    className="form-input block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" // Updated styles
                    placeholder="Username or Email"
                  />
                  <ErrorMessage name="credential" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between">
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                    Password
                  </label>
                  <div className="text-sm">
                    {/* TODO: Implement forgot password functionality */}
                    <Link href="#" className="font-medium text-primary-600 hover:text-primary-500">
                      Forgot your password?
                    </Link>
                  </div>
                </div>
                <div className="mt-1">
                  <Field
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    required // Added HTML5 required
                    className="form-input block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" // Updated styles
                  />
                  <ErrorMessage name="password" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>

              {/* TODO: Implement "Remember me" functionality if needed */}
              {/*
              <div className="flex items-center">
                <Field
                  type="checkbox"
                  name="remember"
                  id="remember"
                  className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <label htmlFor="remember" className="ml-2 block text-sm text-gray-900">
                  Remember me
                </label>
              </div>
              */}

              <div>
                <button
                  type="submit"
                  disabled={loginMutation.isPending}
                  className="flex w-full justify-center rounded-md border border-transparent bg-primary-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-75" // Added disabled styles
                >
                  {loginMutation.isPending ? 'Signing in...' : 'Sign in'}
                </button>
              </div>
            </Form>
          )}
        </Formik>

        {/* Error display logic remains the same, but toast handles specific messages now */}
        {/* The toast notification provides better feedback than this block */}
        {/* Consider removing this block if toast is sufficient */}
        {/* {loginMutation.isError && (
          <div className="mt-4 rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Sign in failed</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{loginMutation.error instanceof Error ? loginMutation.error.message : 'An unknown error occurred.'}</p>
                </div>
              </div>
            </div>
          </div>
        )} */}
      </div>
    </div>
  );
};

export default LoginPage;
