import { Metadata } from 'next';
import { 
  getTrendingDeals, 
  getNewestDeals, 
  getGettingWarmDeals, 
  getMostCommentedDeals 
} from '../src/services/dealService'; 
import HomePageClient from './HomePageClient';

export const metadata: Metadata = {
  title: 'NiceDeals - Find the Best Deals Online',
  description: 'Discover and share the hottest deals, discounts, and coupons on electronics, fashion, home goods, and more.',
  openGraph: {
    title: 'NiceDeals - Find the Best Deals Online',
    description: 'Your go-to community for saving money.',
  },
};

async function fetchHomepageData() {
  try {
    const [trending, newest, gettingWarm, mostCommented] = await Promise.all([
      getTrendingDeals(8), 
      getNewestDeals(4),   
      getGettingWarmDeals(4),
      getMostCommentedDeals(8) 
    ]);
    return {
      trendingDeals: trending || [],
      newestDeals: newest || [],
      gettingWarmDeals: gettingWarm || [],
      mostCommentedDeals: mostCommented || [],
      error: null,
    };
  } catch (error) {
    console.error("Failed to fetch homepage data:", error);
    return {
      trendingDeals: [],
      newestDeals: [],
      gettingWarmDeals: [],
      mostCommentedDeals: [],
      error: "Failed to load deals. Please try again later.",
    };
  }
}

export default async function HomePage() {
  const { 
    trendingDeals, 
    newestDeals, 
    gettingWarmDeals, 
    mostCommentedDeals, 
    error 
  } = await fetchHomepageData();

  if (error) {
    return (
      <main className="container mx-auto px-4 py-8">
        <p className="text-center text-red-600">{error}</p>
      </main>
    );
  }

  return (
    <HomePageClient 
      gettingWarmDeals={gettingWarmDeals}
      newestDeals={newestDeals}
    />
  );
}
