'use client';

import React from 'react';
import Link from 'next/link'; // Use next/link
import { useRouter } from 'next/navigation'; // Use next/navigation
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { useAuth } from '@/hooks/useAuth'; // Use alias

// Define the form values type
interface RegisterFormValues {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  termsAccepted: boolean;
}

// Validation schema (remains the same)
const registerSchema = Yup.object().shape({
  username: Yup.string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be at most 20 characters')
    .matches(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens')
    .required('Username is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\\]{8,}$/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and can include special characters (except backslash)'
    )
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Confirm password is required'),
  termsAccepted: Yup.boolean()
    .oneOf([true], 'You must accept the Terms of Service and Privacy Policy')
    .required('You must accept the Terms of Service and Privacy Policy'),
});

const RegisterPage: React.FC = () => {
  const { register } = useAuth();
  const router = useRouter(); // Use Next.js router

  // Register mutation (remains largely the same)
  const registerMutation = useMutation({
    mutationFn: async (values: RegisterFormValues) => {
      const { username, email, password, termsAccepted } = values;
      await register(username, email, password, termsAccepted);
    },
    onSuccess: () => {
      toast.success('Registration successful! Please check your email to verify your account before logging in.');
      // Redirect to login page after successful registration
      // TODO: Consider creating a dedicated "Check your email" page (/verify-email-sent)
      router.push('/login');
    },
    onError: (error: any) => {
      // Check if error has a response and custom message
      const message = error?.response?.data?.message || error.message || 'Registration failed. Please try again.';
      toast.error(message);
    },
  });

  // Initial form values (remains the same)
  const initialValues: RegisterFormValues = {
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    termsAccepted: false,
  };

  return (
    <div className="mx-auto max-w-md py-12"> {/* Added spacing */}
      <div className="rounded-lg bg-white p-8 shadow-lg"> {/* Increased shadow */}
        <div className="mb-6 text-center">
          {/* Use next/link */}
          <Link href="/" className="inline-flex items-center justify-center">
            {/* Ensure logo is in /public */}
            <img src="/nicedeals-logo.png" alt="NiceDeals" className="h-16 object-contain" />
          </Link>
          <h2 className="mt-4 text-2xl font-bold text-gray-900">Create a new account</h2>
          <p className="mt-2 text-sm text-gray-600">
            Or{' '}
            {/* Use next/link */}
            <Link href="/login" className="font-medium text-primary-600 hover:text-primary-500">
              sign in to your existing account
            </Link>
          </p>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={registerSchema}
          onSubmit={(values) => {
            registerMutation.mutate(values);
          }}
        >
          {({ isSubmitting }) => (
            <Form className="space-y-6">
              {/* Username Field */}
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                  Username
                </label>
                <div className="mt-1">
                  <Field
                    id="username"
                    name="username"
                    type="text"
                    autoComplete="username"
                    required
                    className="form-input block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" // Updated styles
                    placeholder="johndoe"
                  />
                  <ErrorMessage name="username" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>

              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email address
                </label>
                <div className="mt-1">
                  <Field
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    className="form-input block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" // Updated styles
                    placeholder="<EMAIL>"
                  />
                  <ErrorMessage name="email" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>

              {/* Password Field */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <div className="mt-1">
                  <Field
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="new-password"
                    required
                    className="form-input block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" // Updated styles
                  />
                  <ErrorMessage name="password" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>

              {/* Confirm Password Field */}
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                  Confirm Password
                </label>
                <div className="mt-1">
                  <Field
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    autoComplete="new-password"
                    required
                    className="form-input block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" // Updated styles
                  />
                  <ErrorMessage name="confirmPassword" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>

              {/* Terms Acceptance Checkbox */}
              <div className="relative flex items-start"> {/* Adjusted for better alignment */}
                <div className="flex h-5 items-center">
                  <Field
                    type="checkbox"
                    name="termsAccepted"
                    id="termsAccepted"
                    className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="termsAccepted" className="font-medium text-gray-700">
                    I agree to the
                  </label>{' '}
                  {/* TODO: Link to actual terms/privacy pages */}
                  <Link href="/terms" className="font-medium text-primary-600 hover:underline">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="font-medium text-primary-600 hover:underline">
                    Privacy Policy
                  </Link>
                  <ErrorMessage name="termsAccepted" component="p" className="mt-1 text-sm text-red-600" /> {/* Display error below */}
                </div>
              </div>

              {/* Submit Button */}
              <div>
                <button
                  type="submit"
                  disabled={isSubmitting || registerMutation.isPending}
                  className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${isSubmitting || registerMutation.isPending ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {registerMutation.isPending ? 'Creating account...' : 'Create account'}
                </button>
              </div>
            </Form>
          )}
        </Formik>

        {/* General error display using toast is preferred */}
      </div>
    </div>
  );
};

export default RegisterPage;
