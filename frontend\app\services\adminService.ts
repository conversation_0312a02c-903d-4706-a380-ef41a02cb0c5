import api from './api';
import { DashboardResponse, PaginatedResponse, FilterParams } from '../types';


/**
 * Get dashboard statistics and recent activity
 */
const getDashboardStats = async (): Promise<DashboardResponse> => {
  try {
    const response = await api.get('/admin/dashboard');
    return response.data.data || { 
      stats: {
        totalUsers: 0,
        totalDeals: 0,
        activeDeals: 0,
        pendingDeals: 0,
        newUsersToday: 0,
        newDealsToday: 0,
        userGrowth: 0,
        dealGrowth: 0
      }, 
      recentActivity: [] 
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    // Return default values if API fails
    return {
      stats: {
        totalUsers: 0,
        totalDeals: 0,
        activeDeals: 0,
        pendingDeals: 0,
        newUsersToday: 0,
        newDealsToday: 0,
        userGrowth: 0,
        dealGrowth: 0
      },
      recentActivity: []
    };
  }
};

/**
 * Get the count of pending deals
 */
const getPendingDealsCount = async (): Promise<number> => {
  try {
    const dashboardData = await getDashboardStats();
    return dashboardData.stats.pendingDeals || 0;
  } catch (error) {
    console.error('Error fetching pending deals count:', error);
    return 0;
  }
};

/**
 * Get all users (admin only)
 */
const getUsers = async (params: FilterParams = {}): Promise<PaginatedResponse<any>> => {
  const { 
    page = 1, 
    limit = 10,
    search,
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = params;
  
  let url = `/admin/users?page=${page}&limit=${limit}&sortBy=${sortBy}&sortOrder=${sortOrder}`;
  
  if (search) {
    url += `&search=${encodeURIComponent(search)}`;
  }
  
  const response = await api.get(url);
  return {
    data: response.data.data?.users || [],
    totalCount: response.data.totalCount || 0,
    page,
    limit,
    totalPages: Math.ceil((response.data.totalCount || 0) / limit)
  };
};

/**
 * Get all deals (admin only)
 */
const getDeals = async (params: FilterParams = {}): Promise<PaginatedResponse<any>> => {
  // Extract sort parameters from the sort string if it's in format 'field_direction'
  let sortBy = 'created_at';
  let sortOrder = 'desc';
  
  if (params.sort) {
    const [field, order] = String(params.sort).split('_');
    if (field && order) {
      sortBy = field;
      sortOrder = order;
    }
  }
  
  const { 
    page = 1, 
    limit = 10, 
    status, 
    category, 
    store, 
    search
  } = params;
  
  let url = `/admin/deals?page=${page}&limit=${limit}`;
  
  if (status && status !== 'all') {
    url += `&status=${status}`;
  }
  
  if (category && category !== 'all') {
    url += `&category=${category}`;
  }
  
  if (store && store !== 'all') {
    url += `&store=${store}`;
  }
  
  if (search) {
    url += `&search=${encodeURIComponent(search)}`;
  }
  
  if (sortBy) {
    url += `&sortBy=${sortBy}&sortOrder=${sortOrder}`;
  }
  
  console.log('Fetching deals from URL:', url);
  const response = await api.get(url);
  console.log('Deals API response:', response.data);
  
  return {
    data: response.data.deals || [],
    totalCount: response.data.totalCount || 0,
    page,
    limit,
    totalPages: Math.ceil((response.data.totalCount || 0) / limit)
  };
};

/**
 * Get all categories (admin only)
 */
const getCategories = async (): Promise<any> => {
  const response = await api.get('/admin/categories');
  return response.data;
};

/**
 * Create a new category (admin only)
 */
const createCategory = async (categoryData: any): Promise<any> => {
  const response = await api.post('/admin/categories', categoryData);
  return response.data;
};

/**
 * Update a category (admin only)
 */
const updateCategory = async (categoryId: number, categoryData: any): Promise<any> => {
  const response = await api.put(`/admin/categories/${categoryId}`, categoryData);
  return response.data;
};

/**
 * Delete a category (admin only)
 */
const deleteCategory = async (categoryId: number): Promise<any> => {
  const response = await api.delete(`/admin/categories/${categoryId}`);
  return response.data;
};

/**
 * Get all stores (admin only)
 */
const getStores = async (): Promise<any> => {
  const response = await api.get('/admin/stores');
  return response.data;
};

/**
 * Create a new store (admin only)
 */
const createStore = async (storeData: any): Promise<any> => {
  const response = await api.post('/admin/stores', storeData);
  return response.data;
};

/**
 * Update a store (admin only)
 */
const updateStore = async (storeId: number, storeData: any): Promise<any> => {
  const response = await api.put(`/admin/stores/${storeId}`, storeData);
  return response.data;
};

/**
 * Delete a store (admin only)
 */
const deleteStore = async (storeId: number): Promise<any> => {
  const response = await api.delete(`/admin/stores/${storeId}`);
  return response.data;
};

/**
 * Get scraper logs (admin only)
 */
const getScraperLogs = async (): Promise<any> => {
  const response = await api.get('/admin/scrapers');
  return response.data;
};

/**
 * Run a scraper (admin only)
 */
const runScraper = async (scraperId: string): Promise<any> => {
  const response = await api.post(`/admin/scrapers/run/${scraperId}`);
  return response.data;
};

/**
 * Update a deal (admin only)
 */
const updateDeal = async (dealId: number, dealData: any): Promise<any> => {
  const response = await api.put(`/admin/deals/${dealId}`, dealData);
  return response.data;
};

/**
 * Delete a deal (admin only)
 */
const deleteDeal = async (dealId: number): Promise<any> => {
  const response = await api.delete(`/admin/deals/${dealId}`);
  return response.data;
};

/**
 * Activate a pending deal (admin only)
 */
const activatePendingDeal = async (dealId: number): Promise<any> => {
  const response = await api.post(`/admin/deals/${dealId}/activate`);
  return response.data;
};

/**
 * Check if a deal exists by trying to fetch it
 */
const checkDealExists = async (dealId: number): Promise<boolean> => {
  try {
    // Use the main deals endpoint to check if the deal exists
    await getDeal(dealId);
    return true;
  } catch (error: any) {
    if (error?.response?.status === 404) {
      return false;
    }
    console.error('Error checking if deal exists:', error);
    return false;
  }
};

/**
 * Get a single deal by ID (admin only)
 */
const getDeal = async (dealId: number): Promise<any> => {
  try {
    const response = await api.get(`/admin/deals/${dealId}`);
    return response.data.data || response.data;
  } catch (error) {
    console.error('Error fetching deal:', error);
    throw error;
  }
};

/**
 * Create a new deal (admin only)
 */
const createDeal = async (dealData: any): Promise<any> => {
  try {
    const response = await api.post('/deals', dealData);
    return response.data.data || response.data;
  } catch (error) {
    console.error('Error creating deal:', error);
    throw error;
  }
};

const adminService = {
  getDashboardStats,
  getPendingDealsCount,
  getUsers,
  getDeals,
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  getStores,
  createStore,
  updateStore,
  deleteStore,
  getScraperLogs,
  runScraper,
  updateDeal,
  deleteDeal,
  activatePendingDeal,
  checkDealExists,
  getDeal,
  createDeal,
  getActiveDealsCount: async (): Promise<number> => {
    try {
      // Change to use the deals endpoint with status filter
      const response = await api.get('/admin/deals', {
        params: {
          status: 'active',
          limit: 1, // We only need the count
          page: 1
        }
      });
      return response.data.totalCount || 0;
    } catch (error) {
      console.error('Error fetching active deals count:', error);
      return 0;
    }
  },
};

export default adminService;




