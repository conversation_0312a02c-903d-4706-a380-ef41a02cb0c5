import { User, ApiResponse, LoginFormValues, RegisterFormValues } from '../types';
import api from './api';

// Login
export const login = async (credentials: LoginFormValues): Promise<{ user: User; token: string }> => {
  const response = await api.post<ApiResponse<{ user: User; token: string }>>('/auth/login', credentials);
  
  if (response.data.success && response.data.data) {
    // Store the token in localStorage
    localStorage.setItem('token', response.data.data.token);
    
    // Set the token in the API headers
    api.setAuthToken(response.data.data.token);
    
    return response.data.data;
  } else {
    throw new Error(response.data.error || 'Login failed');
  }
};

// Register
export const register = async (userData: RegisterFormValues): Promise<{ user: User; token: string }> => {
  const response = await api.post<ApiResponse<{ user: User; token: string }>>('/auth/register', userData);
  
  if (response.data.success && response.data.data) {
    // Store the token in localStorage
    localStorage.setItem('token', response.data.data.token);
    
    // Set the token in the API headers
    api.setAuthToken(response.data.data.token);
    
    return response.data.data;
  } else {
    throw new Error(response.data.error || 'Registration failed');
  }
};

// Logout
export const logout = (): void => {
  // Remove the token from localStorage
  localStorage.removeItem('token');
  
  // Remove the token from the API headers
  api.removeAuthToken();
};

// Get current user
export const getCurrentUser = async (): Promise<User> => {
  const response = await api.get<ApiResponse<User>>('/auth/profile');
  
  if (response.data.success && response.data.data) {
    return response.data.data;
  } else {
    throw new Error(response.data.error || 'Failed to get current user');
  }
};

// Update user profile
export const updateProfile = async (userData: Partial<User>): Promise<User> => {
  const response = await api.put<ApiResponse<User>>('/auth/profile', userData);
  
  if (response.data.success && response.data.data) {
    return response.data.data;
  } else {
    throw new Error(response.data.error || 'Failed to update profile');
  }
};

// Change password
export const changePassword = async (
  currentPassword: string,
  newPassword: string
): Promise<void> => {
  const response = await api.put<ApiResponse<void>>('/auth/password', {
    currentPassword,
    newPassword,
  });
  
  if (!response.data.success) {
    throw new Error(response.data.error || 'Failed to change password');
  }
};
