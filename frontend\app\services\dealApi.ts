import api from './api';

const dealApi = {
  // Deals
  getDeals: async (params?: any) => {
    const response = await api.get('/deals', { params });
    return response.data;
  },

  getDealById: async (id: number) => {
    const response = await api.get(`/deals/${id}`);
    return response.data;
  },

  getNewestDeals: async () => {
    const response = await api.get('/deals/newest');
    return response.data;
  },

  getTrendingDeals: async () => {
    const response = await api.get('/deals/trending');
    return response.data;
  },

  // Votes
  submitVote: async (dealId: number, vote: 'up' | 'down') => {
    const response = await api.post('/votes', { dealId, vote });
    return response.data;
  },

  // Comments
  getComments: async (dealId: number) => {
    const response = await api.get(`/comments/${dealId}`);
    return response.data;
  },

  submitComment: async (dealId: number, content: string) => {
    const response = await api.post('/comments', { dealId, content });
    return response.data;
  }
};

export default dealApi;