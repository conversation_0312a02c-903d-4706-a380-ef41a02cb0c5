import {
  Deal,
  DealFilters,
  DealListResponse,
  DealFormValues,
  ApiResponse,
  FilterParams
} from '../types';
import api from './api';
import aiService from './aiService';
import { savedDealsService } from './savedDealsService';

// Get deals with filters
export const getDeals = async (filters: DealFilters): Promise<DealListResponse> => {
  // Construct query parameters
  const queryParams = new URLSearchParams();
  
  if (filters.search) {
    console.log('Adding search parameter:', filters.search);
    queryParams.append('query', filters.search); // Changed from 'search' to 'query' to match backend
  }
  if (filters.category) queryParams.append('category', filters.category.toString());
  if (filters.store) queryParams.append('store', filters.store.toString());
  if (filters.status && filters.status !== 'all') queryParams.append('status', filters.status);
  if (filters.sort) queryParams.append('sort', filters.sort);
  if (filters.page) queryParams.append('page', filters.page.toString());
  if (filters.pageSize) queryParams.append('pageSize', filters.pageSize.toString());
  if (filters.minPrice) queryParams.append('minPrice', filters.minPrice.toString());
  if (filters.maxPrice) queryParams.append('maxPrice', filters.maxPrice.toString());
  if (filters.dealType) queryParams.append('dealType', filters.dealType);
  
  const queryString = queryParams.toString();
  const url = `/deals${queryString ? `?${queryString}` : ''}`;
  
  try {
    console.log('getDeals: Sending request to:', url);
    const response = await api.get<ApiResponse<DealListResponse>>(url);
    console.log('getDeals: Raw response:', response);
    console.log('getDeals: Response data:', response.data);
    
    if (!response.data) {
      throw new Error('No data received from API');
    }
    
    // Handle both wrapped and unwrapped responses
    const result = response.data.data || response.data;
    console.log('getDeals: Processed result:', result);
    return result as DealListResponse;
  } catch (error: any) {
    console.error('Error in getDeals service:', error);
    throw error;
  }
};

// Get deal by ID
export const getDealById = async (id: number, userId?: number): Promise<Deal> => {
  console.log(`[dealService] Attempting to fetch deal with ID: ${id}${userId ? `, userId: ${userId}` : ''}`);
  try {
    // Ensure we're using the authentication token for this request
    // This is particularly important for pending deals where ownership matters
    const headers: { [key: string]: string } = {};
    
    // Only access localStorage in browser environment, not during SSR
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token');
      if (token) {
        headers.Authorization = `Bearer ${token}`;
        console.log('[dealService] Adding Authorization header with token');
      }
    }
    
    // Build query params if userId is provided
    let url = `/deals/${id}`;
    if (userId) {
      url += `?userId=${userId}`;
      console.log(`[dealService] Adding userId=${userId} to query params`);
    }
    
    console.log(`[dealService] Making request to ${url} with auth headers:`, headers);
    
    const response = await api.get<ApiResponse<Deal>>(url, { 
      headers
    });
    
    console.log(`[dealService] Received API response for deal ${id}:`, JSON.stringify(response.data, null, 2));
    
    // Handle potential variations in API response structure
    if (response.data.data) {
      console.log(`[dealService] Found deal ${id} inside 'data' wrapper.`);
      return response.data.data;
    } else if (response.data && 'id' in response.data && 'title' in response.data) {
      // If the API directly returns the deal without data wrapper
      console.log(`[dealService] Found deal ${id} directly in response (no 'data' wrapper).`);
      // Explicitly cast to unknown first to satisfy TypeScript if the type isn't exactly Deal
      return response.data as unknown as Deal;
    }
    
    console.error(`[dealService] Invalid or unexpected data format received for deal ${id}:`, response.data);
    throw new Error('Invalid deal data format returned from API');
  } catch (error: any) {
    console.error('Error fetching deal by ID:', error);
    
    // Check if the error is related to a pending deal
    if (error.response?.data?.isPendingDeal) {
      // Create a custom error with additional properties
      const pendingError = new Error(error.response.data.error || 'No such active deal currently');
      (pendingError as any).isPendingDeal = true;
      throw pendingError;
    }
    
    throw error;
  }
};

// Create a new deal
export const createDeal = async (dealData: DealFormValues): Promise<Deal> => {
  // Map frontend field names to backend expected names
  const backendData = {
    title: dealData.title,
    description: dealData.description,
    url: dealData.dealUrl,
    image_url: dealData.imageUrl,
    thumbnail_url: dealData.thumbnailUrl,
    price: dealData.price,
    original_price: dealData.originalPrice,
    category_id: dealData.categoryId,
    store_id: dealData.storeId,
    expires_at: dealData.expiresAt,
    status: 'pending' // Set status to pending for moderation
  };
  
  const response = await api.post<ApiResponse<Deal>>('/deals', backendData);
  return response.data.data as Deal;
};

// Update an existing deal
export const updateDeal = async (id: number, dealData: Partial<DealFormValues>): Promise<Deal> => {
  const response = await api.put<ApiResponse<Deal>>(`/deals/${id}`, dealData);
  return response.data.data as Deal;
};

// Delete a deal
export const deleteDeal = async (id: number): Promise<void> => {
  await api.delete(`/deals/${id}`);
};

// Upvote a deal
export const upvoteDeal = async (id: number): Promise<void> => {
  await api.post(`/votes/${id}`, { value: 1 });
};

// Downvote a deal
export const downvoteDeal = async (id: number): Promise<void> => {
  await api.post(`/votes/${id}`, { value: -1 });
};

// Remove vote
export const removeVote = async (id: number): Promise<void> => {
  await api.delete(`/votes/${id}`);
};

// Get trending deals
export const getTrendingDeals = async (limit: number = 6): Promise<Deal[]> => {
  try {
    const response = await api.get<ApiResponse<Deal[]>>(`/deals/trending?limit=${limit}`);
    // Handle both wrapped and unwrapped responses
    return (response.data.data || response.data || []) as Deal[];
  } catch (error) {
    console.error('Error fetching trending deals:', error);
    return [];
  }
};

// Get newest deals
export const getNewestDeals = async (limit: number = 3): Promise<Deal[]> => {
  try {
    const response = await api.get<ApiResponse<Deal[]>>(`/deals/newest?limit=${limit}`);
    // Handle both wrapped and unwrapped responses
    return (response.data.data || response.data || []) as Deal[];
  } catch (error) {
    console.error('Error fetching newest deals:', error);
    return [];
  }
};

// Get trending by temperature deals
export const getGettingWarmDeals = async (limit: number = 10): Promise<Deal[]> => {
  try {
    const response = await api.get<ApiResponse<Deal[]>>(`/deals/getting-warm?limit=${limit}`);
    // Handle both wrapped and unwrapped responses
    return (response.data.data || response.data || []) as Deal[];
  } catch (error) {
    console.error('Error fetching trending by temperature deals:', error);
    return [];
  }
};

// Get most commented deals
export const getMostCommentedDeals = async (limit: number = 10): Promise<Deal[]> => {
  try {
    const response = await api.get<ApiResponse<Deal[]>>(`/deals/most-commented?limit=${limit}`);
    // Handle both wrapped and unwrapped responses
    return (response.data.data || response.data || []) as Deal[];
  } catch (error) {
    console.error('Error fetching most commented deals:', error);
    return [];
  }
};

// Get user's deals
export const getUserDeals = async (
  userId: number,
  page: number = 1,
  pageSize: number = 30
): Promise<DealListResponse> => {
  try {
    const response = await api.get<ApiResponse<DealListResponse>>(
      `/deals/user/${userId}?page=${page}&pageSize=${pageSize}`
    );
    if (response.data.data) {
      return response.data.data;
    } else {
      // If the API directly returns the response without data wrapper
      return response.data as unknown as DealListResponse;
    }
  } catch (error) {
    throw error;
  }
};

// Get current user's deals
export const getCurrentUserDeals = async (): Promise<Deal[]> => {
  const response = await api.get<ApiResponse<Deal[]>>('/user/deals');
  return response.data.data || [];
};

// Get my deals
export const getMyDeals = async (
  page: number = 1,
  pageSize: number = 30
): Promise<DealListResponse> => {
  try {
    const response = await api.get<ApiResponse<DealListResponse>>(
      `/deals/user/me?page=${page}&pageSize=${pageSize}`
    );
    if (response.data.data) {
      return response.data.data;
    } else {
      // If the API directly returns the response without data wrapper
      return response.data as unknown as DealListResponse;
    }
  } catch (error) {
    throw error;
  }
};

// Get admin deals
export const getAdminDeals = async (filters: FilterParams): Promise<DealListResponse> => {
  // Construct query parameters
  const queryParams = new URLSearchParams();
  
  if (filters.search) queryParams.append('search', filters.search);
  if (filters.category && filters.category !== 'all') queryParams.append('category', filters.category);
  if (filters.store && filters.store !== 'all') queryParams.append('store', filters.store);
  if (filters.status && filters.status !== 'all') queryParams.append('status', filters.status);
  if (filters.page) queryParams.append('page', filters.page.toString());
  if (filters.limit) queryParams.append('limit', filters.limit.toString());
  if (filters.sort) queryParams.append('sort', filters.sort);
  
  const queryString = queryParams.toString();
  const url = `/admin/deals${queryString ? `?${queryString}` : ''}`;
  
  console.log('getAdminDeals: Sending request to:', url);
  
  try {
    const response = await api.get<{
      success: boolean;
      deals: Deal[];
      totalCount: number;
      page: number;
      limit: number;
      totalPages: number;
    }>(url);
    
    console.log('getAdminDeals: Response:', response.data);
    
    if (!response.data.success) {
      throw new Error('Failed to fetch admin deals');
    }

    // The backend now returns all pagination info
    return {
      deals: response.data.deals,
      totalCount: response.data.totalCount,
      page: response.data.page,
      pageSize: response.data.limit,
      totalPages: response.data.totalPages
    };
  } catch (error) {
    console.error('Error in getAdminDeals:', error);
    throw error;
  }
};

// Upload deal image
export const uploadDealImage = async (file: File): Promise<{imageUrl: string, thumbnailUrl: string}> => {
  const formData = new FormData();
  formData.append('image', file);
  
  const response = await api.post<ApiResponse<{ imageUrl: string, thumbnailUrl: string }>>(
    '/deals/upload-image',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
  
  return response.data.data || { imageUrl: '', thumbnailUrl: '' };
};

// Activate a pending deal with AI processing
export const activatePendingDeal = async (dealId: number): Promise<Deal> => {
  try {
    // Use the admin endpoint to get details of a pending deal
    // No need to fetch deal details beforehand, we can directly activate it
    console.log(`Processing deal #${dealId}`);
    
    // Send activation request directly to the admin endpoint
    const processResponse = await api.post<ApiResponse<Deal>>(`/admin/deals/${dealId}/activate`, {
      processTasks: {
        improveTitle: true,
        generateDescription: true,
        localizeImage: true
      }
    });
    
    return processResponse.data.data as Deal;
  } catch (error) {
    console.error(`Error activating deal #${dealId}:`, error);
    throw new Error(`Failed to activate deal #${dealId}: ${(error as Error).message}`);
  }
};

// Save a deal
export const saveDeal = async (dealId: number, userId: number) => {
  return savedDealsService.saveDeal(dealId, userId);
};

// Unsave a deal
export const unsaveDeal = async (dealId: number, userId: number) => {
  return savedDealsService.unsaveDeal(dealId, userId);
};

// Check if a deal is saved
export const checkIfDealSaved = async (dealId: number, userId: number) => {
  return savedDealsService.checkIfDealSaved(dealId, userId);
};

// Get user's saved deals
export const getSavedDeals = async (
  page: number = 1,
  pageSize: number = 30
): Promise<DealListResponse> => {
  try {
    const response = await api.get<ApiResponse<DealListResponse>>(
      `/deals/saved?page=${page}&pageSize=${pageSize}`
    );
    
    // If the response has a data property that matches DealListResponse, return it
    if (response.data.data) {
      return response.data.data;
    }
    
    // Check if response.data has all required DealListResponse properties
    if (
      'deals' in response.data &&
      'totalCount' in response.data &&
      'page' in response.data &&
      'pageSize' in response.data &&
      'totalPages' in response.data
    ) {
      const responseData = response.data as {
        deals: Deal[];
        totalCount: number;
        page: number;
        pageSize: number;
        totalPages: number;
      };
      
      return {
        deals: responseData.deals,
        totalCount: responseData.totalCount,
        page: responseData.page,
        pageSize: responseData.pageSize,
        totalPages: responseData.totalPages
      };
    }
    
    // If neither case matches, return a default structure
    return {
      deals: [],
      totalCount: 0,
      page: page,
      pageSize: pageSize,
      totalPages: 0
    };
  } catch (error) {
    throw error;
  }
};

// Get related deals (same category, most recent, exclude current deal)
export const getRelatedDeals = async (
  categoryId: number,
  currentDealId: number,
  limit: number = 4
): Promise<Deal[]> => {
  try {
    // Create filter to get deals in the same category, excluding current deal
    const queryParams = new URLSearchParams();
    queryParams.append('category', categoryId.toString());
    queryParams.append('excludeDealId', currentDealId.toString());
    queryParams.append('status', 'active');
    queryParams.append('sort', 'newest');
    queryParams.append('limit', limit.toString());
    
    const response = await api.get<ApiResponse<Deal[]>>(`/deals/related?${queryParams.toString()}`);
    
    if (response.data.data) {
      return response.data.data;
    } else if (Array.isArray(response.data)) {
      return response.data as unknown as Deal[];
    }
    
    return [];
  } catch (error) {
    console.error('Error fetching related deals:', error);
    return []; // Return empty array on error
  }
};

// Export all functions as a service object
export const dealService = {
  getDeals,
  getDealById,
  createDeal,
  updateDeal,
  deleteDeal,
  uploadDealImage,
  getNewestDeals,
  getTrendingDeals,
  getGettingWarmDeals,
  getMostCommentedDeals,
  upvoteDeal,
  downvoteDeal,
  removeVote,
  getUserDeals,
  getMyDeals,
  getCurrentUserDeals,
  getAdminDeals,
  activatePendingDeal,
  saveDeal,
  unsaveDeal,
  checkIfDealSaved,
  getSavedDeals,
  getRelatedDeals
};
