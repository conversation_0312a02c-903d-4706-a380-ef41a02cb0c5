'use server';

import { cookies } from 'next/headers';
import { Deal, DealFilters, DealListResponse, Category, Store, PaginationInfo } from '../types';

// Base API URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5010/api';

// Function to get deals with filters
export async function getDeals(filters: DealFilters): Promise<DealListResponse> {
  try {
    // Build query string from filters
    const queryParams = new URLSearchParams();
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.category) queryParams.append('category', filters.category.toString());
    if (filters.store) queryParams.append('store', filters.store.toString());
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.sort) queryParams.append('sort', filters.sort);
    if (filters.page) queryParams.append('page', filters.page.toString());
    if (filters.pageSize) queryParams.append('pageSize', filters.pageSize.toString());
    if (filters.minPrice !== undefined) queryParams.append('minPrice', filters.minPrice.toString());
    if (filters.maxPrice !== undefined) queryParams.append('maxPrice', filters.maxPrice.toString());
    if (filters.dealType) queryParams.append('dealType', filters.dealType);

    // Make the API request
    const response = await fetch(`${API_BASE_URL}/deals?${queryParams.toString()}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch deals: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();

    // Check if the API returned the deals and pagination info at the top level
    if (data && Array.isArray(data.deals) && typeof data.totalCount === 'number') {
      const currentPage = data.page || filters.page || 1;
      const pageSize = data.pageSize || filters.pageSize || 30;
      const totalCount = data.totalCount;

      const paginationInfo: PaginationInfo = {
        currentPage: currentPage,
        totalPages: data.totalPages || 0,
        totalCount: totalCount,
        pageSize: pageSize,
        from: totalCount > 0 ? (currentPage - 1) * pageSize + 1 : 0,
        to: totalCount > 0 ? Math.min(currentPage * pageSize, totalCount) : 0,
      };

      const mappedDeals: Deal[] = data.deals.map((apiDeal: any) => ({
        id: apiDeal.id,
        title: apiDeal.title,
        description: apiDeal.description,
        price: apiDeal.price,
        originalPrice: apiDeal.original_price,
        dealUrl: apiDeal.deal_url,
        imageUrl: apiDeal.image_url,
        thumbnailUrl: apiDeal.thumbnail_url,
        status: apiDeal.status,
        temperature: apiDeal.temperature,
        expiresAt: apiDeal.expires_at,
        updatedAt: apiDeal.updated_at,
        createdAt: apiDeal.created_at,
        categoryId: apiDeal.category_id,
        categoryName: apiDeal.category_name,
        storeId: apiDeal.store_id,
        storeName: apiDeal.store_name,
        commentCount: apiDeal.comment_count,
        userId: apiDeal.user_id,
        username: apiDeal.user_username, 
        url: apiDeal.url,
        source: apiDeal.source,
        totalVotes: apiDeal.total_votes,
        userVote: apiDeal.user_vote,
        affiliateUrl: apiDeal.affiliate_url,
        upvotes: apiDeal.upvotes,
        downvotes: apiDeal.downvotes,
      }));

      return {
        deals: mappedDeals,
        pagination: paginationInfo,
      };
    } else {
      // Log unexpected structure if it doesn't match the expected flat structure either
      console.error('Unexpected API response structure for getDeals after attempting to map:', data);
      // Return the default error structure
      return {
        deals: [],
        pagination: {
          currentPage: filters.page || 1,
          totalPages: 0,
          totalCount: 0,
          pageSize: filters.pageSize || 30,
          from: 0,
          to: 0,
        }
      };
    }
  } catch (error) {
    console.error('Error fetching deals:', error);
    return {
      deals: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        pageSize: 30, // Default pageSize
        from: 0,
        to: 0,
      }
    };
  }
}

// Function to get a single deal by ID
export async function getDealById(id: number): Promise<Deal | null> {
  try {
    const cookieStore = cookies();
    const token = cookieStore.get('auth_token')?.value;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await fetch(`${API_BASE_URL}/deals/${id}`, { headers });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch deal: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    const apiDeal = data.data;
    if (!apiDeal) {
      return null;
    }

    // Map snake_case to camelCase
    const mappedDeal: Deal = {
      id: apiDeal.id,
      title: apiDeal.title,
      description: apiDeal.description,
      price: apiDeal.price,
      originalPrice: apiDeal.original_price,
      dealUrl: apiDeal.deal_url,
      imageUrl: apiDeal.image_url,
      thumbnailUrl: apiDeal.thumbnail_url,
      status: apiDeal.status,
      temperature: apiDeal.temperature,
      expiresAt: apiDeal.expires_at,
      updatedAt: apiDeal.updated_at,
      createdAt: apiDeal.created_at,
      categoryId: apiDeal.category_id,
      categoryName: apiDeal.category_name,
      storeId: apiDeal.store_id,
      storeName: apiDeal.store_name,
      commentCount: apiDeal.comment_count,
      userId: apiDeal.user_id,
      username: apiDeal.user_username, 
      url: apiDeal.url,
      source: apiDeal.source,
      totalVotes: apiDeal.total_votes,
      userVote: apiDeal.user_vote,
      affiliateUrl: apiDeal.affiliate_url,
      upvotes: apiDeal.upvotes,
      downvotes: apiDeal.downvotes,
    };
    return mappedDeal;

  } catch (error) {
    console.error(`Error fetching deal with ID ${id}:`, error);
    return null;
  }
}

// Function to get a category by ID
export async function getCategoryById(id: number): Promise<Category | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/categories/${id}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch category: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    const categoryData = data.data || data;
    if (categoryData && typeof categoryData.id === 'number' && typeof categoryData.name === 'string') {
      return categoryData as Category;
    }
    console.warn(`Category data for ID ${id} not found or malformed:`, data);
    return null;
  } catch (error) {
    console.error(`Error fetching category with ID ${id}:`, error);
    return null;
  }
}

// Function to get a store by ID
export async function getStoreById(id: number): Promise<Store | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/stores/${id}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch store: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    const storeData = data.data || data;
    if (storeData && typeof storeData.id === 'number' && typeof storeData.name === 'string') {
      return storeData as Store;
    }
    console.warn(`Store data for ID ${id} not found or malformed:`, data);
    return null;
  } catch (error) {
    console.error(`Error fetching store with ID ${id}:`, error);
    return null;
  }
}

// Function to get current user's deals (Server Action)
export async function getCurrentUserDeals(): Promise<Deal[]> {
  try {
    const cookieStore = cookies();
    const token = cookieStore.get('auth_token')?.value;

    if (!token) {
      console.error('Authentication token not found.');
      return []; 
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    };

    const response = await fetch(`${API_BASE_URL}/users/me/deals`, { headers });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Failed to fetch current user deals: ${response.status} ${response.statusText}`, errorText);
      throw new Error(`Failed to fetch current user deals: ${response.status}`);
    }

    const data = await response.json();
    const apiDeals = data.data;
    if (!Array.isArray(apiDeals)) {
      console.warn('getCurrentUserDeals: API did not return an array of deals as data.data', data);
      return [];
    }

    const mappedDeals: Deal[] = apiDeals.map((apiDeal: any) => ({
      id: apiDeal.id,
      title: apiDeal.title,
      description: apiDeal.description,
      price: apiDeal.price,
      originalPrice: apiDeal.original_price,
      dealUrl: apiDeal.deal_url,
      imageUrl: apiDeal.image_url,
      thumbnailUrl: apiDeal.thumbnail_url,
      status: apiDeal.status,
      temperature: apiDeal.temperature,
      expiresAt: apiDeal.expires_at,
      updatedAt: apiDeal.updated_at,
      createdAt: apiDeal.created_at,
      categoryId: apiDeal.category_id,
      categoryName: apiDeal.category_name,
      storeId: apiDeal.store_id,
      storeName: apiDeal.store_name,
      commentCount: apiDeal.comment_count,
      userId: apiDeal.user_id,
      username: apiDeal.user_username, 
      url: apiDeal.url,
      source: apiDeal.source,
      totalVotes: apiDeal.total_votes,
      userVote: apiDeal.user_vote,
      affiliateUrl: apiDeal.affiliate_url,
      upvotes: apiDeal.upvotes,
      downvotes: apiDeal.downvotes,
    }));
    return mappedDeals;

  } catch (error) {
    console.error('Error fetching current user deals:', error);
    return [];
  }
}

// Function to get related deals
export async function getRelatedDeals(
  categoryId: number,
  currentDealId: number,
  limit: number = 4
): Promise<Deal[]> {
  try {
    const queryParams = new URLSearchParams({
      categoryId: categoryId.toString(),
      currentDealId: currentDealId.toString(),
      limit: limit.toString(),
    });

    const response = await fetch(`${API_BASE_URL}/deals/related?${queryParams.toString()}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Failed to fetch related deals: ${response.status} ${response.statusText}`, errorText);
      throw new Error(`Failed to fetch related deals: ${response.status}`);
    }

    const data = await response.json();
    const apiDeals = data.data; 

    if (!Array.isArray(apiDeals)) {
      console.warn('getRelatedDeals: API did not return an array of deals as data.data', data);
      return [];
    }

    const mappedDeals: Deal[] = apiDeals.map((apiDeal: any) => ({
      id: apiDeal.id,
      title: apiDeal.title,
      description: apiDeal.description,
      price: apiDeal.price,
      originalPrice: apiDeal.original_price,
      dealUrl: apiDeal.deal_url,
      imageUrl: apiDeal.image_url,
      thumbnailUrl: apiDeal.thumbnail_url,
      status: apiDeal.status,
      temperature: apiDeal.temperature,
      expiresAt: apiDeal.expires_at,
      updatedAt: apiDeal.updated_at,
      createdAt: apiDeal.created_at,
      categoryId: apiDeal.category_id,
      categoryName: apiDeal.category_name,
      storeId: apiDeal.store_id,
      storeName: apiDeal.store_name,
      commentCount: apiDeal.comment_count,
      userId: apiDeal.user_id,
      username: apiDeal.user_username, 
      url: apiDeal.url,
      source: apiDeal.source,
      totalVotes: apiDeal.total_votes,
      userVote: apiDeal.user_vote,
      affiliateUrl: apiDeal.affiliate_url,
      upvotes: apiDeal.upvotes,
      downvotes: apiDeal.downvotes,
    }));
    return mappedDeals;

  } catch (error) {
    console.error('Error fetching related deals:', error);
    return [];
  }
}
