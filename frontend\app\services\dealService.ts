'use client';

import axios from 'axios';
import { toast } from 'react-hot-toast';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import api from './api';
import aiService from './aiService';
import {
  Deal,
  DealFilters,
  DealListResponse,
  DealFormValues,
  ApiResponse,
  FilterParams,
  Category,
  Store
} from '../types';

// --- Local Storage and Caching ---
// const SAVED_DEALS_KEY = 'savedDeals'; // Deprecated for saved deals

// interface SavedDealsCache { // Deprecated for saved deals
//   userId: number;
//   dealIds: number[];
//   lastUpdated: number;
// }

// let savedDealsCache: SavedDealsCache | null = null; // Deprecated for saved deals

// Cache duration: 5 minutes
// const CACHE_DURATION = 5 * 60 * 1000; // Deprecated for saved deals

// Debug logging
const DEBUG = 1;
const debugLog = (...args: any[]) => {
  if (DEBUG > 0) {
    console.log('[DealService]', ...args);
  }
};

// --- Helper Functions ---

// function loadFromLocalStorage(): SavedDealsCache | null { // Deprecated for saved deals
//   try {
//     const saved = localStorage.getItem(SAVED_DEALS_KEY);
//     if (!saved) return null;
    
//     const cache = JSON.parse(saved);
//     // Validate cache structure
//     if (cache.userId && Array.isArray(cache.dealIds) && cache.lastUpdated) {
//       return cache;
//     }
//     return null;
//   } catch (error) {
//     console.error('Error loading saved deals from localStorage:', error);
//     return null;
//   }
// }

// function saveToLocalStorage(cache: SavedDealsCache): void { // Deprecated for saved deals
//   try {
//     localStorage.setItem(SAVED_DEALS_KEY, JSON.stringify(cache));
//   } catch (error) {
//     console.error('Error saving to localStorage:', error);
//   }
// }

function getUserIdFromToken(token: string | null | undefined): number | null {
  if (!token) return null;
  
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const payload = JSON.parse(window.atob(base64));
    return payload.userId || null;
  } catch (error) {
    console.error('Error parsing token:', error);
    return null;
  }
}

// --- Core API Functions ---

export async function getDeals(filters: DealFilters): Promise<DealListResponse> {
  try {
    const queryParams = new URLSearchParams();
    
    // Add filters to query params
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.category) queryParams.append('category', filters.category.toString());
    if (filters.store) queryParams.append('store', filters.store.toString());
    if (filters.status && filters.status !== 'all') queryParams.append('status', filters.status);
    if (filters.sort) queryParams.append('sort', filters.sort);
    if (filters.page) queryParams.append('page', filters.page.toString());
    if (filters.pageSize) queryParams.append('pageSize', filters.pageSize.toString());
    if (filters.minPrice !== undefined) queryParams.append('minPrice', filters.minPrice.toString());
    if (filters.maxPrice !== undefined) queryParams.append('maxPrice', filters.maxPrice.toString());
    if (filters.dealType) queryParams.append('dealType', filters.dealType);

    const response = await api.get<ApiResponse<DealListResponse>>(`/deals?${queryParams.toString()}`);
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }
    // If API indicates not successful or data is missing, throw error to be caught by catch block
    throw new Error(response.data?.error || 'Failed to fetch deals from API');
  } catch (error: any) {
    console.error('Error fetching deals:', error);
    toast.error('Failed to load deals');
    return {
      deals: [],
      page: 1, // Default page
      totalPages: 0,
      totalCount: 0,
      pageSize: filters.pageSize || 30
    };
  }
}

export async function getDealById(id: number, userId?: number): Promise<Deal | null> {
  try {
    const response = await api.get<ApiResponse<Deal>>(`/deals/${id}${userId ? `?userId=${userId}` : ''}`);
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }
    // If API indicates not successful, data is missing, or if there's an error in response.data.error
    if (response.data?.error) {
      console.error(`Error fetching deal ${id}:`, response.data.error);
      toast.error(response.data.error || 'Failed to load deal');
    }
    return null; // Return null if not successful or no data, caught by catch block if network error
  } catch (error) {
    console.error(`Error fetching deal ${id}:`, error);
    toast.error('Failed to load deal');
    return null;
  }
}

// --- Saved Deals Functions ---

export async function fetchSavedDealIds(): Promise<number[]> {
  try {
    // Fetch from API. Assumes /users/me/saved-deals returns Deal[] directly in response.data
    const response = await api.get<Deal[]>('/users/me/saved-deals');
    
    // response.data should be Deal[] here
    if (response.data && Array.isArray(response.data)) {
      const dealIds = response.data.map((deal: Deal) => deal.id);
      return dealIds;
    }
    console.warn('fetchSavedDealIds: response.data was not an array as expected, or was null/undefined.');
    return []; // Return empty if data is not in the expected format
  } catch (error) {
    console.error('Error fetching saved deal IDs:', error);
    // In case of an API error, react-query will handle the error state.
    // Returning an empty array might be misleading if the query itself fails.
    // However, for the function signature Promise<number[]>, an empty array is a valid error state.
    return [];
  }
}

export async function checkIfDealSaved(dealId: number): Promise<boolean> {
  // This function is being deprecated in favor of DealCard using useQuery(['savedDealIds'])
  // and checking the result directly.
  // For now, it will rely on fetchSavedDealIds which is now API-only.
  try {
    // const cached = loadFromLocalStorage(); // Deprecated
    // if (cached) { // Deprecated
    //   return cached.dealIds.includes(dealId); // Deprecated
    // }
    
    // If no cache, fetch from API
    const savedDeals = await fetchSavedDealIds(); // This now directly hits the API
    return savedDeals.includes(dealId);
  } catch (error) {
    console.error('Error checking if deal is saved:', error);
    return false;
  }
}

export async function saveDeal(dealId: number): Promise<void> {
  try {
    await api.post(`/deals/${dealId}/save`);
    // localStorage logic removed, react-query will handle cache invalidation in the component
    toast.success('Deal saved');
  } catch (error) {
    console.error('Error saving deal:', error);
    let errorMessage = 'Failed to save deal';
    if (axios.isAxiosError(error) && error.response?.data?.message) {
      errorMessage = error.response.data.message;
      console.error('Backend error message:', error.response.data.message);
    }
    toast.error(errorMessage);
    throw error;
  }
}

export async function unsaveDeal(dealId: number): Promise<void> {
  try {
    await api.delete(`/deals/${dealId}/save`);
    // localStorage logic removed, react-query will handle cache invalidation in the component
    toast.success('Deal removed from saved');
  } catch (error) {
    console.error('Error unsaving deal:', error);
    toast.error('Failed to remove saved deal');
    throw error;
  }
}

// --- Voting Functions ---

export async function upvoteDeal(dealId: number): Promise<void> {
  try {
    await api.post(`/votes/${dealId}`, { value: 1 });
    toast.success('Upvoted deal');
  } catch (error) {
    console.error('Error upvoting deal:', error);
    toast.error('Failed to upvote deal');
    throw error;
  }
}

export async function downvoteDeal(dealId: number): Promise<void> {
  try {
    await api.post(`/votes/${dealId}`, { value: -1 });
    toast.success('Downvoted deal');
  } catch (error) {
    console.error('Error downvoting deal:', error);
    toast.error('Failed to downvote deal');
    throw error;
  }
}

export async function removeVote(dealId: number): Promise<void> {
  try {
    await api.post(`/votes`, { dealId: dealId, voteType: 0 });
    toast.success('Vote removed');
  } catch (error) {
    console.error('Error removing vote:', error);
    toast.error('Failed to remove vote');
    throw error;
  }
}

// --- Deal Management Functions ---

export async function createDeal(dealData: DealFormValues): Promise<Deal> {
  try {
    const response = await api.post<ApiResponse<Deal>>('/deals', dealData);
    if (response.data && response.data.success && response.data.data) {
      toast.success('Deal created successfully');
      return response.data.data;
    }
    throw new Error(response.data?.error || 'Failed to create deal');
  } catch (error) {
    console.error('Error creating deal:', error);
    toast.error('Failed to create deal');
    throw error;
  }
}

export async function updateDeal(id: number, dealData: Partial<DealFormValues>): Promise<Deal> {
  try {
    const response = await api.put<ApiResponse<Deal>>(`/deals/${id}`, dealData);
    if (response.data && response.data.success && response.data.data) {
      toast.success('Deal updated successfully');
      return response.data.data;
    }
    throw new Error(response.data?.error || 'Failed to update deal');
  } catch (error) {
    console.error(`Error updating deal ${id}:`, error);
    toast.error('Failed to update deal');
    throw error;
  }
}

export async function deleteDeal(id: number): Promise<void> {
  try {
    await api.delete(`/deals/${id}`);
    toast.success('Deal deleted successfully');
  } catch (error) {
    console.error(`Error deleting deal ${id}:`, error);
    toast.error('Failed to delete deal');
    throw error;
  }
}

// --- Specialized Deal Queries ---

export async function getTrendingDeals(limit: number = 6): Promise<Deal[]> {
  try {
    const response = await api.get<ApiResponse<Deal[]>>(`/deals/trending?limit=${limit}`);
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching trending deals:', error);
    return [];
  }
}

export async function getNewestDeals(limit: number = 3): Promise<Deal[]> {
  try {
    const response = await api.get<ApiResponse<Deal[]>>(`/deals/newest?limit=${limit}`);
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching newest deals:', error);
    return [];
  }
}

export async function getGettingWarmDeals(limit: number = 10): Promise<Deal[]> {
  try {
    const response = await api.get<ApiResponse<Deal[]>>(`/deals/getting-warm?limit=${limit}`);
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching getting warm deals:', error);
    return [];
  }
}

export async function getMostCommentedDeals(limit: number = 10): Promise<Deal[]> {
  try {
    const response = await api.get<ApiResponse<Deal[]>>(`/deals/most-commented?limit=${limit}`);
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching most commented deals:', error);
    return [];
  }
}

// --- User-Specific Queries ---

export async function getUserDeals(
  userId: number,
  page: number = 1,
  pageSize: number = 30
): Promise<DealListResponse> {
  try {
    const response = await api.get<ApiResponse<DealListResponse>>(
      `/users/${userId}/deals?page=${page}&pageSize=${pageSize}`
    );
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data?.error || `Failed to fetch deals for user ${userId}`);
  } catch (error) {
    console.error(`Error fetching deals for user ${userId}:`, error);
    return {
      deals: [],
      totalCount: 0,
      page: page,
      pageSize: pageSize,
      totalPages: 1, // Default to 1 page on error
    };
  }
}

export async function getCurrentUserDeals(): Promise<Deal[]> {
  try {
    const response = await api.get<ApiResponse<Deal[]>>('/users/me/deals');
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching current user deals:', error);
    return [];
  }
}

export async function getSavedDeals(
  page: number = 1,
  pageSize: number = 30
): Promise<DealListResponse> {
  try {
    // The backend returns Deal[] directly for this endpoint, not ApiResponse<DealListResponse>
    const response = await api.get<Deal[]>( // Changed type here
      `/users/me/saved-deals?page=${page}&pageSize=${pageSize}`
    );
    const deals = response.data || []; // response.data is Deal[]

    // Construct DealListResponse since backend doesn't provide full pagination for this specific route
    const totalCount = deals.length;
    const paginatedDeals = deals.slice((page - 1) * pageSize, page * pageSize);
    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      deals: paginatedDeals,
      totalCount: totalCount,
      page: page,
      pageSize: pageSize,
      totalPages: totalPages > 0 ? totalPages : 1, // Ensure at least 1 page if deals exist
    };
  } catch (error) {
    console.error('Error fetching saved deals:', error);
    toast.error('Failed to load saved deals');
    return {
      deals: [],
      totalCount: 0,
      page: page,
      pageSize: pageSize,
      totalPages: 1,
    };
  }
}

// --- Admin Functions ---

export async function getAdminDeals(filters: FilterParams): Promise<DealListResponse> {
  try {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await api.get<ApiResponse<DealListResponse>>(`/admin/deals?${queryParams.toString()}`);
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data?.error || 'Failed to fetch admin deals');
  } catch (error) {
    console.error('Error fetching admin deals:', error);
    return {
      deals: [],
      totalCount: 0,
      page: (filters as any).page || 1, // Attempt to use page from filters, default to 1
      pageSize: (filters as any).pageSize || 30, // Attempt to use pageSize from filters, default to 30
      totalPages: 1, // Default to 1 page on error
    };
  }
}

// --- Media Handling ---

export async function uploadDealImage(file: File): Promise<{imageUrl: string, thumbnailUrl: string}> {
  try {
    const formData = new FormData();
    formData.append('image', file);
    
    const response = await api.post<ApiResponse<{imageUrl: string, thumbnailUrl: string}>>(
      '/deals/upload-image',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data?.error || 'Failed to upload image and get URLs');
  } catch (error) {
    console.error('Error uploading image:', error);
    toast.error('Failed to upload image');
    throw error;
  }
}

// --- Related Deals ---

export async function getRelatedDeals(
  categoryId: number,
  currentDealId: number,
  limit: number = 4
): Promise<Deal[]> {
  try {
    const response = await api.get<ApiResponse<Deal[]>>(
      `/deals/related?categoryId=${categoryId}&excludeId=${currentDealId}&limit=${limit}`
    );
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching related deals:', error);
    return [];
  }
}

// --- Deal Activation (AI) ---

export async function activatePendingDeal(dealId: number): Promise<Deal> {
  try {
    const response = await api.post<ApiResponse<Deal>>(`/admin/deals/${dealId}/activate`);
    if (response.data && response.data.success && response.data.data) {
      toast.success('Deal activated successfully');
      return response.data.data;
    }
    throw new Error(response.data?.error || `Failed to activate deal ${dealId}: Invalid server response`);
  } catch (error) {
    console.error(`Error activating deal ${dealId}:`, error);
    toast.error('Failed to activate deal');
    throw error;
  }
}

// Export all functions as a service object for backward compatibility
export const dealService = {
  // Core functions
  getDeals,
  getDealById,
  createDeal,
  updateDeal,
  deleteDeal,
  
  // Saved deals
  saveDeal,
  unsaveDeal,
  checkIfDealSaved,
  getSavedDeals,
  fetchSavedDealIds,
  
  // Voting
  upvoteDeal,
  downvoteDeal,
  removeVote,
  
  // Specialized queries
  getTrendingDeals,
  getNewestDeals,
  getGettingWarmDeals,
  getMostCommentedDeals,
  getRelatedDeals,
  
  // User-specific
  getUserDeals,
  getCurrentUserDeals,
  
  // Admin
  getAdminDeals,
  activatePendingDeal,
  
  // Media
  uploadDealImage
};
