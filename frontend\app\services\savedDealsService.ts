import api from './api';

const SAVED_DEALS_KEY = 'savedDeals';

interface SavedDealsCache {
  userId: number;
  dealIds: number[];
  lastUpdated: number;
}

class SavedDealsService {
  private cache: SavedDealsCache | null = null;

  constructor() {
    this.loadFromLocalStorage();
  }

  private loadFromLocalStorage() {
    if (typeof window !== 'undefined') {
      const cached = localStorage.getItem(SAVED_DEALS_KEY);
      if (cached) {
        try {
          this.cache = JSON.parse(cached);
        } catch (error) {
          console.error("Error parsing saved deals from localStorage:", error);
          this.cache = null; // Reset cache if parsing fails
          localStorage.removeItem(SAVED_DEALS_KEY); // Clean up invalid entry
        }
      } else {
        this.cache = null; // Ensure cache is null if nothing in localStorage
      }
    } else {
      // If on server, ensure cache is null
      this.cache = null;
    }
  }

  private saveToLocalStorage() {
    if (typeof window !== 'undefined') {
      if (this.cache) {
        localStorage.setItem(SAVED_DEALS_KEY, JSON.stringify(this.cache));
      } else {
        localStorage.removeItem(SAVED_DEALS_KEY);
      }
    }
  }

  async initializeCache(userId: number) {
    try {
      // Fetch all saved deals for the user
      const response = await api.get('/users/me/saved-deals');
      const dealIds = response.data.map((deal: any) => deal.id);
      
      this.cache = {
        userId,
        dealIds,
        lastUpdated: Date.now()
      };
      
      this.saveToLocalStorage();
      return dealIds;
    } catch (error) {
      console.error('Error initializing saved deals cache:', error);
      return [];
    }
  }

  async checkIfDealSaved(dealId: number, userId: number): Promise<boolean> {
    // If no cache or different user, initialize it
    if (!this.cache || this.cache.userId !== userId) {
      await this.initializeCache(userId);
    }

    return this.cache?.dealIds.includes(dealId) || false;
  }

  async saveDeal(dealId: number, userId: number): Promise<void> {
    try {
      await api.post(`/deals/${dealId}/save`);
      
      // Update cache
      if (!this.cache || this.cache.userId !== userId) {
        await this.initializeCache(userId);
      } else {
        // Only add the dealId if it's not already in the array
        if (!this.cache.dealIds.includes(dealId)) {
          this.cache.dealIds.push(dealId);
          this.cache.lastUpdated = Date.now();
          this.saveToLocalStorage();
        }
      }
    } catch (error) {
      console.error('Error saving deal:', error);
      throw error;
    }
  }

  async unsaveDeal(dealId: number, userId: number): Promise<void> {
    try {
      await api.delete(`/deals/${dealId}/save`);
      
      // Update cache
      if (!this.cache || this.cache.userId !== userId) {
        await this.initializeCache(userId);
      } else {
        this.cache.dealIds = this.cache.dealIds.filter(id => id !== dealId);
        this.cache.lastUpdated = Date.now();
        this.saveToLocalStorage();
      }
    } catch (error) {
      console.error('Error unsaving deal:', error);
      throw error;
    }
  }

  clearCache() {
    this.cache = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem(SAVED_DEALS_KEY);
    }
  }
}

export const savedDealsService = new SavedDealsService(); 