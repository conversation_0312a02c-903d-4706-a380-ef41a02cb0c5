import { Store } from '../types';
import api from './api';

/**
 * Get all stores
 * @param category Optional category ID to filter store counts
 * @returns Array of stores
 */
export const getStores = async (category?: number): Promise<Store[]> => {
  try {
    const params = category ? { category } : undefined;
    const response = await api.get('/stores', { params });
    return response.data;
  } catch (error) {
    console.error('Error in getStores:', error);
    throw error;
  }
};

/**
 * Get a store by ID
 * @param id Store ID
 * @returns Store details
 */
export const getStoreById = async (id: number): Promise<Store> => {
  try {
    const response = await api.get(`/stores/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error in getStoreById:', error);
    throw error;
  }
};

/**
 * Create a new store
 * @param storeData Store data
 * @returns Created store
 */
export const createStore = async (storeData: { name: string; url?: string; logoUrl?: string }): Promise<Store> => {
  try {
    const response = await api.post('/stores', storeData);
    return response.data;
  } catch (error) {
    console.error('Error in createStore:', error);
    throw error;
  }
};

/**
 * Update a store
 * @param id Store ID
 * @param storeData Store data to update
 * @returns Updated store
 */
export const updateStore = async (id: number, storeData: Partial<Store>): Promise<Store> => {
  try {
    const response = await api.put(`/stores/${id}`, storeData);
    return response.data;
  } catch (error) {
    console.error('Error in updateStore:', error);
    throw error;
  }
};

/**
 * Delete a store
 * @param id Store ID
 * @returns Success message
 */
export const deleteStore = async (id: number): Promise<{ message: string }> => {
  try {
    const response = await api.delete(`/stores/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error in deleteStore:', error);
    throw error;
  }
};

/**
 * Upload a store logo
 * @param id Store ID
 * @param logoFile Logo file
 * @returns Updated logo URL
 */
export const uploadStoreLogo = async (id: number, logoFile: File): Promise<{ logoUrl: string }> => {
  try {
    const formData = new FormData();
    formData.append('logo', logoFile);

    const response = await api.post(`/stores/${id}/upload-logo`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error in uploadStoreLogo:', error);
    throw error;
  }
};

// Export the functions as a service object
export const storeService = {
  getStores,
  getStoreById,
  createStore,
  updateStore,
  deleteStore,
  uploadStoreLogo
};
