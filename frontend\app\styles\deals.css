/* Deal Card Styles */
.deal-card {
  @apply bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100 transition-all duration-300;
}

.card-hover {
  @apply hover:shadow-md hover:border-gray-200 hover:translate-y-[-2px];
}

.deal-title {
  @apply font-semibold text-gray-900 line-clamp-2 hover:text-deal-orange transition-colors duration-150;
}

.deal-price {
  @apply text-xl font-bold text-deal-orange;
}

.deal-original-price {
  @apply text-sm text-gray-500 line-through;
}

.deal-badge {
  @apply text-xs font-bold px-2 py-1 rounded-xl;
}

.deal-badge-primary {
  @apply bg-deal-orange text-white;
}

.deal-badge-secondary {
  @apply bg-deal-blue text-white;
}

/* Animation */
@keyframes subtlePulse {
  0% {
    opacity: 0.9;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.9;
  }
}

.animate-pulse-subtle {
  animation: subtlePulse 2s infinite;
}

@keyframes subtleBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-bounce-subtle {
  animation: subtleBounce 2s infinite;
}

/* Color variables (meant to be used with Tailwind's theme extension) */
:root {
  --color-deal-orange: #ff6b35;
  --color-deal-orange-light: #ff8a5f;
  --color-deal-orange-dark: #e55025;
  --color-deal-blue: #2ec4b6;
  --color-deal-blue-light: #3fded0;
  --color-deal-blue-dark: #1fa89c;
}
