// Admin-specific types for the SSR application

export interface DashboardStats {
  totalUsers: number;
  totalDeals: number;
  activeDeals: number;
  pendingDeals: number;
  newUsersToday: number;
  newDealsToday: number;
  userGrowth: number;
  dealGrowth: number;
}

export interface RecentActivity {
  id: number;
  type: 'user' | 'deal';
  action: string;
  title: string;
  timestamp: string;
  user?: {
    id: number;
    username: string;
  };
}

export interface AdminCategory {
  id: number;
  name: string;
  description?: string;
  slug: string;
  dealsCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface AdminStore {
  id: number;
  name: string;
  description?: string;
  slug: string;
  websiteUrl?: string;
  logo?: string;
  logoUrl?: string;
  dealsCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface AdminDeal {
  id: number;
  title: string;
  description?: string;
  price?: number;
  originalPrice?: number;
  url?: string;
  imageUrl?: string;
  status: 'active' | 'expired' | 'pending' | 'deleted';
  categoryId?: number;
  categoryName?: string;
  storeId?: number;
  storeName?: string;
  userId?: number;
  username?: string;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
  temperature: number;
  commentCount: number;
}

export interface AdminUser {
  id: number;
  username: string;
  email: string;
  role: 'user' | 'admin' | 'moderator';
  status: 'active' | 'banned' | 'unverified';
  dealsCount: number;
  commentsCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface AdminScraper {
  id: number;
  name: string;
  description: string;
  source: string;
  status: 'active' | 'inactive';
  lastRun?: string;
  schedule?: string;
  dealsAdded: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
