'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import UserCommentsClient from './UserCommentsClient';
import { getUserComments } from '@/services/commentService';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import Alert from '@/components/common/AlertModal';

// Define the UserCommentResponse interface from commentService
interface UserCommentResponse {
  data: Array<{
    id: number;
    text: string;
    dealId: number;
    userId: number;
    parentId?: number;
    createdAt: string;
    dealTitle: string;
    price?: number;
    originalPrice?: number;
    imageUrl?: string;
    thumbnailUrl?: string;
    dealOwnerUsername?: string;
    categoryName?: string;
    dealOwnerId?: number;
    categoryId?: number;
    commenterUsername?: string;
    status?: string;
  }>;
  pagination: {
    total: number;
    totalPages: number;
    page: number;
    pageSize: number;
  };
}

export default function UserCommentsPage() {
  const router = useRouter();
  const { isAuthenticated, loading } = useAuth();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 20;
  
  const [pageData, setPageData] = useState<{
    commentsData: UserCommentResponse | null;
    loading: boolean;
    error: string | null;
  }>({
    commentsData: null,
    loading: true,
    error: null
  });
  
  // Check authentication and fetch data
  useEffect(() => {
    if (loading) return; // Wait for auth check
    
    if (!isAuthenticated) {
      router.replace('/login');
      return;
    }
    
    const fetchUserComments = async () => {
      try {
        const data = await getUserComments(currentPage, pageSize);
        console.log('Received comments data:', data);
        
        setPageData({
          commentsData: data,
          loading: false,
          error: null
        });
      } catch (err) {
        console.error('Error fetching user comments:', err);
        setPageData({
          commentsData: null,
          loading: false,
          error: 'Failed to load your comments'
        });
      }
    };
    
    fetchUserComments();
  }, [isAuthenticated, loading, router, currentPage, pageSize]);
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  // Show loading state while checking auth or fetching data
  if (loading || pageData.loading) {
    return <LoadingSpinner />;
  }
  
  // Show error state
  if (pageData.error) {
    return (
      <Alert variant="destructive">
        {pageData.error}
      </Alert>
    );
  }
  
  // Render the client component with comments data
  return (
    <UserCommentsClient 
      commentsData={pageData.commentsData} 
      currentPage={currentPage}
      onPageChange={handlePageChange}
    />
  );
}
