'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { Deal, DealListResponse } from '@/types';
import UserDealsClient from './UserDealsClient';
import { dealService } from '@/services/dealService';
import LoadingSpinner from '@/components/common/LoadingSpinner';

export default function UserDealsPage() {
  const router = useRouter();
  const { isAuthenticated, loading, user } = useAuth();
  const [pageData, setPageData] = useState<{
    deals: Deal[];
    loading: boolean;
    error: string | null;
  }>({
    deals: [],
    loading: true,
    error: null
  });
  
  // Check authentication and fetch data
  useEffect(() => {
    if (loading) return; // Wait for auth check
    
    if (!isAuthenticated) {
      router.replace('/login');
      return;
    }
    
    const fetchUserDeals = async () => {
      try {
        const response: DealListResponse = await dealService.getMySubmittedDeals();
        console.log('Fetched submitted deals response:', response);
        
        const dealsToSet = response.deals || [];

        console.log('Deals to set:', dealsToSet.map((deal: Deal) => ({
          dealId: deal.id,
          userId: deal.userId,
          currentUserId: user?.id,
          thumbnailUrl: deal.thumbnailUrl,
          imageUrl: deal.imageUrl,
          status: deal.status,
        })));
        
        setPageData({
          deals: dealsToSet,
          loading: false,
          error: null
        });
      } catch (err) {
        console.error('Error fetching user deals:', err);
        setPageData({
          deals: [],
          loading: false,
          error: 'Failed to load your deals. Please try again later.'
        });
      }
    };
    
    fetchUserDeals();
  }, [isAuthenticated, loading, router, user]);
  
  // Show loading state while checking auth or fetching data
  if (loading || pageData.loading) {
    return (
      <div className="container mx-auto mt-8 px-4">
        <div className="flex justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }
  
  // Show error state
  if (pageData.error) {
    return (
      <div className="container mx-auto mt-8 max-w-4xl px-4">
        <div className="rounded-lg bg-red-50 p-4">
          <h2 className="text-lg font-semibold text-red-800">Error</h2>
          <p className="text-red-700">{pageData.error}</p>
        </div>
      </div>
    );
  }
  
  // Render the client component with deals data
  return <UserDealsClient deals={pageData.deals} />;
}
