'use client';

import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import toast from 'react-hot-toast';
import { UserCircleIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/hooks/useAuth';
import { getUserProfile, updateUserProfile } from '@/services/userService';
import { UpdateProfileFormValues } from '@/types';
import AvatarUpload from 'src/components/profile/AvatarUpload';

// Define tab types
type TabType = 0 | 1 | 2;

// Validation schema for profile update
const profileUpdateSchema = Yup.object().shape({
  username: Yup.string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be at most 20 characters')
    .matches(
      /^[a-zA-Z0-9_-]+$/,
      'Username can only contain letters, numbers, underscores, and hyphens'
    )
    .required('Username is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  currentPassword: Yup.string()
    .test('password-match', 'Current password is required when changing password', 
      function(value) {
        const { newPassword } = this.parent;
        return !newPassword || (!!newPassword && !!value);
      }),
  newPassword: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\\]{8,}$/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and can include special characters (except backslash)'
    ),
  confirmNewPassword: Yup.string()
    .test('passwords-match', 'Passwords must match', function(value) {
      const { newPassword } = this.parent;
      return !newPassword || value === newPassword;
    }),
  about: Yup.string().max(500, 'Bio must be at most 500 characters'),
});

export default function UserProfileClient() {
  const { user, updateUser } = useAuth();
  const [tabIndex, setTabIndex] = useState<TabType>(0);
  const queryClient = useQueryClient();

  // Fetch user profile data
  const { data: profile, isLoading: profileLoading } = useQuery({
    queryKey: ['userProfile', user?.id],
    queryFn: () => user?.id ? getUserProfile(user.id) : undefined,
    enabled: !!user?.id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: (values: UpdateProfileFormValues) => updateUserProfile(user!.id, values),
    onSuccess: (data) => {
      toast.success('Profile updated successfully');
      queryClient.invalidateQueries({ queryKey: ['userProfile', user?.id] });
      updateUser(data);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update profile');
    },
  });

  // Handle form submission
  const handleSubmit = (values: UpdateProfileFormValues) => {
    updateProfileMutation.mutate(values);
  };

  if (profileLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-t-2 border-b-2 border-deal-orange"></div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl py-8 px-4 sm:px-6 lg:px-8">
      <div className="bg-white shadow sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="mb-6 border-b border-gray-200">
            <div className="space-x-8">
              <button
                type="button"
                className={`pb-4 text-sm font-medium ${
                  tabIndex === (0 as TabType) ? 'border-b-2 border-deal-orange text-deal-orange' : 'text-gray-500'
                }`}
                onClick={() => setTabIndex(0 as TabType)}
              >
                Profile
              </button>
              <button
                type="button"
                className={`pb-4 text-sm font-medium ${
                  tabIndex === (1 as TabType) ? 'border-b-2 border-deal-orange text-deal-orange' : 'text-gray-500'
                }`}
                onClick={() => setTabIndex(1 as TabType)}
              >
                My Deals
              </button>
              <button
                type="button"
                className={`pb-4 text-sm font-medium ${
                  tabIndex === (2 as TabType) ? 'border-b-2 border-deal-orange text-deal-orange' : 'text-gray-500'
                }`}
                onClick={() => setTabIndex(2 as TabType)}
              >
                Activity
              </button>
            </div>
          </div>
          
          {user && (
            <div>
              {tabIndex === (0 as TabType) && (
                <div>
                  <h3 className="text-lg font-medium leading-6 text-gray-900">Profile Information</h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Update your account information and preferences.
                  </p>
                  <div className="mt-5 border-t border-gray-200 pt-6">
                    <Formik
                      initialValues={{
                        username: profile?.username || user.username || '',
                        email: profile?.email || user.email || '',
                        currentPassword: '',
                        newPassword: '',
                        confirmNewPassword: '',
                        about: ''
                      }}
                      validationSchema={profileUpdateSchema}
                      onSubmit={handleSubmit}
                      enableReinitialize
                    >
                      {({ errors, touched, isSubmitting }) => (
                        <Form className="space-y-6">
                          <div>
                            <div className="flex items-center">
                              <AvatarUpload
                                currentAvatar={user.avatar}
                                size="lg"
                              />
                              <div className="ml-6">
                                <h3 className="text-lg font-medium text-gray-900">{user.username}</h3>
                                <p className="text-sm text-gray-500">
                                  Member since {new Date(user.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                            <div className="sm:col-span-3">
                              <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                                Username
                              </label>
                              <div className="mt-1">
                                <Field
                                  type="text"
                                  name="username"
                                  id="username"
                                  autoComplete="username"
                                  className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-deal-orange focus:ring-deal-orange sm:text-sm ${
                                    touched.username && errors.username ? 'border-red-500' : ''
                                  }`}
                                />
                              </div>
                              <ErrorMessage name="username" component="p" className="mt-2 text-sm text-red-600" />
                            </div>

                            <div className="sm:col-span-3">
                              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                                Email address
                              </label>
                              <div className="mt-1">
                                <Field
                                  type="email"
                                  name="email"
                                  id="email"
                                  autoComplete="email"
                                  className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-deal-orange focus:ring-deal-orange sm:text-sm ${
                                    touched.email && errors.email ? 'border-red-500' : ''
                                  }`}
                                />
                              </div>
                              <ErrorMessage name="email" component="p" className="mt-2 text-sm text-red-600" />
                            </div>

                            <div className="sm:col-span-6">
                              <label htmlFor="about" className="block text-sm font-medium text-gray-700">
                                Bio
                              </label>
                              <div className="mt-1">
                                <Field
                                  as="textarea"
                                  id="about"
                                  name="about"
                                  rows={3}
                                  className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-deal-orange focus:ring-deal-orange sm:text-sm ${
                                    touched.about && errors.about ? 'border-red-500' : ''
                                  }`}
                                  placeholder="Tell us a bit about yourself..."
                                />
                              </div>
                              <ErrorMessage name="about" component="p" className="mt-2 text-sm text-red-600" />
                              <p className="mt-2 text-sm text-gray-500">
                                Brief description for your profile. URLs are hyperlinked.
                              </p>
                            </div>

                            <div className="sm:col-span-6">
                              <div className="relative">
                                <div className="absolute inset-0 flex items-center" aria-hidden="true">
                                  <div className="w-full border-t border-gray-300" />
                                </div>
                                <div className="relative flex justify-center">
                                  <span className="bg-white px-2 text-sm text-gray-500">Change Password</span>
                                </div>
                              </div>
                            </div>

                            <div className="sm:col-span-3">
                              <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700">
                                Current Password
                              </label>
                              <div className="mt-1">
                                <Field
                                  type="password"
                                  name="currentPassword"
                                  id="currentPassword"
                                  autoComplete="current-password"
                                  className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-deal-orange focus:ring-deal-orange sm:text-sm ${
                                    touched.currentPassword && errors.currentPassword ? 'border-red-500' : ''
                                  }`}
                                />
                              </div>
                              <ErrorMessage name="currentPassword" component="p" className="mt-2 text-sm text-red-600" />
                            </div>

                            <div className="sm:col-span-3">
                              <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700">
                                New Password
                              </label>
                              <div className="mt-1">
                                <Field
                                  type="password"
                                  name="newPassword"
                                  id="newPassword"
                                  autoComplete="new-password"
                                  className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-deal-orange focus:ring-deal-orange sm:text-sm ${
                                    touched.newPassword && errors.newPassword ? 'border-red-500' : ''
                                  }`}
                                />
                              </div>
                              <ErrorMessage name="newPassword" component="p" className="mt-2 text-sm text-red-600" />
                            </div>

                            <div className="sm:col-span-3">
                              <label htmlFor="confirmNewPassword" className="block text-sm font-medium text-gray-700">
                                Confirm New Password
                              </label>
                              <div className="mt-1">
                                <Field
                                  type="password"
                                  name="confirmNewPassword"
                                  id="confirmNewPassword"
                                  autoComplete="new-password"
                                  className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-deal-orange focus:ring-deal-orange sm:text-sm ${
                                    touched.confirmNewPassword && errors.confirmNewPassword ? 'border-red-500' : ''
                                  }`}
                                />
                              </div>
                              <ErrorMessage
                                name="confirmNewPassword"
                                component="p"
                                className="mt-2 text-sm text-red-600"
                              />
                            </div>
                          </div>

                          <div className="pt-5">
                            <div className="flex justify-end">
                              <button
                                type="button"
                                className="rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-deal-orange focus:ring-offset-2"
                              >
                                Cancel
                              </button>
                              <button
                                type="submit"
                                className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-deal-orange py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-deal-orange-dark focus:outline-none focus:ring-2 focus:ring-deal-orange focus:ring-offset-2"
                                disabled={isSubmitting}
                              >
                                {isSubmitting ? 'Saving...' : 'Save'}
                              </button>
                            </div>
                          </div>
                        </Form>
                      )}
                    </Formik>
                  </div>
                </div>
              )}
              
              {tabIndex === (1 as TabType) && user && (
                <div>
                  <h3 className="text-lg font-medium leading-6 text-gray-900">My Deals</h3>
                  <div className="mt-4">
                    <p className="text-sm text-gray-500">
                      <a href="/user/deals" className="text-deal-orange hover:text-deal-orange-dark">
                        View all your deals
                      </a>
                    </p>
                  </div>
                </div>
              )}
              
              {tabIndex === (2 as TabType) && user && (
                <div>
                  <h3 className="text-lg font-medium leading-6 text-gray-900">Activity</h3>
                  <div className="mt-4">
                    <div className="flex flex-col space-y-4">
                      <p className="text-sm text-gray-500">
                        <a href="/user/saved-deals" className="text-deal-orange hover:text-deal-orange-dark">
                          View your saved deals
                        </a>
                      </p>
                      <p className="text-sm text-gray-500">
                        <a href="/user/comments" className="text-deal-orange hover:text-deal-orange-dark">
                          View your comments
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
