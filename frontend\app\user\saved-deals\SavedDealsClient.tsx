'use client';

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { BookmarkIcon } from '@heroicons/react/24/outline';
import DealCard from '@/components/deals/DealCard';
import Pagination from '@/components/common/Pagination';
import { DealListResponse } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { getSavedDeals } from '@/services/dealService';

interface SavedDealsClientProps {
  initialDeals: DealListResponse;
  initialPage: number;
  pageSize: number;
}

export default function SavedDealsClient({ initialDeals, initialPage, pageSize }: SavedDealsClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentPage = searchParams.get('page') ? parseInt(searchParams.get('page')!) : initialPage;

  // Client-side data fetching to keep data fresh and enable client-side pagination
  const {
    data: dealsResponse,
    isLoading,
    isError,
    error
  } = useQuery({
    queryKey: ['savedDeals', currentPage],
    queryFn: () => getSavedDeals(currentPage, pageSize),
    initialData: initialDeals,
    staleTime: 30000, // Consider data fresh for 30 seconds
  });

  const handlePageChange = (page: number) => {
    // Update URL with new page number
    const params = new URLSearchParams(searchParams);
    params.set('page', page.toString());
    router.push(`/user/saved-deals?${params.toString()}`);
  };

  if (isLoading && !dealsResponse) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-deal-orange"></div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700">Error loading saved deals: {(error as Error).message}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Your Saved Deals</h1>
            <p className="text-sm text-gray-600">Deals you've bookmarked for later</p>
          </div>
        </div>

        {(!dealsResponse?.deals || dealsResponse.deals.length === 0) ? (
          <div className="bg-white rounded-lg shadow-sm p-6 text-center">
            <BookmarkIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No saved deals</h3>
            <p className="mt-1 text-sm text-gray-500">
              You haven't saved any deals yet. Browse deals and click the save button to add them here.
            </p>
            <div className="mt-6">
              <Link
                href="/dealsBrowse"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-deal-orange hover:bg-deal-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-deal-orange"
              >
                Browsex Deals
              </Link>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {dealsResponse.deals.map((deal) => (
                <DealCard key={deal.id} deal={deal} />
              ))}
            </div>

            {dealsResponse.totalPages > 1 && (
              <div className="mt-8">
                <Pagination
                  currentPage={currentPage}
                  totalPages={dealsResponse.totalPages || 1}
                  onPageChange={handlePageChange}
                  totalItems={dealsResponse.totalCount}
                  pageSize={pageSize}
                  itemName="Deals"
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
