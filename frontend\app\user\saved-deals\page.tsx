'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { DealListResponse } from '@/types';
import SavedDealsClient from './SavedDealsClient';
import { getSavedDeals } from '@/services/dealService';

export default function SavedDealsPage() {
  const router = useRouter();
  const { isAuthenticated, loading } = useAuth();
  const [pageData, setPageData] = useState<{
    initialDeals: DealListResponse | null;
    loading: boolean;
    error: string | null;
  }>({
    initialDeals: null,
    loading: true,
    error: null
  });
  
  // Check authentication and fetch data
  useEffect(() => {
    if (loading) return; // Wait for auth check
    
    if (!isAuthenticated) {
      router.replace('/login');
      return;
    }
    
    const fetchData = async () => {
      try {
        const dealsResponse = await getSavedDeals(1, 30);
        setPageData({
          initialDeals: dealsResponse,
          loading: false,
          error: null
        });
      } catch (error) {
        setPageData({
          initialDeals: null,
          loading: false,
          error: 'Failed to load saved deals'
        });
      }
    };
    
    fetchData();
  }, [isAuthenticated, loading, router]);
  
  // Show loading state while checking auth or fetching data
  if (loading || pageData.loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </div>
    );
  }
  
  // Show error state
  if (pageData.error) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700">Error loading saved deals: {pageData.error}</p>
          </div>
        </div>
      </div>
    );
  }
  
  // Render the client component with initial data
  return (
    <SavedDealsClient 
      initialDeals={pageData.initialDeals || {
        deals: [],
        totalCount: 0,
        page: 1,
        pageSize: 30,
        totalPages: 0
      }} 
      initialPage={1} 
      pageSize={30} 
    />
  );
}
