import { Deal } from '@/types';
import { format, formatDistanceToNow } from 'date-fns';

// Format a date as relative time (e.g., "2 hours ago", "3 days ago")
export const formatRelativeTime = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

// Format a price with the proper currency symbol
export const formatPrice = (price?: number | null, currency: string = '£'): string => {
  if (price === undefined || price === null) return '';
  return `${currency}${price.toFixed(2)}`;
};

// Format a discount percentage
export const formatDiscount = (originalPrice?: number, salePrice?: number): string => {
  if (!originalPrice || !salePrice || originalPrice <= salePrice) return '';
  const discountPercentage = Math.round(((originalPrice - salePrice) / originalPrice) * 100);
  return `${discountPercentage}%`;
};

// Get an affiliate link for a deal, with tracking parameters if needed
export const getAffiliateUrl = (deal: Deal): string => {
  let url: string | undefined;
  
  // If the deal has an affiliate URL, use that
  if ('affiliateUrl' in deal && deal.affiliateUrl) {
    url = deal.affiliateUrl as string;
  }
  // If the deal has a url property, use that
  else if (deal.url) {
    url = deal.url;
  }
  // If the deal has a dealUrl, use that
  else if (deal.dealUrl) {
    url = deal.dealUrl;
  }
  // Fallback to the deal detail page
  else {
    return `/dealDetail/${deal.id}`;
  }
  
  try {
    if (!url) return `/dealDetail/${deal.id}`;
    
    // Check if this is an Amazon deal
    const storeId = deal.storeId;
    // Check for Amazon using either storeId or storeName
    if (storeId === 42 || (deal.storeName?.toLowerCase().includes('amazon'))) {
      try {
        // Parse the URL
        const urlObj = new URL(url);
        
        // Check if it's already an Amazon URL to be safe
        if (urlObj.hostname.includes('amazon') || urlObj.hostname.includes('amzn')) {
          // Add the affiliate tag
          urlObj.searchParams.set('tag', 'bargn0c-21');
          return urlObj.toString();
        }
      } catch (e) {
        console.error('Error parsing Amazon URL:', e);
      }
    }
    
    // Check if this is an eBay deal
    // Check for eBay using either storeId or storeName
    if (storeId === 43 || (deal.storeName?.toLowerCase().includes('ebay'))) {
      try {
        // Parse the URL
        const urlObj = new URL(url);
        
        // Check if it's an eBay URL to be safe
        if (urlObj.hostname.includes('ebay')) {
          // Add eBay affiliate parameters for UK
          urlObj.searchParams.set('mkevt', '1');
          urlObj.searchParams.set('mkcid', '1');
          urlObj.searchParams.set('mkrid', '710-53481-19255-0');
          urlObj.searchParams.set('campid', '5338762847');
          return urlObj.toString();
        }
      } catch (e) {
        console.error('Error parsing eBay URL:', e);
      }
    }
    
    // Return the original URL for non-affiliate stores
    return url;
  } catch (error) {
    console.error('Error generating affiliate URL:', error);
    return url || ''; // Return the original URL if there's an error
  }
};

/**
 * Format a date string to a more readable format
 * @param dateString ISO date string
 * @param options Formatting options
 * @returns Formatted date string
 */
export const formatDate = (
  dateString: string | undefined | null, 
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }
): string => {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid Date';
    }
    return new Intl.DateTimeFormat('en-GB', options).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString; // Return original if error
  }
};
