import { Deal } from '../types';

// Default image path
const DEFAULT_IMAGE = '/images/placeholder.jpg';
// Base API URL for uploads
const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL?.replace('/api', '') || 'http://localhost:5010';

// Placeholder image used for pending deals and to replace remote images
export const PLACEHOLDER_IMAGE = '/placeholder-image.png';
const API_BASE_URL_FROM_ENV = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5010/api';
const BASE_URL = API_BASE_URL_FROM_ENV.endsWith('/api') 
  ? API_BASE_URL_FROM_ENV.substring(0, API_BASE_URL_FROM_ENV.length - 4) 
  : API_BASE_URL_FROM_ENV;

// Get the full image URL for a deal
export const getFullImageUrl = (deal: Deal): string => {
  if (!deal.imageUrl) {
    return DEFAULT_IMAGE;
  }
  
  // NEVER show external images anywhere on the site - use PLACEHOLDER_IMAGE instead
  if (deal.imageUrl.startsWith('http')) {
    return PLACEHOLDER_IMAGE;
  }
  
  // Prevent infinite recursion in image loading failures
  if (deal.imageUrl === DEFAULT_IMAGE || deal.imageUrl === PLACEHOLDER_IMAGE) {
    return deal.imageUrl;
  }
  
  // If the server provides a full path with /uploads already in it, use it directly
  if (deal.imageUrl.includes('/uploads/deals/')) {
    return `${BASE_URL}${deal.imageUrl}`;
  }
  
  // Standard path construction
  return `${BACKEND_URL}/uploads/deals/${deal.imageUrl}`;
};

// Function to get the thumbnail URL from a full image URL
export const getThumbnailUrl = (imageUrl: string): string => {
  if (!imageUrl) return DEFAULT_IMAGE;
  
  // NEVER show external images anywhere on the site - use PLACEHOLDER_IMAGE instead
  if (isExternalUrl(imageUrl)) return PLACEHOLDER_IMAGE;
  
  // For uploads, construct the thumbnail URL with backend URL
  if (imageUrl.startsWith('/uploads/')) {
    const thumbnailPath = imageUrl.replace('/uploads/deals/', '/uploads/deals/thumbnails/');
    return `${BACKEND_URL}${thumbnailPath}`;
  }
  
  // Special case for our placeholder images
  if (imageUrl === DEFAULT_IMAGE || imageUrl === PLACEHOLDER_IMAGE) {
    return imageUrl;
  }
  
  // For non-upload paths, just return as is
  return imageUrl;
};

// Handle image loading errors
export const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>, fallbackImage: string = DEFAULT_IMAGE) => {
  const target = e.target as HTMLImageElement;
  
  // Only replace if not already the default image
  if (target.src !== fallbackImage && !target.src.endsWith(fallbackImage)) {
    target.src = fallbackImage;
  }
  
  // Remove onerror to prevent potential infinite loops
  target.onerror = null;
};

// Get Deal image with additional options
export interface GetDealImageOptions {
  currentUserId?: number;
  useThumbnail?: boolean;
  forceOwner?: boolean;
}

// Function to get the appropriate deal image URL
export const getDealImage = (deal: Deal, options?: GetDealImageOptions): string => {
  const { useThumbnail = false } = options || {};
  
  // Check if the image is external
  if (deal.imageUrl && isExternalUrl(deal.imageUrl)) {
    return PLACEHOLDER_IMAGE; // NEVER show external images anywhere
  }
  
  // Need to pass deal.imageUrl (string) to getThumbnailUrl, not the whole deal object
  return useThumbnail ? getThumbnailUrl(deal.imageUrl || '') : getFullImageUrl(deal);
};

// Check if URL is external (starts with http/https)
export const isExternalUrl = (url: string | null | undefined): boolean => {
  // If url is falsy or not a string, it's not external
  if (!url || typeof url !== 'string') {
    return false;
  }
  
  // Simple check - if it starts with /uploads/ it's local, otherwise if it starts with http it's external
  const isLocal = url.startsWith('/uploads/');
  const isExternal = !isLocal && (url.startsWith('http://') || url.startsWith('https://'));
  return isExternal;
};

// Convert a path to a full URL
export const getImageUrl = (path: string | null | undefined): string => {
  if (!path) return PLACEHOLDER_IMAGE;
  
  // NEVER show external images anywhere on the site - use PLACEHOLDER_IMAGE instead
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return PLACEHOLDER_IMAGE;
  }
  
  // Special case for our placeholder images
  if (path === DEFAULT_IMAGE || path === PLACEHOLDER_IMAGE) {
    return path;
  }
  
  // Make sure path starts with a slash if not already
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  
  // Append path to base URL - use BACKEND_URL for consistency
  return `${BACKEND_URL}${normalizedPath}`;
};

// Function specifically for admin routes
export const getAdminImageUrl = (path: string | { imageUrl?: string }): string => {
  // Handle object with imageUrl property
  const imagePath = typeof path === 'string' ? path : path?.imageUrl;
  
  // NEVER show external images even in admin - use PLACEHOLDER_IMAGE instead
  if (imagePath && isExternalUrl(imagePath)) {
    return PLACEHOLDER_IMAGE;
  }
  
  return getImageUrl(imagePath || '');
};
