/**
 * URL utility functions for SSR-friendly navigation in Next.js App Router
 * These helpers ensure consistent URL creation for proper server rendering
 */

import { DealFilters } from '../types';

/**
 * Creates an SSR-friendly URL for dealsBrowse page with consistent parameter handling
 * @param pathname The base pathname (e.g., "/dealsBrowse")
 * @param params Filter parameters to add to the URL
 * @returns A properly formatted URL string with query parameters
 */
export function createDealsBrowseUrl(
  pathname: string = '/dealsBrowse',
  params: Partial<DealFilters> = {}
): string {
  const searchParams = new URLSearchParams();
  
  // Add parameters to URL
  if (params.search) searchParams.set('search', params.search);
  if (params.category) searchParams.set('category', params.category.toString());
  if (params.store) searchParams.set('store', params.store.toString());
  
  // Always include status and sort parameters for consistent SSR navigation
  searchParams.set('status', params.status || 'active');
  searchParams.set('sort', params.sort || 'newest');
  
  if (params.page && params.page !== 1) searchParams.set('page', params.page.toString());
  if (params.pageSize && params.pageSize !== 30) searchParams.set('pageSize', params.pageSize.toString());
  if (params.minPrice !== undefined) searchParams.set('minPrice', params.minPrice.toString());
  if (params.maxPrice !== undefined) searchParams.set('maxPrice', params.maxPrice.toString());
  if (params.dealType) searchParams.set('dealType', params.dealType);
  
  return `${pathname}?${searchParams.toString()}`;
}

/**
 * Determines if a navigation change is "major" (requires full SSR navigation)
 * or "minor" (can use client-side updates)
 * 
 * @param prevParams Previous filter parameters
 * @param newParams New filter parameters
 * @returns boolean indicating if this is a major change requiring SSR
 */
export function isMajorNavigationChange(
  prevParams?: Partial<DealFilters>,
  newParams?: Partial<DealFilters>
): boolean {
  if (!prevParams || !newParams) return true;
  
  // Consider these changes as "major" - they should trigger a full SSR navigation
  return (
    prevParams.sort !== newParams.sort ||
    prevParams.status !== newParams.status ||
    prevParams.dealType !== newParams.dealType ||
    prevParams.category !== newParams.category ||
    prevParams.store !== newParams.store ||
    prevParams.search !== newParams.search
  );
}
