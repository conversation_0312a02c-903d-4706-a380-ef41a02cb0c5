025-05-14 16:06:15] PS C:\CODING\nicedeals\frontend> npm run dev

> nicedeals-frontend@0.1.0 dev
> next dev

  ▲ Next.js 14.2.26
  - Local:        http://localhost:3000
  - Environments: .env

 ✓ Starting...
 ✓ Ready in 3.4s
 ○ Compiling /dealsBrowse ...
 ⚠ ./app/dealsBrowse/page.tsx
Attempted import error: 'getCategoryById' is not exported from '../services/dealService' (imported as 'getCategoryById').

Import trace for requested module:
./app/dealsBrowse/page.tsx

./app/dealsBrowse/page.tsx
Attempted import error: 'getStoreById' is not exported from '../services/dealService' (imported 
as 'getStoreById').

Import trace for requested module:
./app/dealsBrowse/page.tsx
Starting server-side data fetching...
getDeals: Sending request to: /deals?category=38&status=active&sort=newest&page=1&pageSize=30
Error fetching category for metadata: TypeError: (0 , _services_dealService__WEBPACK_IMPORTED_MODULE_2__.getCategoryById) is not a function
    at Module.generateMetadata (webpack-internal:///(rsc)/./app/dealsBrowse/page.tsx:38:106)    
    at C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:399790
    at C:\CODING\nicedeals\frontend\node_modules\next\dist\server\lib\trace\tracer.js:140:36    
    at NoopContextManager.with (C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\@opentelemetry\api\index.js:1:7062)
    at ContextAPI.with (C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\@opentelemetry\api\index.js:1:518)
    at NoopTracer.startActiveSpan (C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\@opentelemetry\api\index.js:1:18093)
    at ProxyTracer.startActiveSpan (C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\@opentelemetry\api\index.js:1:18854)
    at C:\CODING\nicedeals\frontend\node_modules\next\dist\server\lib\trace\tracer.js:122:103   
    at NoopContextManager.with (C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\@opentelemetry\api\index.js:1:7062)
    at ContextAPI.with (C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\@opentelemetry\api\index.js:1:518)
    at NextTracerImpl.trace (C:\CODING\nicedeals\frontend\node_modules\next\dist\server\lib\trace\tracer.js:122:28)
    at C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:399694
    at C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:401710
    at tC (C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:401803)
    at tx (C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:402535)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tR (C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:36:2486)
    at async C:\CODING\nicedeals\frontend\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:36:2989
getDeals: Raw responsex: {
  status: 200,
  statusText: 'OK',
  headers: Object [AxiosHeaders] {
    vary: 'Origin, Accept-Encoding',
    'content-security-policy': "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests",   
    'cross-origin-opener-policy': 'same-origin',
    'cross-origin-resource-policy': 'cross-origin',
    'origin-agent-cluster': '?1',
    'referrer-policy': 'no-referrer',
    'strict-transport-security': 'max-age=15552000; includeSubDomains',
    'x-content-type-options': 'nosniff',
    'x-dns-prefetch-control': 'off',
    'x-download-options': 'noopen',
    'x-frame-options': 'SAMEORIGIN',
    'x-permitted-cross-domain-policies': 'none',
    'x-xss-protection': '0',
    'ratelimit-policy': '500;w=900',
    'ratelimit-limit': '500',
    'ratelimit-remaining': '499',
    'ratelimit-reset': '153',
    'content-type': 'application/json; charset=utf-8',
    etag: 'W/"14d1-0Shi7noVtbLsJSSC7kbBzwHTcm4"',
    date: 'Wed, 14 May 2025 15:07:02 GMT',
    connection: 'keep-alive',
    'keep-alive': 'timeout=5',
    'transfer-encoding': 'chunked'
  },
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http', 'fetch' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 0,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function [FormData]], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: Object [AxiosHeaders] {
      Accept: 'application/json, text/plain, */*',
      'Content-Type': 'application/json',
      'User-Agent': 'axios/1.8.1',
      'Accept-Encoding': 'gzip, compress, deflate, br'
    },
    baseURL: 'http://localhost:5010/api',
    method: 'get',
    url: '/deals?category=38&status=active&sort=newest&page=1&pageSize=30',
    allowAbsoluteUrls: true,
    data: undefined
  },
  request: <ref *1> ClientRequest {
    _events: [Object: null prototype] {
      abort: [Function (anonymous)],
      aborted: [Function (anonymous)],
      connect: [Function (anonymous)],
      error: [Function (anonymous)],
      socket: [Function (anonymous)],
      timeout: [Function (anonymous)],
      finish: [Function: requestOnFinish]
    },
    _eventsCount: 7,
    _maxListeners: undefined,
    outputData: [],
    outputSize: 0,
    writable: true,
    destroyed: true,
    _last: true,
    chunkedEncoding: false,
    shouldKeepAlive: true,
    maxRequestsOnConnectionReached: false,
    _defaultKeepAlive: true,
    useChunkedEncodingByDefault: false,
    sendDate: false,
    _removedConnection: false,
    _removedContLen: false,
    _removedTE: false,
    strictContentLength: false,
    _contentLength: 0,
    _hasBody: true,
    _trailer: '',
    finished: true,
    _headerSent: true,
    _closed: true,
    socket: Socket {
      connecting: false,
      _hadError: false,
      _parent: null,
      _host: 'localhost',
      _closeAfterHandlingError: false,
      _events: [Object],
      _readableState: [ReadableState],
      _writableState: [WritableState],
      allowHalfOpen: false,
      _maxListeners: undefined,
      _eventsCount: 6,
      _sockname: null,
      _pendingData: null,
      _pendingEncoding: '',
      server: null,
      _server: null,
      timeout: 5000,
      parser: null,
      _httpMessage: null,
      autoSelectFamilyAttemptedAddresses: [Array],
      [Symbol(async_id_symbol)]: -1,
      [Symbol(kHandle)]: [TCP],
      [Symbol(lastWriteQueueSize)]: 0,
      [Symbol(timeout)]: Timeout {
        _idleTimeout: 5000,
        _idlePrev: [TimersList],
        _idleNext: [TimersList],
        _idleStart: 45320,
        _onTimeout: [Function: bound ],
        _timerArgs: undefined,
        _repeat: null,
        _destroyed: false,
        [Symbol(refed)]: false,
        [Symbol(kHasPrimitive)]: false,
        [Symbol(asyncId)]: 8544,
        [Symbol(triggerId)]: 8542,
        [Symbol(kResourceStore)]: [Object],
        [Symbol(kResourceStore)]: [Object],
        [Symbol(kResourceStore)]: [Object],
        [Symbol(kResourceStore)]: undefined
      },
      [Symbol(kBuffer)]: null,
      [Symbol(kBufferCb)]: null,
      [Symbol(kBufferGen)]: null,
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kSetNoDelay)]: true,
      [Symbol(kSetKeepAlive)]: true,
      [Symbol(kSetKeepAliveInitialDelay)]: 1,
      [Symbol(kBytesRead)]: 0,
      [Symbol(kBytesWritten)]: 0
    },
    _header: 'GET /api/deals?category=38&status=active&sort=newest&page=1&pageSize=30 HTTP/1.1\r\n' +
      'Accept: application/json, text/plain, */*\r\n' +
      'Content-Type: application/json\r\n' +
      'User-Agent: axios/1.8.1\r\n' +
      'Accept-Encoding: gzip, compress, deflate, br\r\n' +
      'Host: localhost:5010\r\n' +
      'Connection: keep-alive\r\n' +
      '\r\n',
    _keepAliveTimeout: 0,
    _onPendingData: [Function: nop],
    agent: Agent {
      _events: [Object: null prototype],
      _eventsCount: 2,
      _maxListeners: undefined,
      defaultPort: 80,
      protocol: 'http:',
      options: [Object: null prototype],
      requests: [Object: null prototype] {},
      sockets: [Object: null prototype] {},
      freeSockets: [Object: null prototype],
      keepAliveMsecs: 1000,
      keepAlive: true,
      maxSockets: Infinity,
      maxFreeSockets: 256,
      scheduling: 'lifo',
      maxTotalSockets: Infinity,
      totalSocketCount: 1,
      [Symbol(shapeMode)]: false,
      [Symbol(kCapture)]: false
    },
    socketPath: undefined,
    method: 'GET',
    maxHeaderSize: undefined,
    insecureHTTPParser: undefined,
    joinDuplicateHeaders: undefined,
    path: '/api/deals?category=38&status=active&sort=newest&page=1&pageSize=30',
    _ended: true,
    res: IncomingMessage {
      _events: [Object],
      _readableState: [ReadableState],
      _maxListeners: undefined,
      socket: null,
      httpVersionMajor: 1,
      httpVersionMinor: 1,
      httpVersion: '1.1',
      complete: true,
      rawHeaders: [Array],
      rawTrailers: [],
      joinDuplicateHeaders: undefined,
      aborted: false,
      upgrade: false,
      url: '',
      method: null,
      statusCode: 200,
      statusMessage: 'OK',
      client: [Socket],
      _consuming: false,
      _dumped: false,
      req: [Circular *1],
      _eventsCount: 4,
      responseUrl: 'http://localhost:5010/api/deals?category=38&status=active&sort=newest&page=1&pageSize=30',
      redirects: [],
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kHeaders)]: [Object],
      [Symbol(kHeadersCount)]: 48,
      [Symbol(kTrailers)]: null,
      [Symbol(kTrailersCount)]: 0
    },
    aborted: false,
    timeoutCb: null,
    upgradeOrConnect: false,
    parser: null,
    maxHeadersCount: null,
    reusedSocket: false,
    host: 'localhost',
    protocol: 'http:',
    _redirectable: Writable {
      _events: [Object],
      _writableState: [WritableState],
      _maxListeners: undefined,
      _options: [Object],
      _ended: true,
      _ending: true,
      _redirectCount: 0,
      _redirects: [],
      _requestBodyLength: 0,
      _requestBodyBuffers: [],
      _eventsCount: 3,
      _onNativeResponse: [Function (anonymous)],
      _currentRequest: [Circular *1],
      _currentUrl: 'http://localhost:5010/api/deals?category=38&status=active&sort=newest&page=1&pageSize=30',
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false
    },
    [Symbol(shapeMode)]: false,
    [Symbol(kCapture)]: false,
    [Symbol(kBytesWritten)]: 0,
    [Symbol(kNeedDrain)]: false,
    [Symbol(corked)]: 0,
    [Symbol(kOutHeaders)]: [Object: null prototype] {
      accept: [Array],
      'content-type': [Array],
      'user-agent': [Array],
      'accept-encoding': [Array],
      host: [Array]
    },
    [Symbol(errored)]: null,
    [Symbol(kHighWaterMark)]: 16384,
    [Symbol(kRejectNonStandardBodyWrites)]: false,
    [Symbol(kUniqueHeaders)]: null
  },
  data: {
    deals: [ [Object], [Object], [Object] ],
    totalCount: 3,
    page: 1,
    pageSize: 30,
    totalPages: 1
  }
}
Deals fetched successfully, count: 3
Categories fetched successfully from server-side, count: 26
Fetching stores from server for category 38
Full stores URL: http://localhost:5010/api/stores?category=38
Stores response status: 200
Successfully fetched 0 stores
Stores data is an array at the top level
Stores fetched successfully from server-side, count: 94
Server-side fetched: 26 categories, 94 stores
Initial categories from server: 26
Initial stores from server: 94
 ⚠ ./app/dealsBrowse/page.tsx
Attempted import error: 'getCategoryById' is not exported from '../services/dealService' (imported as 'getCategoryById').

Import trace for requested module:
./app/dealsBrowse/page.tsx

./app/dealsBrowse/page.tsx
Attempted import error: 'getStoreById' is not exported from '../services/dealService' (imported 
as 'getStoreById').

Import trace for requested module:
./app/dealsBrowse/page.tsx
 GET /dealsBrowse?category=38&status=active&sort=newest 200 in 5422ms
 ⚠ ./app/dealsBrowse/page.tsx
Attempted import error: 'getCategoryById' is not exported from '../services/dealService' (imported as 'getCategoryById').

Import trace for requested module:
./app/dealsBrowse/page.tsx

./app/dealsBrowse/page.tsx
Attempted import error: 'getStoreById' is not exported from '../services/dealService' (imported 
as 'getStoreById').

Import trace for requested module:
./app/dealsBrowse/page.tsx
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 3201ms