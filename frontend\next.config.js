/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // App Router is enabled by default in Next.js 13.4+
  // experimental: {
  //   appDir: true, // No longer needed for Next 14 if using /app
  // },
  async rewrites() {
    return [
      {
        // Proxy API requests to the backend server
        source: '/api/:path*',
        // Ensure this matches your backend server's address and port
        destination: 'http://localhost:5010/api/:path*',
      },
    ];
  },
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '5010',
        pathname: '/uploads/**', // Allow images specifically from the /uploads path
      },
      {
        protocol: 'https', // Allow images from deal websites too
        hostname: '**', // Allow any hostname (be cautious with this in production)
      }
    ],
  },
  // If you were using react-scripts for environment variables (REACT_APP_...)
  // you might need to configure Next.js's env handling:
  // env: {
  //   NEXT_PUBLIC_API_URL: process.env.REACT_APP_API_URL || 'http://localhost:5010/api',
  // },
};

module.exports = nextConfig;
