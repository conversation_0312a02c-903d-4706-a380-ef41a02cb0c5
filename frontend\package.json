{"name": "nicedeals-frontend", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.19", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.67.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.1", "@types/node": "^18.16.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-helmet": "^6.1.11", "@types/react-helmet-async": "^1.0.1", "axios": "^1.3.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.29.3", "date-fns-tz": "^2.0.0", "formik": "^2.2.9", "humps": "^2.0.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.477.0", "next": "^14.2.26", "react": "^18.2.0", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.0", "react-query": "^3.39.3", "react-router-dom": "^6.10.0", "recharts": "^2.15.1", "serve": "^14.2.1", "socket.io-client": "^4.6.1", "tailwind-merge": "^3.0.2", "typescript": "~4.9.5", "web-vitals": "^3.3.1", "yup": "^1.1.1", "zod": "^3.24.2"}, "scripts": {"start:cra": "react-scripts start", "dev": "next dev -p 3010", "build": "next build", "start": "next start", "lint": "next lint", "test": "react-scripts test", "eject": "react-scripts eject", "serve": "serve -s build -l 3010"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/humps": "^2.0.6", "@types/lodash": "^4.17.16", "autoprefixer": "^10.4.14", "postcss": "^8.4.23", "react-scripts": "5.0.1", "tailwindcss": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "proxy": "http://localhost:5010"}