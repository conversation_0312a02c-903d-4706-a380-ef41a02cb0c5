/* NiceDeals Custom Styles */

/* General Layout */
.glass {
  @apply bg-white/70 backdrop-blur-md border border-white/30 shadow-sm;
}

.card-hover {
  @apply transition-all duration-300 hover:shadow-md hover:-translate-y-1;
}

/* Deal Cards */
.deal-card {
  @apply glass rounded-xl overflow-hidden;
}

.deal-image {
  @apply object-cover h-48 w-full transition-transform duration-500 hover:scale-105;
}

.deal-price {
  @apply text-lg font-bold text-deal-orange;
}

.deal-original-price {
  @apply text-sm line-through text-gray-400;
}

.deal-title {
  @apply text-lg font-medium text-gray-900 hover:text-deal-orange transition-colors duration-200 line-clamp-2 leading-tight;
}

/* Deal badges */
.deal-badge {
  @apply inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.deal-badge-primary {
  @apply bg-deal-orange text-white;
}

.deal-badge-secondary {
  @apply bg-deal-blue-light text-deal-blue;
}

.deal-badge-success {
  @apply bg-deal-green-light text-deal-green;
}

/* Navigation styling */
.nav-link {
  @apply relative px-3 py-2 transition-colors duration-300 hover:text-deal-orange;
}

.nav-link.active {
  @apply text-deal-orange font-medium;
}

.nav-link.active::after {
  content: '';
  @apply absolute bottom-0 left-0 w-full h-0.5 bg-deal-orange rounded-full;
}

/* Search input styling */
.search-input {
  @apply w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm placeholder-gray-400 shadow-sm focus:border-deal-orange focus:outline-none focus:ring-1 focus:ring-deal-orange;
}

/* Custom Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.85;
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes slide-up {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slide-up 0.5s ease-out;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
} 