import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './hooks/useAuth';
import ScrollToTop from './components/common/ScrollToTop';
import { Toaster } from 'react-hot-toast';
import VigLink from './components/common/VigLink';

// Layouts
import MainLayout from './components/layouts/MainLayout';
import AdminLayout from './layouts/AdminLayout';

// Pages
import HomePage from './pages/HomePage';
import DealsBrowsePage from './pages/DealsBrowsePage';
import DealDetailPage from './pages/DealDetailPage';
import DealConfirmationPage from './pages/DealConfirmationPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ProfilePage from './pages/ProfilePage';
import UserDealsPage from './pages/UserDealsPage';
import SavedDealsPage from './pages/SavedDealsPage';
import NotFoundPage from './pages/NotFoundPage';
import CreateDealPage from './pages/CreateDealPage';
import AdminDashboardPage from './pages/admin/AdminDashboardPage';
import AdminCategoriesPage from './pages/admin/AdminCategoriesPage';
import AdminStoresPage from './pages/admin/AdminStoresPage';
import AdminDealsPage from './pages/admin/AdminDealsPage';
import AdminUsersPage from './pages/admin/AdminUsersPage';
import AdminScrapersPage from './pages/admin/AdminScrapersPage';
import AdminEditDealPage from './pages/admin/AdminEditDealPage';
import UserCommentsPage from './pages/UserCommentsPage';
import VerifyEmailPage from './pages/VerifyEmailPage';
import VerifyEmailSentPage from './pages/VerifyEmailSentPage';

// Protected route component
const ProtectedRoute = ({ children, requiredRole }: { children: JSX.Element, requiredRole?: string }) => {
  const { isAuthenticated, user, loading } = useAuth();
  
  // Show loading indicator or nothing while checking auth status
  if (loading) {
    return <div className="flex h-screen items-center justify-center">
      <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary-500"></div>
    </div>;
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  if (requiredRole && user?.role !== requiredRole) {
    return <Navigate to="/" replace />;
  }
  
  return children;
};

// Admin route component
const AdminRoute = ({ children }: { children: JSX.Element }) => {
  const { isAuthenticated, user, loading } = useAuth();
  
  // Show loading indicator while checking auth status
  if (loading) {
    return <div className="flex h-screen items-center justify-center">
      <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary-500"></div>
    </div>;
  }
  
  // Check if user is authenticated and has admin access (userId 1 or 2)
  const hasAdminAccess = isAuthenticated && user && (user.id === 1 || user.id === 2);
  
  if (!hasAdminAccess) {
    return <Navigate to="/" replace />;
  }
  
  return children;
};

const App: React.FC = () => {
  return (
    <>
      <Toaster 
        position="top-right"
        toastOptions={{
          // Default options for all toasts
          duration: 4000, // 4 seconds
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 10000,
            iconTheme: {
              primary: '#4ade80',
              secondary: '#fff',
            },
          },
          error: {
            duration: 10000,
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />
      <ScrollToTop />
      <VigLink />
      <Routes>
        {/* Public routes with main layout */}
        <Route path="/" element={<MainLayout />}>
          <Route index element={<HomePage />} />
          <Route path="dealsBrowse" element={<DealsBrowsePage />} />
          <Route path="deals/confirmation" element={<DealConfirmationPage />} />
          <Route path="deals/create" element={
            <ProtectedRoute>
              <CreateDealPage />
            </ProtectedRoute>
          } />
          <Route path="dealDetail/:id" element={<DealDetailPage />} />
          <Route path="login" element={<LoginPage />} />
          <Route path="register" element={<RegisterPage />} />
          <Route path="verify-email" element={<VerifyEmailPage />} />
          <Route path="verify-email-sent" element={<VerifyEmailSentPage />} />
          
          {/* User routes (protected) */}
          <Route path="user/profile" element={
            <ProtectedRoute>
              <ProfilePage />
            </ProtectedRoute>
          } />
          <Route path="user/deals" element={
            <ProtectedRoute>
              <UserDealsPage />
            </ProtectedRoute>
          } />
          <Route path="user/saved-deals" element={
            <ProtectedRoute>
              <SavedDealsPage />
            </ProtectedRoute>
          } />
          <Route path="user/comments" element={
            <ProtectedRoute>
              <UserCommentsPage />
            </ProtectedRoute>
          } />
          <Route path="profile" element={
            <ProtectedRoute>
              <ProfilePage />
            </ProtectedRoute>
          } />
        </Route>
        
        {/* Admin routes - using AdminRoute for userId 1 and 2 only */}
        <Route path="/admin" element={
          <AdminRoute>
            <AdminLayout />
          </AdminRoute>
        }>
          <Route index element={<AdminDashboardPage />} />
          <Route path="deals" element={<AdminDealsPage />} />
          <Route path="deals/edit/:id" element={<AdminEditDealPage />} />
          <Route path="users" element={<AdminUsersPage />} />
          <Route path="scrapers" element={<AdminScrapersPage />} />
          <Route path="categories" element={<AdminCategoriesPage />} />
          <Route path="stores" element={<AdminStoresPage />} />
        </Route>
        
        {/* 404 page */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </>
  );
};

export default App;
