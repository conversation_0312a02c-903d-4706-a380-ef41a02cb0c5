'use client'; // Mark as Client Component

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Tag, ChevronDown, ChevronUp } from 'lucide-react';
import { categoryService } from '../services/categoryService';
import { Category } from '../types';
import { getCategoryIcon } from './layouts/MainLayout';

const CategoryList: React.FC = () => {
  const [showAll, setShowAll] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const data = await categoryService.getCategories();
        setCategories(data);
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCategories();
  }, []);
  
  // Determine how many categories to display
  const displayCategories = showAll ? categories : categories.slice(0, 12);
  
  return (
    <div className="py-8">
      <div className="container mx-auto px-0">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Tag className="h-5 w-5 text-deal-orange" />
            <h2 className="text-xl font-display font-semibold text-gray-800">Browse Categories</h2>
          </div>
          <Link href="/dealsBrowse" className="flex items-center gap-1 text-sm font-medium text-deal-orange">
            View All Categories
            <ChevronDown className="h-4 w-4" />
          </Link>
        </div>
        
        {/* Categories Grid */}
        {loading ? (
          // Loading skeletons
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="glass rounded-xl p-4 animate-pulse">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-gray-200"></div>
                  <div className="space-y-2">
                    <div className="h-4 w-20 bg-gray-200 rounded"></div>
                    <div className="h-3 w-16 bg-gray-100 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {displayCategories.map((category, index) => (
                <Link
                  key={category.id}
                  href={`/dealsBrowse?category=${category.id}`}
                  className="glass category-item animate-fade-in"
                  style={{ animationDelay: `${index * 0.05}s` }}
                >
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-deal-orange-light to-deal-orange/10 flex items-center justify-center">
                    <span className="category-icon text-deal-orange">
                      {React.cloneElement(getCategoryIcon(category.name), { className: 'h-5 w-5' })}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">{category.name}</span>
                    <div className="text-xs text-gray-500">
                      {/* Add deal count if available in your Category type */}
                      {category.dealsCount ? `${category.dealsCount} deals` : 'Browse deals'}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
            
            {categories.length > 12 && (
              <div className="mt-6 text-center">
                <button
                  onClick={() => setShowAll(!showAll)}
                  className="btn-secondary-new flex items-center gap-2 mx-auto"
                >
                  {showAll ? (
                    <>
                      <span>Show Less</span>
                      <ChevronUp className="h-4 w-4" />
                    </>
                  ) : (
                    <>
                      <span>Show All Categories</span>
                      <ChevronDown className="h-4 w-4" />
                    </>
                  )}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default CategoryList;