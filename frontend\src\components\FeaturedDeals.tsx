'use client';

import React from 'react';
import Link from 'next/link';
import { <PERSON>, Zap, Clock, ChevronRight } from 'lucide-react';
import DealCard from './deals/DealCard';
import { Deal } from '../types';

interface DealsSectionConfig {
  title: string;
  icon: React.ReactNode;
  deals: Deal[];
  isLoading: boolean;
  seeAllLink: string;
  background: string;
}

interface FeaturedDealsProps {
  gettingWarmDeals?: Deal[];
  newestDeals?: Deal[];
  trendingDeals?: Deal[];
  isLoading?: boolean;
}

const sectionBaseConfig = [
  {
    key: 'gettingWarm',
    title: "Getting Warm",
    icon: <Flame className="w-5 h-5 text-white" />,
    seeAllLink: "/dealsBrowse?status=active&sort=getting-warm",
    background: "bg-gradient-to-r from-deal-orange to-deal-orange-dark"
  },
  {
    key: 'newest',
    title: "New Arrivals",
    icon: <Clock className="w-5 h-5 text-white" />,
    seeAllLink: "/dealsBrowse?sort=newest",
    background: "bg-gradient-to-r from-deal-green to-emerald-600"
  }
];

const FeaturedDeals: React.FC<FeaturedDealsProps> = ({
  gettingWarmDeals = [],
  newestDeals = [],
  trendingDeals = [],
  isLoading = false
}) => {
  
  const sections: DealsSectionConfig[] = sectionBaseConfig.map(config => {
    let deals: Deal[] = [];
    if (config.key === 'gettingWarm') deals = gettingWarmDeals || [];
    if (config.key === 'newest') deals = newestDeals || [];
    
    return {
      ...config,
      deals: deals,
      isLoading: isLoading
    };
  }).filter(section => section.deals && section.deals.length > 0 || section.isLoading);

  if (isLoading && sections.length === 0) {
    return (
      <div className="container mx-auto px-0 space-y-10">
        {[...Array(2)].map((_, sectionIndex) => (
           <div key={sectionIndex} className="animate-fade-in">
              <div className="mb-6 h-8 bg-gray-200 rounded w-1/3 animate-pulse"></div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {[...Array(4)].map((_, index) => (
                  <div key={index} className="animate-pulse glass rounded-xl overflow-hidden h-[300px]">
                    <div className="h-44 bg-gray-200"></div>
                    <div className="p-4 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      <div className="h-4 bg-gray-200 rounded w-full"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
        ))}
      </div>
    );
  }
  
  if (!isLoading && sections.length === 0) {
     return (
      <div className="container mx-auto px-0 text-center py-12">
        <p className="text-gray-500">No featured deals available right now.</p>
      </div>
     ); 
  }
  
  return (
    <div className="container mx-auto px-0 space-y-10">
      {sections.map((section, sectionIndex) => (
        <div key={section.title} className="animate-fade-in" style={{ animationDelay: `${sectionIndex * 0.1}s` }}>
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${section.background}`}>
                {section.icon}
              </div>
              <h2 className="text-xl font-display font-semibold text-gray-800">{section.title}</h2>
            </div>
            
            <Link href={section.seeAllLink} className="flex items-center gap-1 text-deal-blue hover:text-deal-orange transition-colors">
              <span>View all</span>
              <ChevronRight className="w-4 h-4" />
            </Link>
          </div>
          
          {section.isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, index) => (
                <div 
                  key={index} 
                  className="animate-pulse glass rounded-xl overflow-hidden h-[300px]"
                >
                  <div className="h-44 bg-gray-200"></div>
                  <div className="p-4 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-200 rounded w-full"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : section.deals && section.deals.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {section.deals.map((deal: Deal, index: number) => (
                <div 
                  key={deal.id} 
                  className="animate-slide-up" 
                  style={{ animationDelay: `${sectionIndex * 0.1 + index * 0.05}s` }}
                >
                  <DealCard deal={deal} showActions={false} />
                </div>
              ))}
            </div>
          ) : (
             <div className="glass text-center py-12 rounded-xl">
              <p className="text-gray-500">No {section.title.toLowerCase()} available at the moment.</p>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default FeaturedDeals;