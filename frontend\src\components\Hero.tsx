'use client'; // Mark as Client Component

import React, { useState, useEffect } from 'react';
import Link from 'next/link'; // Import next/link
import { ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';

// Define the HeroSlide interface
interface HeroSlide {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  buttonText: string;
  buttonLink: string;
  altButtonText?: string;
  altButtonLink?: string;
  bgColor: string;
}

// Create an array of slides
const slides: HeroSlide[] = [
  {
    id: 1,
    title: "Today's Hottest Deals",
    subtitle: "Save Big",
    description: "Discover incredible discounts on top brands and trending products that everyone's talking about.",
    image: "https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?q=80&w=2070&auto=format&fit=crop",
    buttonText: "Explore Hot Deals",
    buttonLink: "/dealsBrowse?sort=hottest",
    altButtonText: "Learn More",
    altButtonLink: "/dealsBrowse",
    bgColor: "from-orange-500/20 to-pink-500/20"
  },
  {
    id: 2,
    title: "Electronics Super Sale",
    subtitle: "Tech Deals",
    description: "Latest gadgets and electronics at unbeatable prices. Upgrade your tech game today!",
    image: "https://images.unsplash.com/photo-1593642632823-8f785ba67e45?q=80&w=2532&auto=format&fit=crop",
    buttonText: "Shop Electronics",
    buttonLink: "/dealsBrowse?category=1",
    bgColor: "from-blue-500/20 to-purple-500/20"
  },
  {
    id: 3,
    title: "Home & Garden Specials",
    subtitle: "Refresh Your Space",
    description: "Transform your living spaces with amazing deals on home decor, furniture and garden essentials.",
    image: "https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?q=80&w=2070&auto=format&fit=crop",
    buttonText: "Browse Home Deals",
    buttonLink: "/dealsBrowse?category=3",
    bgColor: "from-green-500/20 to-teal-500/20"
  },
  {
    id: 4,
    title: "Fashion Clearance",
    subtitle: "Style for Less",
    description: "Update your wardrobe with the latest trends at clearance prices. Look great for less!",
    image: "https://images.unsplash.com/photo-1483985988355-763728e1935b?q=80&w=2070&auto=format&fit=crop",
    buttonText: "Shop Fashion",
    buttonLink: "/dealsBrowse?category=5",
    bgColor: "from-red-500/20 to-yellow-500/20"
  }
];

const Hero: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [autoplay, setAutoplay] = useState(true);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);

  // Function to navigate to the next slide
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
  };

  // Function to navigate to the previous slide
  const prevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
  };

  // Go to a specific slide
  const goToSlide = (index: number) => {
    setCurrentSlide(index);
    setAutoplay(false); // Pause autoplay when user manually navigates
    // Restart autoplay after 5 seconds of inactivity
    setTimeout(() => setAutoplay(true), 5000);
  };

  // Handle touch events for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (touchStart - touchEnd > 150) {
      nextSlide(); // Swipe left
    }

    if (touchStart - touchEnd < -150) {
      prevSlide(); // Swipe right
    }
  };

  // Set up autoplay effect
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    
    if (autoplay) {
      intervalId = setInterval(nextSlide, 6000);
    }
    
    return () => {
      clearInterval(intervalId);
    };
  }, [autoplay, currentSlide]);

  return (
    <div className="relative overflow-hidden">
      {/* Hero Slider */}
      <div 
        className="relative w-full h-[500px] md:h-[600px]" // Ensure container has dimensions and relative positioning
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${index === currentSlide ? 'opacity-100' : 'opacity-0'}`}
          >
            {/* Background Image Container (positioned relatively implicitly by parent) */}
            <div className="absolute inset-0 z-0"> {/* Image container, base layer */}
              <img 
                src={slide.image} 
                alt={slide.title} 
                className="w-full h-full object-cover"
              />
            </div>

            {/* Gradient Overlay */}
            <div 
              className={`absolute inset-0 bg-gradient-to-t ${slide.bgColor} z-10`} // Gradient overlay on top of image
            />
            
            {/* Content */}
            <div className="relative z-20 flex h-full"> {/* Content on top of overlay */}
              <div className="container mx-auto px-0 flex flex-col justify-center h-full">
                <div className="max-w-lg animate-slide-up" style={{animationDelay: "0.2s"}}>
                  <span className="inline-block px-3 py-1 mb-3 text-sm font-medium rounded-full bg-white/20 text-white backdrop-blur-sm">
                    {slide.subtitle}
                  </span>
                  <h1 className="text-4xl md:text-5xl font-display font-bold text-white mb-4">
                    {slide.title}
                  </h1>
                  <p className="text-lg text-white/90 mb-6">
                    {slide.description}
                  </p>
                  <div className="flex flex-wrap gap-3">
                    <Link 
                      href={slide.buttonLink}
                      className="btn-primary-new flex items-center gap-1"
                    >
                      {slide.buttonText}
                      <ArrowRight className="w-4 h-4" />
                    </Link>
                    {slide.altButtonText && (
                      <Link 
                        href={slide.altButtonLink || '#'}
                        className="btn-tertiary bg-white/10 text-white"
                      >
                        {slide.altButtonText}
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {/* Navigation Arrows */}
        <button 
          onClick={prevSlide} 
          className="absolute left-4 top-1/2 -translate-y-1/2 z-30 p-2 rounded-full bg-white/20 text-white hover:bg-white/40 backdrop-blur-md transition"
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
        
        <button 
          onClick={nextSlide} 
          className="absolute right-4 top-1/2 -translate-y-1/2 z-30 p-2 rounded-full bg-white/20 text-white hover:bg-white/40 backdrop-blur-md transition"
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
        
        {/* Slide Indicators */}
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-30 flex space-x-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                index === currentSlide 
                ? "bg-white w-8" 
                : "bg-white/40"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>
      
      {/* CTA Banner - show for non-authenticated users */}
      {!isAuthenticated && (
        <div className="glass py-4 border-t border-white/20 mt-4 rounded-xl">
          <div className="container mx-auto px-0">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-4 md:mb-0">
                <h3 className="text-lg font-medium text-gray-800">Join our community of deal hunters</h3>
                <p className="text-gray-600">Sign up to save your favorite deals and get personalized notifications</p>
              </div>
              <div className="flex gap-3">
                <Link href="/register" className="btn-primary-new py-2">
                  Create Account
                </Link>
                <Link href="/login" className="btn-secondary-new py-2">
                  Sign In
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Hero;