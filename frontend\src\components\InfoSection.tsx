'use client';

import React from 'react';
import Link from 'next/link';
import { Flame, Share2, <PERSON><PERSON>ing, ArrowRight } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';

const features = [
  {
    icon: <Flame className="w-10 h-10 text-white" />,
    title: 'Hot Deals',
    description: 'Find the hottest deals voted by our community. Save money on your favorite products and services.',
    color: 'bg-gradient-to-br from-deal-orange to-deal-orange-dark',
    link: '/dealsBrowse?sort=hottest'
  },
  {
    icon: <Share2 className="w-10 h-10 text-white" />,
    title: 'Share & Earn',
    description: 'Share deals you\'ve found and earn reputation in our community. Help others save money too.',
    color: 'bg-gradient-to-br from-deal-blue to-blue-600',
    link: '/deals/create'
  },
  {
    icon: <BellRing className="w-10 h-10 text-white" />,
    title: 'Email Alerts',
    description: 'Sign up for personalized deal alerts and receive notifications when new deals matching your interests are posted.',
    color: 'bg-gradient-to-br from-deal-green to-emerald-600',
    link: '/alerts'
  }
];

const InfoSection: React.FC = () => {
  const { isAuthenticated } = useAuth();
  
  return (
    <div className="container mx-auto px-0 py-16 sm:py-20">
      <div className="text-center max-w-3xl mx-auto mb-16 animate-fade-in">
        <h2 className="text-2xl sm:text-4xl font-display font-bold text-gray-800 mb-4">Why Use NiceDeals?</h2>
        <p className="text-gray-600 text-lg">
          Join thousands of savvy shoppers who are saving money every day
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {features.map((feature, index) => (
          <div 
            key={feature.title}
            className="glass rounded-2xl overflow-hidden card-hover animate-slide-up"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="p-6 flex flex-col h-full">
              <div className={`w-16 h-16 rounded-2xl ${feature.color} flex items-center justify-center shadow-lg mb-6`}>
                {feature.icon}
              </div>
              
              <h3 className="text-xl font-display font-semibold text-gray-800 mb-3">
                {feature.title}
              </h3>
              
              <p className="text-gray-600 flex-grow mb-5">
                {feature.description}
              </p>
              
              <Link 
                href={feature.link}
                className="inline-flex items-center text-deal-orange hover:text-deal-orange-dark font-medium gap-1 group"
              >
                <span>Learn more</span>
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        ))}
      </div>
      
      {!isAuthenticated && (
        <div className="mt-20 text-center">
          <div className="glass rounded-2xl p-8 sm:p-10 md:p-14 max-w-4xl mx-auto animate-fade-in">
            <h3 className="text-2xl sm:text-2xl font-display font-semibold text-gray-800 mb-4">
              Ready to start saving?
            </h3>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Sign up today to get personalized deal recommendations and alerts based on your preferences. Never miss a bargain again!
            </p>
            <Link href="/register" className="btn-primary-new animate-pulse-subtle">
              Create Free Account
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default InfoSection; 