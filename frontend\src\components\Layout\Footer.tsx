import React from 'react';
import Link from 'next/link'; // Use next/link for Next.js

const Footer: React.FC = () => {
  return (
    <footer className="glass py-10 border-t border-white/20 mt-auto"> {/* Added mt-auto for potential sticky footer */} 
      <div className="container mx-auto px-0">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-10">
          {/* About Section */}
          <div>
            <h3 className="font-display font-semibold text-gray-800 mb-4">About NiceDeals</h3>
            <p className="text-gray-600 text-sm">
              NiceDeals helps you discover amazing discounts, share great offers, and never miss a bargain again.
            </p>
          </div>
          
          {/* Quick Links Section */}
          <div>
            <h3 className="font-display font-semibold text-gray-800 mb-4">Quick Links</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/dealsBrowse" className="text-gray-600 hover:text-deal-orange">Browse Deals</Link></li>
              <li><Link href="/dealsBrowse" className="text-gray-600 hover:text-deal-orange">Categories</Link></li>
              <li><Link href="/dealsBrowse?sort=hottest" className="text-gray-600 hover:text-deal-orange">Popular Deals</Link></li>
              <li><Link href="/dealsBrowse" className="text-gray-600 hover:text-deal-orange">Merchants</Link></li>
            </ul>
          </div>
          
          {/* Customer Support Section */}
          <div>
            <h3 className="font-display font-semibold text-gray-800 mb-4">Customer Support</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/" className="text-gray-600 hover:text-deal-orange">Help Center</Link></li>
              <li><Link href="/" className="text-gray-600 hover:text-deal-orange">Contact Us</Link></li>
              <li><Link href="/" className="text-gray-600 hover:text-deal-orange">FAQ</Link></li>
            </ul>
          </div>
          
          {/* Subscribe Section */}
          <div>
            <h3 className="font-display font-semibold text-gray-800 mb-4">Subscribe</h3>
            <p className="text-gray-600 text-sm mb-3">
              Get the latest dealss delivered to your inbox.
            </p>
            <form className="flex">
              <input 
                type="email" 
                placeholder="Your email" 
                className="search-input flex-1 rounded-r-none border-gray-300 focus:border-primary-500 focus:ring-primary-500" // Added border styles
              />
              <button 
                type="submit" 
                className="btn btn-primary rounded-l-none"
              >
                Subscribe
              </button>
            </form>
          </div>
        </div>
        {/* Copyright Section */}
        <div className="text-center text-gray-500 text-sm mt-10 pt-6 border-t border-white/10">
          © {new Date().getFullYear()} NiceDeals. All rights reserved.
        </div>
      </div>
    </footer>
  );
};

export default Footer;
