'use client'; // Mark as client component because of hooks

import React, { useState, useEffect, Fragment } from 'react';
import Link from 'next/link';
import { usePathname, useRouter, useSearchParams } from 'next/navigation'; // Use Next.js navigation hooks
import { Dialog, Disclosure, Menu, Transition } from '@headlessui/react';
import {
    Bars3Icon,
    XMarkIcon,
    UserIcon, 
    HomeIcon,
    TagIcon,
    MagnifyingGlassIcon,
    UserCircleIcon,
    ArrowRightOnRectangleIcon,
    ShieldCheckIcon,
    BookmarkIcon,
    // Add any other icons used specifically in the header/nav
} from '@heroicons/react/24/outline';
import { ChevronDownIcon } from '@heroicons/react/20/solid'; // Often used with Menu
import { useAuth } from '@/hooks/useAuth';
import { categoryService } from '@/services/categoryService'; // Use alias
import { Category } from '@/types'; // Use alias
import { getCategoryIcon } from '@/utils/iconUtils'; // Update import extension to .tsx

function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ')
}

const SiteHeader: React.FC = () => {
    const { isAuthenticated, user, logout } = useAuth();
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [categories, setCategories] = useState<Category[]>([]);

    // --- Navigation Link Activity Check (Adapted for Next.js) ---
    const isLinkActive = (path: string): boolean => {
        const currentSort = searchParams.get('sort');
        const currentStatus = searchParams.get('status');
        const linkUrl = new URL(path, 'http://dummybase'); // Need a base for URL constructor
        const linkSort = linkUrl.searchParams.get('sort');
        const linkStatus = linkUrl.searchParams.get('status');

        // For "Getting Warm" and "Hottest", match sort param
        if (path.includes('sort=getting-warm') || path.includes('sort=hottest')) {
            return currentSort === linkSort;
        }

        // For "Browse Deals" (newest), match sort and status
        if (path.includes('sort=newest')) {
            // Default to newest if sort is null/undefined
            const effectiveCurrentSort = currentSort ?? 'newest'; 
            return effectiveCurrentSort === 'newest' && currentStatus === 'active';
        }
        
        // For admin page
        if (path.startsWith('/admin')) {
             return pathname.startsWith('/admin');
        }
        
        // For exact root path
        if (path === '/') {
            return pathname === '/';
        }

        return false; // Default case
    };
    // --- End Navigation Link Activity Check ---

    useEffect(() => {
        const fetchCategories = async () => {
            try {
                const data = await categoryService.getCategories();
                setCategories(data);
            } catch (error) {
                console.error('Error fetching categories:', error);
            }
        };
        fetchCategories();
    }, []);

    const hasAdminAccess = isAuthenticated && user && (user.id === 1 || user.id === 2);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.push(`/dealsBrowse?search=${encodeURIComponent(searchQuery)}`); // Use router.push
    };
    
     // Dynamically build navigation based on auth/admin status
    const desktopNavigation = [
        { name: 'Browse Deals', href: '/dealsBrowse?status=active&sort=newest' },
        { name: 'Getting Warm', href: '/dealsBrowse?status=active&sort=getting-warm' },
        { name: 'Hottest', href: '/dealsBrowse?status=active&sort=hottest' },
    ];
    if (hasAdminAccess) {
        desktopNavigation.push({ name: 'Admin', href: '/admin' });
    }

    const userNavigation = [
        { name: 'Your Profile', href: '/user/profile', icon: UserCircleIcon },
        { name: 'Your Deals', href: '/user/deals', icon: TagIcon },
        { name: 'Your Saved Deals', href: '/user/saved-deals', icon: BookmarkIcon },
        { name: 'Your Comments', href: '/user/comments', icon: TagIcon }, // Icon might need changing
    ];

    const mobileBaseNavigation = [
        { name: 'Home', href: '/', icon: HomeIcon },
        { name: 'Browse Deals', href: '/dealsBrowse?status=active&sort=newest', icon: TagIcon },
        { name: 'Getting Warm', href: '/dealsBrowse?status=active&sort=getting-warm', icon: TagIcon }, // Add icons if desired
        { name: 'Hottest', href: '/dealsBrowse?status=active&sort=hottest', icon: TagIcon },
    ];
     if (hasAdminAccess) {
        mobileBaseNavigation.push({ name: 'Admin', href: '/admin', icon: ShieldCheckIcon });
    }

    return (
        <header className="sticky top-0 z-50 glass border-b border-white/30 backdrop-blur-md">
            {/* --- Desktop Header --- */}
            <div className="container mx-auto px-0">
                <div className="flex h-16 items-center justify-between">
                    {/* Logo */}
                    <div className="flex items-center flex-shrink-0">
                        <Link href="/" className="flex items-center">
                            {/* Ensure the logo path is correct in /public */}
                            <img src="/nicedeals-logo.png" alt="NiceDeals" className="h-10 object-contain" />
                        </Link>
                    </div>

                    {/* Desktop Navigation */}
                    <nav className="hidden md:flex items-center space-x-4 lg:space-x-6 ml-6" aria-label="Main navigation">
                        {desktopNavigation.map((item) => (
                            <Link
                                key={item.name}
                                href={item.href}
                                className={classNames(
                                    'nav-link', // Existing style from globals.css
                                    isLinkActive(item.href) ? 'active' : ''
                                )}
                            >
                                {item.name}
                            </Link>
                        ))}
                    </nav>

                    {/* Search Form (Desktop) */}
                    <div className="flex-1 flex justify-center px-2 lg:ml-6 lg:justify-end">
                        <form className="hidden md:flex items-center w-full max-w-md lg:max-w-xs" onSubmit={handleSearch}>
                            <label htmlFor="search" className="sr-only">Search deals</label>
                            <div className="relative w-full">
                                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                                </div>
                                <input
                                    id="search"
                                    name="search"
                                    className="block w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-3 leading-5 text-gray-900 placeholder-gray-500 focus:border-orange-500 focus:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-orange-500 sm:text-sm"
                                    placeholder="Search for deals..."
                                    type="search"
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                />
                            </div>
                        </form>
                    </div>

                    {/* User Actions (Desktop) */}
                    <div className="hidden md:flex items-center space-x-4 ml-4">
                        {isAuthenticated ? (
                            <>
                                {/* Submit Deal Button */}    
                                <Link
                                    href="/deals/create"
                                    className="btn-primary-new whitespace-nowrap" // Assuming this class exists
                                >
                                    Submit Deal
                                </Link>
                                {/* User Menu */}    
                                <Menu as="div" className="relative flex-shrink-0">
                                    <div>
                                        <Menu.Button className="flex rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2">
                                            <span className="sr-only">Open user menu</span>
                                            {/* Replace with user avatar if available */}
                                            <UserCircleIcon className="h-8 w-8 text-gray-600 rounded-full" /> 
                                        </Menu.Button>
                                    </div>
                                    <Transition
                                        as={Fragment}
                                        enter="transition ease-out duration-100"
                                        enterFrom="transform opacity-0 scale-95"
                                        enterTo="transform opacity-100 scale-100"
                                        leave="transition ease-in duration-75"
                                        leaveFrom="transform opacity-100 scale-100"
                                        leaveTo="transform opacity-0 scale-95"
                                    >
                                        <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                            {userNavigation.map((item) => (
                                                <Menu.Item key={item.name}>
                                                    {({ active }) => (
                                                        <Link
                                                            href={item.href}
                                                            className={classNames(
                                                                active ? 'bg-gray-100' : '',
                                                                'block px-4 py-2 text-sm text-gray-700 flex items-center'
                                                            )}
                                                        >
                                                            {item.icon && <item.icon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />} 
                                                            {item.name}
                                                        </Link>
                                                    )}
                                                </Menu.Item>
                                            ))}
                                            <Menu.Item>
                                                {({ active }) => (
                                                    <button
                                                        onClick={logout}
                                                        className={classNames(
                                                            active ? 'bg-gray-100' : '',
                                                            'block w-full text-left px-4 py-2 text-sm text-gray-700 flex items-center'
                                                        )}
                                                    >
                                                        <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                                                        Sign out
                                                    </button>
                                                )}
                                            </Menu.Item>
                                        </Menu.Items>
                                    </Transition>
                                </Menu>
                            </>
                        ) : (
                            <>
                                {/* Sign In/Up Buttons */}    
                                <Link href="/login" className="btn-secondary-new whitespace-nowrap"> {/* Assuming this class exists */}    
                                    Sign In
                                </Link>
                                <Link href="/register" className="btn-primary-new whitespace-nowrap"> {/* Assuming this class exists */}    
                                    Create Account
                                </Link>
                            </>
                        )}
                    </div>

                    {/* Mobile menu button */}
                    <div className="flex md:hidden">
                        <button
                            type="button"
                            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700 hover:text-gray-900"
                            onClick={() => setMobileMenuOpen(true)}
                        >
                            <span className="sr-only">Open main menu</span>
                            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
                        </button>
                    </div>
                </div>
            </div>

            {/* --- Mobile Menu Dialog --- */}
            <Dialog as="div" className="md:hidden" open={mobileMenuOpen} onClose={setMobileMenuOpen}>
                <div className="fixed inset-0 z-50" />
                <Dialog.Panel className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
                    <div className="flex items-center justify-between">
                        <Link href="/" className="-m-1.5 p-1.5">
                            <span className="sr-only">NiceDeals</span>
                            <img className="h-8 w-auto" src="/nicedeals-logo.png" alt="" />
                        </Link>
                        <button
                            type="button"
                            className="-m-2.5 rounded-md p-2.5 text-gray-700"
                            onClick={() => setMobileMenuOpen(false)}
                        >
                            <span className="sr-only">Close menu</span>
                            <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                        </button>
                    </div>
                    <div className="mt-6 flow-root">
                        <div className="-my-6 divide-y divide-gray-500/10">
                            <div className="space-y-2 py-6">
                                {/* Search Form (Mobile) - reusing desktop logic for now */}    
                                <form className="mt-4 mb-2 px-3" onSubmit={handleSearch}>
                                    <label htmlFor="search-mobile" className="sr-only">Search deals</label>
                                    <div className="relative">
                                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                                        </div>
                                        <input
                                            id="search-mobile"
                                            name="search-mobile"
                                            className="block w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-3 leading-5 text-gray-900 placeholder-gray-500 focus:border-orange-500 focus:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-orange-500 sm:text-sm"
                                            placeholder="Search for deals..."
                                            type="search"
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                            onKeyDown={(e) => { if(e.key === 'Enter') setMobileMenuOpen(false); }} // Close menu on enter
                                        />
                                    </div>
                                </form>
                                {/* Categories */}    
                                <Disclosure as="div" className="mt-2">
                                    {({ open }) => (
                                        <>
                                            <Disclosure.Button className="flex w-full items-center justify-between rounded-lg py-2 pl-3 pr-3.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50">
                                                Categories
                                                <ChevronDownIcon
                                                    className={classNames(open ? 'rotate-180' : '', 'h-5 w-5 flex-none')}
                                                    aria-hidden="true"
                                                />
                                            </Disclosure.Button>
                                            <Disclosure.Panel className="mt-2 space-y-2">
                                                {categories.map((item) => (
                                                    <Disclosure.Button
                                                        key={item.name}
                                                        as={Link}
                                                        href={`/dealsBrowse?category=${item.slug}`}
                                                        onClick={() => setMobileMenuOpen(false)} // Close menu on click
                                                        className="group flex items-center rounded-lg py-2 pl-6 pr-3 text-sm leading-6 font-medium text-gray-700 hover:bg-gray-100"
                                                    >
                                                        {getCategoryIcon(item.name)} 
                                                        {item.name}
                                                    </Disclosure.Button>
                                                ))}
                                            </Disclosure.Panel>
                                        </>
                                    )}
                                </Disclosure>
                            </div>
                            <div className="py-6">
                                {isAuthenticated ? (
                                    <>
                                        {userNavigation.map((item) => (
                                            <Link
                                                key={item.name}
                                                href={item.href}
                                                onClick={() => setMobileMenuOpen(false)}
                                                className="-mx-3 group flex items-center rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                                            >
                                                 {item.icon && (
                                                    <item.icon 
                                                       className="mr-3 h-6 w-6 flex-shrink-0 text-gray-400 group-hover:text-gray-500" 
                                                       aria-hidden="true" 
                                                    />
                                                )}
                                                {item.name}
                                            </Link>
                                        ))}
                                        <button
                                            onClick={() => {
                                                logout();
                                                setMobileMenuOpen(false);
                                            }}
                                            className="-mx-3 flex w-full items-center rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                                        >
                                            <ArrowRightOnRectangleIcon className="mr-3 h-6 w-6 flex-shrink-0 text-gray-400" aria-hidden="true" />
                                            Sign out
                                        </button>
                                    </>
                                ) : (
                                    <Link
                                        href="/login"
                                        onClick={() => setMobileMenuOpen(false)}
                                        className="-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                                    >
                                        Log in
                                    </Link>
                                )}
                                <Link
                                    href="/deals/create"
                                    onClick={() => setMobileMenuOpen(false)}
                                    className="-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-white bg-orange-600 hover:bg-orange-700 mb-2"
                                >
                                   Submit Deal
                                </Link>
                            </div>
                        </div>
                    </div>
                </Dialog.Panel>
            </Dialog>
        </header>
    );
};

export default SiteHeader;
