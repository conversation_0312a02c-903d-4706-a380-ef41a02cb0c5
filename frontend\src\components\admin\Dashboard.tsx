import React, { useState, useEffect } from 'react';
import { 
  ArrowUpIcon, 
  ArrowDownIcon, 
  UserIcon, 
  ShoppingBagIcon, 
  ClockIcon,
  ExclamationCircleIcon,
  CheckCircleIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs';
import { formatDate } from '../../utils/formatters';
import adminService from '../../services/adminService';
import { Skeleton } from '../ui/skeleton';
import { Link } from 'react-router-dom';

interface DashboardStats {
  totalUsers: number;
  totalDeals: number;
  activeDeals: number;
  pendingDeals: number;
  newUsersToday: number;
  newDealsToday: number;
  userGrowth: number;
  dealGrowth: number;
}

interface RecentActivity {
  id: number;
  type: 'user' | 'deal';
  action: string;
  title: string;
  timestamp: string;
  user?: {
    id: number;
    username: string;
  };
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await adminService.getDashboardStats();
        setStats(data.stats);
        setRecentActivity(data.recentActivity || []);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const StatCard = ({ 
    title, 
    value, 
    change, 
    icon, 
    linkTo = null 
  }: { 
    title: string; 
    value: number; 
    change: number; 
    icon: React.ReactNode;
    linkTo?: string | null;
  }) => {
    const CardWrapper = ({ children }: { children: React.ReactNode }) => {
      if (linkTo) {
        return (
          <Link to={linkTo} className="block transition-all duration-200 hover:opacity-90">
            {children}
          </Link>
        );
      }
      return <>{children}</>;
    };

    return (
      <CardWrapper>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            <div className="h-4 w-4 text-muted-foreground">{icon}</div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">{value.toLocaleString()}</div>
                <div className="flex items-center space-x-1 text-xs">
                  {change > 0 ? (
                    <ArrowUpIcon className="h-3 w-3 text-green-500" />
                  ) : change < 0 ? (
                    <ArrowDownIcon className="h-3 w-3 text-red-500" />
                  ) : (
                    <span className="h-3 w-3">-</span>
                  )}
                  <span className={
                    change > 0 
                      ? 'text-green-500' 
                      : change < 0 
                        ? 'text-red-500' 
                        : 'text-gray-500'
                  }>
                    {change !== 0 ? `${Math.abs(change)}% from last period` : 'No change'}
                  </span>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </CardWrapper>
    );
  };

  const renderStatusSummary = () => {
    if (!stats) return null;
    
    const pendingPercentage = stats.totalDeals > 0 
      ? Math.round((stats.pendingDeals / stats.totalDeals) * 100) 
      : 0;
    
    const activePercentage = stats.totalDeals > 0 
      ? Math.round((stats.activeDeals / stats.totalDeals) * 100) 
      : 0;
    
    return (
      <Card>
        <CardHeader>
          <CardTitle>Platform Status</CardTitle>
          <CardDescription>Current state of deals and activity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-1">
                <div className="text-sm font-medium">Active Deals</div>
                <div className="text-sm font-medium">{activePercentage}%</div>
              </div>
              <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-green-500 rounded-full" 
                  style={{ width: `${activePercentage}%` }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-1">
                <div className="text-sm font-medium">Pending Deals</div>
                <div className="text-sm font-medium">{pendingPercentage}%</div>
              </div>
              <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-yellow-500 rounded-full" 
                  style={{ width: `${pendingPercentage}%` }}
                ></div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="flex items-center">
                <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                <div>
                  <div className="text-sm font-medium">Active Deals</div>
                  <div className="text-xs text-gray-500">{stats.activeDeals} deals</div>
                </div>
              </div>
              
              <div className="flex items-center">
                <ClockIcon className="h-5 w-5 text-yellow-500 mr-2" />
                <div>
                  <div className="text-sm font-medium">Pending Deals</div>
                  <div className="text-xs text-gray-500">{stats.pendingDeals} deals</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2">
            <ExclamationCircleIcon className="h-5 w-5 text-red-500" />
            <p className="text-red-700">{error}</p>
          </div>
          <button 
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-100 text-red-700 rounded-md text-sm hover:bg-red-200 transition-colors"
          >
            Retry
          </button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">
            <ChartBarIcon className="mr-2 h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="activity">
            <ClockIcon className="mr-2 h-4 w-4" />
            Recent Activity
          </TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {loading ? (
              Array(4)
                .fill(0)
                .map((_, i) => (
                  <Card key={i}>
                    <CardHeader className="pb-2">
                      <Skeleton className="h-4 w-24" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-8 w-24" />
                      <Skeleton className="mt-2 h-3 w-32" />
                    </CardContent>
                  </Card>
                ))
            ) : (
              <>
                <StatCard
                  title="Total Users"
                  value={stats?.totalUsers || 0}
                  change={stats?.userGrowth || 0}
                  icon={<UserIcon className="h-4 w-4" />}
                  linkTo="/admin/users"
                />
                <StatCard
                  title="Total Deals"
                  value={stats?.totalDeals || 0}
                  change={stats?.dealGrowth || 0}
                  icon={<ShoppingBagIcon className="h-4 w-4" />}
                  linkTo="/admin/deals"
                />
                <StatCard
                  title="Active Deals"
                  value={stats?.activeDeals || 0}
                  change={0}
                  icon={<CheckCircleIcon className="h-4 w-4" />}
                  linkTo="/admin/deals?status=active"
                />
                <StatCard
                  title="Pending Deals"
                  value={stats?.pendingDeals || 0}
                  change={0}
                  icon={<ClockIcon className="h-4 w-4" />}
                  linkTo="/admin/deals?status=pending"
                />
              </>
            )}
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {loading ? (
              Array(2)
                .fill(0)
                .map((_, i) => (
                  <Card key={i}>
                    <CardHeader>
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-48 mt-1" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-24 w-full" />
                    </CardContent>
                  </Card>
                ))
            ) : (
              <>
                {renderStatusSummary()}
                <Card>
                  <CardHeader>
                    <CardTitle>Today's Summary</CardTitle>
                    <CardDescription>New activity in the last 24 hours</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <UserIcon className="h-5 w-5 text-blue-500 mr-2" />
                          <div>
                            <div className="text-sm font-medium">New Users</div>
                            <div className="text-xs text-gray-500">Today</div>
                          </div>
                        </div>
                        <div className="text-xl font-bold">{stats?.newUsersToday || 0}</div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <ShoppingBagIcon className="h-5 w-5 text-green-500 mr-2" />
                          <div>
                            <div className="text-sm font-medium">New Deals</div>
                            <div className="text-xs text-gray-500">Today</div>
                          </div>
                        </div>
                        <div className="text-xl font-bold">{stats?.newDealsToday || 0}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </TabsContent>
        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest actions across the platform</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                Array(5)
                  .fill(0)
                  .map((_, i) => (
                    <div key={i} className="mb-4 flex items-center space-x-4">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-[250px]" />
                        <Skeleton className="h-3 w-[200px]" />
                      </div>
                    </div>
                  ))
              ) : recentActivity.length > 0 ? (
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-4">
                      <div
                        className={`rounded-full p-2 ${
                          activity.type === 'user' ? 'bg-blue-100' : 'bg-green-100'
                        }`}
                      >
                        {activity.type === 'user' ? (
                          <UserIcon className="h-4 w-4 text-blue-500" />
                        ) : (
                          <ShoppingBagIcon className="h-4 w-4 text-green-500" />
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.title}</p>
                        <p className="text-xs text-gray-500">
                          {activity.action} {activity.user && `by ${activity.user.username}`}
                        </p>
                        <p className="text-xs text-gray-400">{formatDate(activity.timestamp)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-sm text-gray-500">No recent activity found</p>
              )}
            </CardContent>
            <CardFooter>
              <p className="text-xs text-gray-500">Showing the last 10 activities</p>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
