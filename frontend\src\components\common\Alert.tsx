import React, { ReactNode } from 'react';
import { ExclamationTriangleIcon, InformationCircleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

interface AlertProps {
  children: ReactNode;
  variant?: 'info' | 'success' | 'warning' | 'destructive';
  title?: string;
}

const Alert: React.FC<AlertProps> = ({ 
  children, 
  variant = 'info',
  title 
}) => {
  const variantConfig = {
    info: {
      icon: InformationCircleIcon,
      classes: 'bg-blue-50 text-blue-800',
      iconColor: 'text-blue-400',
      titleColor: 'text-blue-800',
      textColor: 'text-blue-700'
    },
    success: {
      icon: CheckCircleIcon,
      classes: 'bg-green-50 text-green-800',
      iconColor: 'text-green-400',
      titleColor: 'text-green-800',
      textColor: 'text-green-700'
    },
    warning: {
      icon: ExclamationTriangleIcon,
      classes: 'bg-yellow-50 text-yellow-800',
      iconColor: 'text-yellow-400',
      titleColor: 'text-yellow-800',
      textColor: 'text-yellow-700'
    },
    destructive: {
      icon: ExclamationTriangleIcon,
      classes: 'bg-red-50 text-red-800',
      iconColor: 'text-red-400',
      titleColor: 'text-red-800',
      textColor: 'text-red-700'
    }
  };

  const { icon: Icon, classes, iconColor, titleColor, textColor } = variantConfig[variant];

  return (
    <div className={`p-4 rounded-md ${classes}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          <Icon className={`h-5 w-5 ${iconColor}`} aria-hidden="true" />
        </div>
        <div className="ml-3">
          {title && (
            <h3 className={`text-sm font-medium ${titleColor}`}>{title}</h3>
          )}
          <div className={`mt-2 text-sm ${textColor}`}>
            {typeof children === 'string' ? <p>{children}</p> : children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Alert; 