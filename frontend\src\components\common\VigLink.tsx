import React, { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

// Declare the window interface augmentation properly
declare global {
  interface Window {
    vglnk?: {
      key: string;
      noui?: boolean;
      newTab?: boolean;
      reactivate?: () => void;
      [key: string]: any;
    };
  }
}

/**
 * VigLink/Sovrn component for monetizing outbound links
 * This component uses a simplified implementation to avoid CORS issues
 */
export const VigLink: React.FC = () => {
  const location = useLocation();
  const scriptLoaded = useRef(false);
  
  useEffect(() => {
    // Only load the script once
    if (!scriptLoaded.current) {
      scriptLoaded.current = true;
      
      // Use a timeout to ensure DOM is fully loaded
      setTimeout(() => {
        // Create the vglnk config object
        const vglnkScript = document.createElement('script');
        vglnkScript.type = 'text/javascript';
        vglnkScript.innerHTML = `
          var vglnk = {
            key: 'deb5eb052b31159b9fe8b37c7cd2dea8',
            noui: true, // Disable the UI components (like the partner badge)
            newTab: true, // Open links in new tab
            form_factor_media_query: "true", // Enable responsiveness
            cuid: "${Math.random().toString(36).substring(2, 15)}" // Unique ID to avoid tracking issues
          };
        `;
        document.head.appendChild(vglnkScript);
        
        // Then append the main script
        const scriptElement = document.createElement('script');
        scriptElement.src = 'https://cdn.viglink.com/api/vglnk.js';
        scriptElement.async = true;
        scriptElement.defer = true;
        document.body.appendChild(scriptElement);
        
        console.log('VigLink initialized with simplified implementation');
      }, 100);
    }
    
    // When route changes, try to reactivate VigLink
    if (window.vglnk && typeof window.vglnk.reactivate === 'function') {
      try {
        // Use optional chaining to handle possible undefined values
        setTimeout(() => window.vglnk?.reactivate?.(), 500);
      } catch (e) {
        console.log('VigLink reactivation error:', e);
      }
    }
    
  }, [location.pathname]);
  
  return null;
};

export default VigLink;
