'use client';

import React, { useMemo, useState, useEffect } from 'react';
import Link from 'next/link'; // Import next/link
import { useRouter, usePathname, useSearchParams } from 'next/navigation'; // Import Next.js navigation hooks
import { format } from 'date-fns';
import { 
  ChevronUpIcon, 
  ChevronDownIcon,
  ChatBubbleLeftIcon,
  ClockIcon,
  LinkIcon,
  FireIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import { Deal } from '@/types';
import { useAuth } from '@/hooks/useAuth';
import api from '@/services/api';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { getThumbnailUrl, handleImageError } from '@/utils/imageUtils'; // Adjust path if needed
import { getAffiliateUrl } from '@/utils/formatters';
import { saveDeal, unsaveDeal, checkIfDealSaved } from '@/services/dealService';

// Hot threshold for deal temperature
const GETTING_WARM_TEMPERATURE = 1; // We'll use this as default since it's 1 in .env

interface DealCardProps {
  deal: Deal;
  showActions?: boolean;
}

const DealCard: React.FC<DealCardProps> = ({ deal, showActions = true }) => {
  const { isAuthenticated, user } = useAuth();
  const queryClient = useQueryClient();
  const router = useRouter(); // Use Next.js router
  const pathname = usePathname(); // Use Next.js pathname
  const searchParams = useSearchParams(); // Use Next.js search params
  const [isSaved, setIsSaved] = useState(false);
  
  // Check if deal is saved when component mounts
  useEffect(() => {
    const checkSavedStatus = async () => {
      if (isAuthenticated && deal.id && user?.id) {
        try {
          const saved = await checkIfDealSaved(deal.id, user.id);
          setIsSaved(saved);
        } catch (error) {
          console.error('Error checking saved status:', error);
          setIsSaved(false);
        }
      }
    };
    
    checkSavedStatus();
  }, [isAuthenticated, deal.id, user?.id]);

  // Handle save deal
  const handleSave = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent link/navigation
    e.stopPropagation(); // Prevent event bubbling
    
    if (!isAuthenticated || !user?.id) {
      toast.error('Please log in to save deals');
      return;
    }

    try {
      if (isSaved) {
        await unsaveDeal(deal.id, user.id);
      } else {
        await saveDeal(deal.id, user.id);
      }
      setIsSaved(!isSaved);
      toast.success(isSaved ? "Removed from saved deals" : "Deal saved successfully");
    } catch (error: any) {
      toast.error(error.message || 'Failed to save deal');
    }
  };

  // Function to update URL with new filter
  const updateUrlWithFilter = (filterType: 'store' | 'category', value: string) => {
    const currentParams = new URLSearchParams(searchParams.toString());
    const currentValue = currentParams.get(filterType);
    
    // Only update if the value is different
    if (currentValue !== value) {
      currentParams.set(filterType, value);
      // Always include status=active and sort=newest
      currentParams.set('status', 'active');
      currentParams.set('sort', 'newest');
      router.push(`${pathname}?${currentParams.toString()}`);
    }
  };

  // Format the discount percentage
  const discountPercentage = deal.originalPrice && deal.price
    ? Math.round(((deal.originalPrice - deal.price) / deal.originalPrice) * 100)
    : null;
  
  // Format the date
  const formattedDate = deal.updatedAt ? 
    format(new Date(deal.updatedAt), 'MMM d, yyyy') : 
    'Just now';
  
  // Check if deal is expired
  const isExpired = deal.status === 'expired' || 
    (deal.expiresAt && typeof deal.expiresAt === 'string' && new Date(deal.expiresAt) < new Date());
  
  // Memoize the thumbnail URL calculation to prevent recalculation on every render
  const thumbnailUrl = useMemo(() => getThumbnailUrl(deal), [deal]);
  
  // Vote mutations
  const voteMutation = useMutation({
    mutationFn: async ({ dealId, value }: { dealId: number; value: 1 | -1 }) => {
      const response = await api.post(`/votes`, { dealId, voteType: value });
      return response.data;
    },
    onSuccess: (data) => {
      // Update all queries that contain this deal
      const updateDealInCache = (oldData: any) => {
        if (!oldData) return oldData;
        
        // Handle wrapped data (list responses)
        if (oldData.deals) {
          return {
            ...oldData,
            deals: oldData.deals.map((d: Deal) =>
              d.id === deal.id
                ? { ...d, temperature: data.totalVotes, userVote: data.voteType }
                : d
            )
          };
        }
        
        // Handle single deal response
        if (oldData.data && oldData.data.id === deal.id) {
          return {
            ...oldData,
            data: { ...oldData.data, temperature: data.totalVotes, userVote: data.voteType }
          };
        }
        
        // Handle direct deal object
        if (oldData.id === deal.id) {
          return { ...oldData, temperature: data.totalVotes, userVote: data.voteType };
        }
        
        return oldData;
      };

      // Update all relevant queries using the new API syntax
      queryClient.setQueriesData({ queryKey: ['deals'] }, updateDealInCache);
      queryClient.setQueriesData({ queryKey: ['deal', deal.id] }, updateDealInCache);
      queryClient.setQueriesData({ queryKey: ['recentDeals'] }, updateDealInCache);
      queryClient.setQueriesData({ queryKey: ['trendingDeals'] }, updateDealInCache);
      queryClient.setQueriesData({ queryKey: ['newestDeals'] }, updateDealInCache);
    },
    onError: (error: any) => {
      console.error('Vote error:', error);
      toast.error(error.response?.data?.error || 'Failed to vote');
    }
  });
  
  // Handle vote
  const handleVote = (value: 1 | -1) => {
    if (!isAuthenticated) {
      toast.error('Please log in to vote');
      return;
    }

    // Check if user has already voted
    if (deal?.userVote !== undefined) {
      toast('You have already voted on this deal', {
        icon: '⚠️',
        style: {
          background: '#fef3c7',
          color: '#92400e',
          border: '1px solid #f59e0b',
        },
      });
      return;
    }
    
    voteMutation.mutate({ dealId: deal.id, value });
  };
  
  // Determine vote button styles
  const getUpvoteButtonStyle = () => {
    if (deal.userVote === 1) {
      return 'text-green-600 hover:text-green-700';
    }
    return 'text-gray-400 hover:text-green-600';
  };
  
  const getDownvoteButtonStyle = () => {
    if (deal.userVote === -1) {
      return 'text-red-600 hover:text-red-700';
    }
    return 'text-gray-400 hover:text-red-600';
  };
  
  // Temperature color based on deal temperature (votes)
  const getTemperatureColor = () => {
    if (deal.temperature > 50) return 'text-red-600';
    if (deal.temperature > 25) return 'text-orange-500';
    if (deal.temperature > 0) return 'text-yellow-500';
    if (deal.temperature < -25) return 'text-blue-600';
    if (deal.temperature < 0) return 'text-blue-400';
    return 'text-gray-500';
  };
  
  return (
    <div className="rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 relative">
      {/* Status banners */}
      {isExpired && (
        <div className="absolute top-2 right-2 z-10 bg-gray-600 px-2 py-1 text-xs font-semibold text-white rounded-md">
          Expired
        </div>
      )}
      
      {/* Image section */}
      <div className="relative h-44 bg-gray-100 overflow-hidden">
        <Link href={`/dealDetail/${deal.id}`}>
          <img
            src={thumbnailUrl}
            alt={deal.title}
            className="w-full h-full object-contain hover:scale-105 transition-transform duration-300"
            onError={handleImageError}
          />
        </Link>
        
        {/* Discount badge or FREE badge */}
        {deal.price === 0 ? (
          <div className="absolute top-3 left-3 z-10">
            <div className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-xl animate-pulse-subtle">
              FREE
            </div>
          </div>
        ) : discountPercentage && discountPercentage > 0 && (
          <div className="absolute top-3 left-3 z-10">
            <div className="bg-deal-orange text-white text-xs font-bold px-2 py-1 rounded-xl animate-pulse-subtle">
              {discountPercentage}% OFF
            </div>
          </div>
        )}
        
        {/* "HOT" badge for high temperature deals - Show only if temperature is greater than or equal to GETTING_WARM_TEMPERATURE */}
        {deal.temperature >= GETTING_WARM_TEMPERATURE && (
          <div className="absolute top-3 right-3 z-10">
            <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-xl">
              🔥 HOT
            </div>
          </div>
        )}
        
        {/* Heart/favorite button - Updated with save functionality */}
        <button
          onClick={handleSave}
          className="absolute bottom-3 right-3 p-1.5 rounded-full bg-white/80 hover:bg-white text-gray-500 hover:text-red-500 transition-colors backdrop-blur-sm"
          title={isSaved ? "Remove from saved deals" : "Save deal"}
        >
          {isSaved ? (
            <HeartIconSolid className="h-5 w-5 text-red-500" />
          ) : (
            <HeartIcon className="h-5 w-5" />
          )}
        </button>
      </div>
      
      {/* Content section */}
      <div className="p-3">
        {/* Category badge */}
        <div className="flex gap-1.5 mb-1.5 text-xs">
          {deal.categoryId && deal.categoryName && (
            <button
              onClick={() => updateUrlWithFilter('category', deal.categoryId.toString())}
              className="text-deal-blue bg-deal-blue-light px-2 py-0.5 rounded-sm"
            >
              {deal.categoryName}
            </button>
          )}
          
          {/* Date badge - shows today or formatted date */}
          <span className="text-gray-500 px-2 py-0.5">
            {deal.updatedAt && new Date(deal.updatedAt).toDateString() === new Date().toDateString() 
              ? 'Today' 
              : formattedDate}
          </span>
        </div>
        
        {/* Title */}
        <Link href={`/dealDetail/${deal.id}`} className="block mb-2">
          <h2 className="font-medium text-gray-900 leading-tight line-clamp-2 hover:text-deal-orange transition-colors duration-200">
            {deal.title}
          </h2>
        </Link>
        
        {/* Store name with "from" prefix */}
        {deal.storeId && deal.storeName && (
          <div className="text-sm text-gray-500 mb-2">
            from <button
              onClick={() => updateUrlWithFilter('store', deal.storeId!.toString())}
              className="text-deal-orange hover:underline"
            >
              {deal.storeName}
            </button>
          </div>
        )}
        
        {/* Price section */}
        <div className="flex items-baseline mb-3">
          <span className="text-deal-orange text-xl font-bold mr-2">
            £{deal.price?.toFixed(2)}
          </span>
          {deal.originalPrice && (
            <span className="text-gray-400 text-sm line-through">
              £{deal.originalPrice.toFixed(2)}
            </span>
          )}
        </div>
        
        {/* Footer with temp/votes/comments */}
        <div className="flex items-center justify-between text-sm border-t border-gray-100 pt-2">
          {/* Temperature */}
          <div className="flex items-center gap-2">
            <div className="flex items-center">
              <FireIcon className={`h-4 w-4 ${getTemperatureColor()}`} />
              <span className={`font-medium ${getTemperatureColor()}`}>
                {deal.temperature}°
              </span>
            </div>
            
            {deal.commentCount !== undefined && (
              <div className="flex items-center text-gray-500">
                <ChatBubbleLeftIcon className="h-4 w-4 mr-1" />
                <span>{deal.commentCount}</span>
              </div>
            )}
          </div>
          
          {/* Voting or View Deal */}
          <div className="flex items-center">
            {showActions ? (
              <div className="flex">
                <button
                  onClick={() => handleVote(1)}
                  disabled={deal?.userVote !== undefined}
                  title={deal?.userVote !== undefined ? `You've already voted on this deal` : "Vote up"}
                  className={`p-1 rounded transition-colors ${getUpvoteButtonStyle()} ${deal?.userVote !== undefined ? 'cursor-not-allowed opacity-50' : ''}`}
                >
                  <ChevronUpIcon className="h-5 w-5" />
                </button>
                
                <button
                  onClick={() => handleVote(-1)}
                  disabled={deal?.userVote !== undefined}
                  title={deal?.userVote !== undefined ? `You've already voted on this deal` : "Vote down"}
                  className={`p-1 rounded transition-colors ${getDownvoteButtonStyle()} ${deal?.userVote !== undefined ? 'cursor-not-allowed opacity-50' : ''}`}
                >
                  <ChevronDownIcon className="h-5 w-5" />
                </button>
              </div>
            ) : (
              <Link 
                href={`/dealDetail/${deal.id}`}
                className="text-deal-orange hover:underline text-sm font-medium flex items-center"
              >
                View Deal <LinkIcon className="h-3.5 w-3.5 ml-1" />
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Export with React.memo to prevent unnecessary re-renders when parent components update
export default React.memo(DealCard);
