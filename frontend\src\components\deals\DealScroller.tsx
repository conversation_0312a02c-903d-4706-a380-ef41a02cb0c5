import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';
import { Deal } from '../../types';
import { getThumbnailUrl, handleImageError } from '../../utils/imageUtils';
import DealCard from './DealCard';

interface DealScrollerProps {
  title: string;
  deals: Deal[] | undefined;
  isLoading: boolean;
  viewAllLink?: string;
  icon?: React.ReactNode;
  theme?: 'primary' | 'secondary' | 'accent';
}

const DealScroller: React.FC<DealScrollerProps> = ({ 
  title, 
  deals, 
  isLoading, 
  viewAllLink = '/deals',
  icon,
  theme = 'primary'
}) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [isHovered, setIsHovered] = useState(false);

  // Get theme-specific classes
  const themeClasses = useMemo(() => {
    switch(theme) {
      case 'secondary':
        return {
          text: 'text-secondary-600',
          border: 'border-secondary-500',
          gradient: 'from-secondary-500 to-secondary-600',
          bg: 'bg-secondary-50'
        };
      case 'accent':
        return {
          text: 'text-accent-600',
          border: 'border-accent-500',
          gradient: 'from-accent-500 to-accent-600',
          bg: 'bg-accent-50'
        };
      default:
        return {
          text: 'text-primary-600',
          border: 'border-primary-500',
          gradient: 'from-primary-500 to-primary-600',
          bg: 'bg-primary-50'
        };
    }
  }, [theme]);

  const scroll = useCallback((direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const { current } = scrollRef;
      const scrollAmount = current.clientWidth * 0.8;
      const newScrollLeft = direction === 'left' 
        ? current.scrollLeft - scrollAmount 
        : current.scrollLeft + scrollAmount;
      
      current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  }, []);

  const handleScroll = useCallback(() => {
    if (scrollRef.current) {
      const { current } = scrollRef;
      setShowLeftArrow(current.scrollLeft > 0);
      setShowRightArrow(current.scrollLeft < (current.scrollWidth - current.clientWidth - 10));
    }
  }, []);

  const handleMouseEnter = useCallback(() => setIsHovered(true), []);
  const handleMouseLeave = useCallback(() => setIsHovered(false), []);

  // Check if we need to show right arrow on initial render
  useEffect(() => {
    if (scrollRef.current) {
      const { current } = scrollRef;
      setShowRightArrow(current.scrollWidth > current.clientWidth);
    }
  }, [deals]);

  // Memoize the deal cards to prevent unnecessary re-rendering
  const dealCards = useMemo(() => {
    if (isLoading) {
      return Array.from({ length: 8 }).map((_, index) => (
        <div 
          key={index} 
          className="min-w-[280px] flex-shrink-0 animate-pulse rounded-lg bg-gray-200 h-[220px]"
          style={{ animationDelay: `${index * 0.1}s` }} 
        />
      ));
    }
    
    if (deals && deals.length > 0) {
      return deals.map((deal) => (
        <div key={deal.id} className="min-w-[280px] flex-shrink-0">
          <DealCard deal={deal} showActions={false} />
        </div>
      ));
    }
    
    return (
      <div className="flex w-full items-center justify-center py-8 text-gray-500">
        No deals available
      </div>
    );
  }, [deals, isLoading]);

  return (
    <div 
      className="mb-16 relative" 
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Section header with themed styling */}
      <div className={`mb-6 relative ${themeClasses.bg} rounded-xl p-4 shadow-sm`}>
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            {icon && <span className={`${themeClasses.text}`}>{icon}</span>}
            <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-700">
              {title}
            </h2>
            {/* Animated indicator for hot sections */}
            {title.toLowerCase().includes('hot') || title.toLowerCase().includes('trending') ? (
              <span className="flex h-3 w-3">
                <span className="animate-ping absolute inline-flex h-3 w-3 rounded-full bg-red-400 opacity-75"></span>
                <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
              </span>
            ) : null}
          </div>
          <Link
            to={viewAllLink}
            className={`flex items-center text-sm font-medium ${themeClasses.text} hover:underline transition-all duration-200 transform hover:translate-x-1`}
          >
            View all
            <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </div>
        {/* Decorative accent line */}
        <div className={`absolute bottom-0 left-0 h-1 w-24 rounded-full ${themeClasses.border} bg-gradient-to-r ${themeClasses.gradient}`}></div>
      </div>

      <div className="relative">
        {showLeftArrow && (
          <button 
            onClick={() => scroll('left')}
            className={`absolute left-0 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 transform items-center justify-center rounded-full bg-white shadow-lg transition-all duration-300 ${isHovered ? 'opacity-90 -translate-x-1 hover:bg-gray-50' : 'opacity-70'} hover:opacity-100 focus:outline-none`}
            aria-label="Scroll left"
          >
            <ChevronLeftIcon className={`h-6 w-6 ${themeClasses.text}`} />
          </button>
        )}

        <div 
          ref={scrollRef}
          className="hide-scrollbar flex items-stretch gap-4 overflow-x-auto pb-4 scrollbar-hide pl-1 pr-1"
          onScroll={handleScroll}
        >
          {dealCards}
        </div>

        {showRightArrow && (
          <button 
            onClick={() => scroll('right')}
            className={`absolute right-0 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 transform items-center justify-center rounded-full bg-white shadow-lg transition-all duration-300 ${isHovered ? 'opacity-90 translate-x-1 hover:bg-gray-50' : 'opacity-70'} hover:opacity-100 focus:outline-none`}
            aria-label="Scroll right"
          >
            <ChevronRightIcon className={`h-6 w-6 ${themeClasses.text}`} />
          </button>
        )}
      </div>
    </div>
  );
};

export default React.memo(DealScroller);
