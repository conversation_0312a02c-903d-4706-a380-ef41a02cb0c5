import React, { useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { ArrowUpDown, Filter } from 'lucide-react';
import { Category, Store, DealFilters } from '../../types';
import api from '../../services/api';

interface FilterBarProps {
  filters: DealFilters;
  onFilterChange: (newFilters: DealFilters) => void;
}

const FilterBar: React.FC<FilterBarProps> = ({ filters, onFilterChange }) => {
  const [searchInput, setSearchInput] = useState(filters.search || '');
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [isSortOpen, setIsSortOpen] = useState(false);
  const [tempSort, setTempSort] = useState<DealFilters['sort']>(filters.sort || 'newest');
  
  // Fetch categories with store filter
  const { data: categoriesData } = useQuery(['categories', filters.store], async () => {
    const params = filters.store ? { store: filters.store } : undefined;
    const response = await api.get('/categories', { params });
    return response.data as Category[];
  });
  
  // Fetch stores with category filter
  const { data: storesData } = useQuery(['stores', filters.category], async () => {
    const params = filters.category ? { category: filters.category } : undefined;
    const response = await api.get('/stores', { params });
    return response.data as Store[];
  });
  
  // Update searchInput when filters.search changes
  useEffect(() => {
    setSearchInput(filters.search || '');
  }, [filters.search]);
  
  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onFilterChange({ ...filters, search: searchInput, page: 1 });
  };
  
  // Handle category change
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value === 'all' ? undefined : parseInt(e.target.value, 10);
    onFilterChange({ ...filters, category: value, page: 1 });
  };
  
  // Handle store change
  const handleStoreChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value === 'all' ? undefined : parseInt(e.target.value, 10);
    onFilterChange({ ...filters, store: value, page: 1 });
  };
  
  // Handle status change
  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value === 'all' ? 'all' : e.target.value as 'active' | 'expired';
    onFilterChange({ ...filters, status: value, page: 1 });
  };
  
  // Handle sort change
  const handleSortChange = (sortValue: DealFilters['sort']) => {
    setTempSort(sortValue);
  };
  
  // Handle apply sort
  const handleApplySort = () => {
    onFilterChange({ ...filters, sort: tempSort, page: 1 });
    setIsSortOpen(false);
  };
  
  // Handle clear filters
  const handleClearFilters = () => {
    setSearchInput('');
    onFilterChange({
      page: 1,
      pageSize: filters.pageSize,
      sort: filters.sort, // Preserve the current sort
      status: 'active',
      category: undefined,
      store: undefined,
      search: undefined
    });
  };

  // Get current sort display text
  const getSortDisplayText = () => {
    switch(filters.sort) {
      case 'newest': return 'Newest';
      case 'hottest': return 'Hottest';
      case 'price-asc': return 'Price: Low to High';
      case 'price-desc': return 'Price: High to Low';
      case 'most-commented': return 'Most Commented';
      case 'getting-warm': return 'Getting Warm';
      case 'trending': return 'Trending';
      default: return 'Sort: Newest';
    }
  };
  
  return (
    <div className="w-full">
      {/* Search row with full-width bar and buttons */}
      <div className="mb-4 w-full flex flex-col sm:flex-row items-center gap-2">
        {/* Full-width search bar */}
        <form onSubmit={handleSearchSubmit} className="w-full relative">
          <div className="relative w-full">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              className="block w-full rounded-lg border border-gray-300 px-4 py-3 pl-10 focus:border-primary-500 focus:ring-primary-500 text-sm md:text-base"
              placeholder="Search for amazing deals..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
            />
            {searchInput && (
              <button
                type="button"
                className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                onClick={() => {
                  setSearchInput('');
                  if (filters.search) {
                    onFilterChange({ ...filters, search: undefined, page: 1 });
                  }
                }}
                aria-label="Clear search"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </div>
        </form>
      </div>

      {/* Filter and Sort Row */}
      <div className="flex flex-wrap gap-2 items-center justify-between">
        {/* Search button and filter button */}
        <div className="flex gap-2">
          <button
            type="button"
            onClick={handleSearchSubmit}
            className="inline-flex items-center justify-center rounded-lg bg-primary-500 px-4 py-2 text-sm font-medium text-white hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
          >
            Search
          </button>
          
          <div className="relative">
            <button
              type="button"
              onClick={() => setIsFiltersOpen(!isFiltersOpen)}
              className={`inline-flex items-center rounded-lg border px-4 py-2 text-sm font-medium focus:outline-none transition-colors ${
                isFiltersOpen ? 'bg-primary-50 text-primary-700 border-primary-200' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </button>
            
            {/* Dropdown for filters */}
            {isFiltersOpen && (
              <div className="absolute left-0 z-10 mt-2 w-60 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                <div className="p-4 space-y-4">
                  {/* Close button */}
                  <button
                    type="button"
                    onClick={() => setIsFiltersOpen(false)}
                    className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 focus:outline-none"
                    aria-label="Close filters"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>

                  {/* Category filter */}
                  <div className="space-y-2">
                    <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                      Category
                    </label>
                    <select
                      id="category"
                      className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
                      value={filters.category || 'all'}
                      onChange={handleCategoryChange}
                    >
                      <option value="all">All Categories</option>
                      {categoriesData?.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name} ({category.dealsCount || 0})
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  {/* Store filter */}
                  <div className="space-y-2">
                    <label htmlFor="store" className="block text-sm font-medium text-gray-700">
                      Store
                    </label>
                    <select
                      id="store"
                      className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
                      value={filters.store || 'all'}
                      onChange={handleStoreChange}
                    >
                      <option value="all">All Stores</option>
                      {storesData?.map((store) => (
                        <option key={store.id} value={store.id}>
                          {store.name} ({store.dealsCount || 0})
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  {/* Status filter */}
                  <div className="space-y-2">
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                      Status
                    </label>
                    <select
                      id="status"
                      className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500"
                      value={filters.status || 'active'}
                      onChange={handleStatusChange}
                    >
                      <option value="active">Active</option>
                      <option value="expired">Expired</option>
                      <option value="all">All</option>
                    </select>
                  </div>
                  
                  {/* Clear button */}
                  <button
                    type="button"
                    onClick={() => {
                      handleClearFilters();
                      setIsFiltersOpen(false);
                    }}
                    className="w-full mt-2 py-2 rounded-md bg-gray-100 text-sm font-medium text-gray-700 hover:bg-gray-200 transition-colors"
                  >
                    Clear Filters
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Sort dropdown */}
        <div className="relative">
          <button
            type="button"
            onClick={() => setIsSortOpen(!isSortOpen)}
            className="inline-flex items-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none transition-colors"
          >
            <ArrowUpDown className="mr-2 h-4 w-4" />
            {getSortDisplayText()}
          </button>
          
          {isSortOpen && (
            <div className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="py-1">
                <button
                  onClick={() => handleSortChange('newest')}
                  className={`block w-full px-4 py-2 text-left text-sm ${tempSort === 'newest' ? 'bg-gray-100 text-primary-700 font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
                >
                  Newest
                </button>
                <button
                  onClick={() => handleSortChange('hottest')}
                  className={`block w-full px-4 py-2 text-left text-sm ${tempSort === 'hottest' ? 'bg-gray-100 text-primary-700 font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
                >
                  Hottest
                </button>
                <button
                  onClick={() => handleSortChange('getting-warm')}
                  className={`block w-full px-4 py-2 text-left text-sm ${tempSort === 'getting-warm' ? 'bg-gray-100 text-primary-700 font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
                >
                  Getting Warm
                </button>
                <button
                  onClick={() => handleSortChange('price-asc')}
                  className={`block w-full px-4 py-2 text-left text-sm ${tempSort === 'price-asc' ? 'bg-gray-100 text-primary-700 font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
                >
                  Price: Low to High
                </button>
                <button
                  onClick={() => handleSortChange('price-desc')}
                  className={`block w-full px-4 py-2 text-left text-sm ${tempSort === 'price-desc' ? 'bg-gray-100 text-primary-700 font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
                >
                  Price: High to Low
                </button>
                <button
                  onClick={() => handleSortChange('most-commented')}
                  className={`block w-full px-4 py-2 text-left text-sm ${tempSort === 'most-commented' ? 'bg-gray-100 text-primary-700 font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
                >
                  Most Commented
                </button>
                <button
                  onClick={() => handleSortChange('trending')}
                  className={`block w-full px-4 py-2 text-left text-sm ${tempSort === 'trending' ? 'bg-gray-100 text-primary-700 font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
                >
                  Trending
                </button>
                <div className="border-t border-gray-100 mt-1 pt-1 px-3 py-2">
                  <button
                    onClick={handleApplySort}
                    className="w-full rounded-md bg-primary-500 px-3 py-1.5 text-sm font-medium text-white hover:bg-primary-600 transition-colors"
                  >
                    Apply Sort
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Active filters display */}
      {(filters.search || filters.category || filters.store || filters.status !== 'active') && (
        <div className="mt-4 flex flex-wrap gap-2">
          {filters.search && (
            <span className="inline-flex items-center rounded-full bg-primary-100 px-3 py-1 text-xs font-medium text-primary-800">
              Search: {filters.search}
              <button
                type="button"
                className="ml-1.5 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-primary-800 hover:text-primary-900 focus:outline-none"
                onClick={() => onFilterChange({ ...filters, search: undefined, page: 1 })}
              >
                <span className="sr-only">Remove search filter</span>
                <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                  <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
                </svg>
              </button>
            </span>
          )}
          
          {filters.category && categoriesData && (
            <span className="inline-flex items-center rounded-full bg-secondary-100 px-3 py-1 text-xs font-medium text-secondary-800">
              Category: {categoriesData.find(c => c.id === filters.category)?.name || 'Unknown'}
              <button
                type="button"
                className="ml-1.5 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-secondary-800 hover:text-secondary-900 focus:outline-none"
                onClick={() => onFilterChange({ ...filters, category: undefined, page: 1 })}
              >
                <span className="sr-only">Remove category filter</span>
                <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                  <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
                </svg>
              </button>
            </span>
          )}
          
          {filters.store && storesData && (
            <span className="inline-flex items-center rounded-full bg-accent-100 px-3 py-1 text-xs font-medium text-accent-800">
              Store: {storesData.find(s => s.id === filters.store)?.name || 'Unknown'}
              <button
                type="button"
                className="ml-1.5 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-accent-800 hover:text-accent-900 focus:outline-none"
                onClick={() => onFilterChange({ ...filters, store: undefined, page: 1 })}
              >
                <span className="sr-only">Remove store filter</span>
                <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                  <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
                </svg>
              </button>
            </span>
          )}
          
          {filters.status && filters.status !== 'active' && (
            <span className="inline-flex items-center rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-800">
              Status: {filters.status.charAt(0).toUpperCase() + filters.status.slice(1)}
              <button
                type="button"
                className="ml-1.5 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-gray-800 hover:text-gray-900 focus:outline-none"
                onClick={() => onFilterChange({ ...filters, status: 'active', page: 1 })}
              >
                <span className="sr-only">Remove status filter</span>
                <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                  <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
                </svg>
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default FilterBar;
