import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { 
  MessageSquare, 
  Calendar, 
  Heart, 
  ExternalLink
} from 'lucide-react';
import { Deal } from '../../types';
import { getThumbnailUrl, getFullImageUrl, handleImageError } from '@/utils/imageUtils';
import { getAffiliateUrl } from '../../utils/formatters';
import { saveDeal, unsaveDeal, checkIfDealSaved } from '../../services/dealService';
import { useAuth } from '../../hooks/useAuth';
import toast from 'react-hot-toast';

// Hot threshold for deal temperature
const GETTING_WARM_TEMPERATURE = 1; // We'll use this as default since it's 1 in .env

interface GridDealCardProps {
  deal: Deal;
}

const GridDealCard: React.FC<GridDealCardProps> = ({ deal }) => {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const [isSaved, setIsSaved] = useState(false);
  
  // Check if deal is saved when component mounts
  useEffect(() => {
    const checkSavedStatus = async () => {
      if (isAuthenticated && deal.id && user?.id) {
        try {
          const saved = await checkIfDealSaved(deal.id, user.id);
          setIsSaved(saved);
        } catch (error) {
          console.error('Error checking saved status:', error);
          setIsSaved(false);
        }
      }
    };
    
    checkSavedStatus();
  }, [isAuthenticated, deal.id, user?.id]);

  // Handle save deal
  const handleSave = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent link/navigation
    e.stopPropagation(); // Prevent event bubbling
    
    if (!isAuthenticated || !user?.id) {
      toast.error('Please log in to save deals');
      return;
    }

    try {
      if (isSaved) {
        await unsaveDeal(deal.id, user.id);
      } else {
        await saveDeal(deal.id, user.id);
      }
      setIsSaved(!isSaved);
      toast.success(isSaved ? "Removed from saved deals" : "Deal saved successfully");
    } catch (error: any) {
      toast.error(error.message || 'Failed to save deal');
    }
  };
  
  // Format the discount percentage
  const discountPercentage = deal.originalPrice && deal.price
    ? Math.round(((deal.originalPrice - deal.price) / deal.originalPrice) * 100)
    : null;
  
  // Format the date
  const formattedDate = deal.updatedAt ? 
    format(new Date(deal.updatedAt), 'MMM d, yyyy') : 
    'Just now';
  
  // Date display logic
  const displayDate = deal.updatedAt && new Date(deal.updatedAt).toDateString() === new Date().toDateString() 
    ? 'Today' 
    : formattedDate;
  
  // Check if deal is expired
  const isExpired = deal.status === 'expired' || 
    (deal.expiresAt && typeof deal.expiresAt === 'string' && new Date(deal.expiresAt) < new Date());
  
  // Handle category click
  const handleCategoryClick = () => {
    if (deal.categoryId) {
      navigate(`/dealsBrowse?category=${deal.categoryId}&status=active&sort=newest`);
    }
  };

  // Handle store click
  const handleStoreClick = () => {
    if (deal.storeId) {
      navigate(`/dealsBrowse?store=${deal.storeId}&status=active&sort=newest`);
    }
  };
  
  return (
    <div className="deal-card card-hover">
      {/* Image section */}
      <div className="relative overflow-hidden h-48">
        <Link to={`/dealDetail/${deal.id}`}>
          <img 
            src={getFullImageUrl(deal)} 
            alt={deal.title} 
            className="deal-image"
            onError={handleImageError}
          />
        </Link>
        
        {/* Discount badge or FREE badge */}
        {deal.price === 0 ? (
          <div className="absolute top-3 left-3 z-10">
            <div className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-xl animate-pulse-subtle">
              FREE
            </div>
          </div>
        ) : discountPercentage && discountPercentage > 0 && (
          <div className="absolute top-3 left-3 z-10">
            <div className="deal-badge deal-badge-primary animate-pulse-subtle">
              {discountPercentage}% OFF
            </div>
          </div>
        )}
        
        {/* "HOT" badge for high temperature deals */}
        {deal.temperature >= GETTING_WARM_TEMPERATURE && (
          <div className="absolute top-3 right-3 z-10">
            <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-xl">
              🔥 HOT
            </div>
          </div>
        )}
        
        {/* Heart/favorite button - updated with save functionality */}
        <button 
          className="absolute bottom-3 right-3 p-2 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white transition-colors"
          onClick={handleSave}
          title={isSaved ? "Remove from saved deals" : "Save deal"}
        >
          <Heart 
            className={`w-4 h-4 ${isSaved ? 'fill-red-500 text-red-500' : 'text-gray-600 hover:text-red-500'} transition-colors`} 
          />
        </button>
      </div>
      
      {/* Content section */}
      <div className="p-4">
        {/* Category and Date row */}
        <div className="flex items-center justify-between mb-2">
          <button 
            onClick={handleCategoryClick}
            className="text-xs font-medium text-deal-blue bg-deal-blue-light px-2 py-1 rounded-full hover:bg-deal-blue hover:text-white transition-colors"
          >
            {deal.categoryName}
          </button>
          
          <div className="text-xs text-gray-500 flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            {displayDate}
          </div>
        </div>
        
        {/* Title */}
        <Link to={`/dealDetail/${deal.id}`}>
          <h3 className="deal-title mb-2">{deal.title}</h3>
        </Link>
        
        {/* Store name */}
        <div className="text-xs text-gray-500 mb-3">
          from <button
            onClick={handleStoreClick}
            className="font-medium text-gray-700 hover:text-deal-orange hover:underline transition-colors"
          >
            {deal.storeName}
          </button>
        </div>
        
        {/* Price section */}
        <div className="flex items-end gap-2 mb-3">
          <div className="deal-price">£{deal.price?.toFixed(2)}</div>
          {deal.originalPrice && deal.price !== undefined && deal.originalPrice > deal.price && (
            <div className="deal-original-price">£{deal.originalPrice.toFixed(2)}</div>
          )}
        </div>
        
        {/* Footer with comments count and view deal button */}
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <MessageSquare className="w-3 h-3" />
            <span>{deal.commentCount || 0} comments</span>
          </div>
          
          <Link 
            to={`/dealDetail/${deal.id}`} 
            className="flex items-center gap-1 text-deal-orange hover:text-deal-orange-dark text-sm font-medium transition-colors"
          >
            <span>View Deal</span>
            <ExternalLink className="w-3 h-3" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default GridDealCard; 