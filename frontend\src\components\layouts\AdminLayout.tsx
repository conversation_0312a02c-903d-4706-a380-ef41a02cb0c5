import React, { useState } from 'react';
import { Outlet, Link, useNavigate, useLocation } from 'react-router-dom';
import {
  Bars3Icon,
  ChartBarIcon,
  Cog6ToothIcon,
  FireIcon,
  HomeIcon,
  ServerIcon,
  TagIcon,
  UserGroupIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../../hooks/useAuth';

const AdminLayout: React.FC = () => {
  const { logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: ChartBarIcon, current: location.pathname === '/admin' },
    { name: 'Deals', href: '/admin/deals', icon: TagIcon, current: location.pathname === '/admin/deals' },
    { name: 'Users', href: '/admin/users', icon: UserGroupIcon, current: location.pathname === '/admin/users' },
    { name: 'Scrapers', href: '/admin/scrapers', icon: ServerIcon, current: location.pathname === '/admin/scrapers' },
    { name: 'Back to Site', href: '/', icon: HomeIcon, current: false },
  ];
  
  const handleLogout = () => {
    logout();
    navigate('/login');
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      <div className="hidden md:fixed md:inset-y-0 md:flex md:w-64 md:flex-col">
        <div className="flex min-h-0 flex-1 flex-col border-r border-gray-200 bg-white">
          <div className="flex flex-1 flex-col overflow-y-auto pt-5 pb-4">
            <div className="flex flex-shrink-0 items-center px-4">
              <Link to="/admin" className="flex items-center">
                <FireIcon className="h-8 w-8 text-primary-600" />
                <span className="ml-2 text-lg font-bold text-gray-900">NiceDeals Admin</span>
              </Link>
            </div>
            <nav className="mt-5 flex-1 space-y-1 bg-white px-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`group flex items-center rounded-md px-2 py-2 text-sm font-medium ${
                    item.current
                      ? 'bg-primary-50 text-primary-600'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <item.icon
                    className={`mr-3 h-6 w-6 flex-shrink-0 ${
                      item.current ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                    }`}
                    aria-hidden="true"
                  />
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
          <div className="flex flex-shrink-0 border-t border-gray-200 p-4">
            <button
              onClick={handleLogout}
              className="group block w-full flex-shrink-0 rounded-md px-2 py-2 text-left text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900"
            >
              <div className="flex items-center">
                <Cog6ToothIcon className="mr-3 h-6 w-6 text-gray-400 group-hover:text-gray-500" />
                <span>Sign out</span>
              </div>
            </button>
          </div>
        </div>
      </div>
      
      {/* Mobile sidebar */}
      <div className="fixed inset-0 z-40 flex md:hidden" role="dialog" aria-modal="true">
        {/* Sidebar backdrop */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-gray-600 bg-opacity-75"
            aria-hidden="true"
            onClick={() => setSidebarOpen(false)}
          />
        )}
        
        {/* Sidebar */}
        <div
          className={`fixed inset-0 z-40 flex transform transition-transform ${
            sidebarOpen ? 'translate-x-0' : '-translate-x-full'
          }`}
        >
          <div className="relative flex w-full max-w-xs flex-1 flex-col bg-white pb-4">
            {/* Close button */}
            <div className="absolute top-0 right-0 -mr-12 pt-2">
              <button
                type="button"
                className="ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                onClick={() => setSidebarOpen(false)}
              >
                <span className="sr-only">Close sidebar</span>
                <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
              </button>
            </div>
            
            {/* Logo */}
            <div className="flex flex-shrink-0 items-center px-4 pt-5">
              <Link to="/admin" className="flex items-center" onClick={() => setSidebarOpen(false)}>
                <FireIcon className="h-8 w-8 text-primary-600" />
                <span className="ml-2 text-lg font-bold text-gray-900">NiceDeals Admin</span>
              </Link>
            </div>
            
            {/* Navigation */}
            <div className="mt-5 h-0 flex-1 overflow-y-auto">
              <nav className="space-y-1 px-2">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`group flex items-center rounded-md px-2 py-2 text-base font-medium ${
                      item.current
                        ? 'bg-primary-50 text-primary-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <item.icon
                      className={`mr-4 h-6 w-6 flex-shrink-0 ${
                        item.current ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                      }`}
                      aria-hidden="true"
                    />
                    {item.name}
                  </Link>
                ))}
              </nav>
            </div>
            
            {/* Logout button */}
            <div className="flex flex-shrink-0 border-t border-gray-200 p-4">
              <button
                onClick={() => {
                  handleLogout();
                  setSidebarOpen(false);
                }}
                className="group block w-full flex-shrink-0 rounded-md px-2 py-2 text-left text-base font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              >
                <div className="flex items-center">
                  <Cog6ToothIcon className="mr-4 h-6 w-6 text-gray-400 group-hover:text-gray-500" />
                  <span>Sign out</span>
                </div>
              </button>
            </div>
          </div>
          <div className="w-14 flex-shrink-0" aria-hidden="true"></div>
        </div>
      </div>
      
      {/* Mobile header */}
      <div className="sticky top-0 z-10 flex h-16 flex-shrink-0 md:hidden">
        <div className="flex flex-1 items-center justify-between bg-white px-4 shadow">
          <div className="flex items-center">
            <button
              type="button"
              className="inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <span className="sr-only">Open sidebar</span>
              <Bars3Icon className="h-6 w-6" aria-hidden="true" />
            </button>
            <div className="ml-4">
              <Link to="/admin" className="flex items-center">
                <FireIcon className="h-8 w-8 text-primary-600" />
                <span className="ml-2 text-lg font-bold text-gray-900">NiceDeals Admin</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
      
      <div className="md:pl-64">
        <main className="flex-1">
          <div className="py-6">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
              <Outlet />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
