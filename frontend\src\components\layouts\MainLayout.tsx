import React, { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { Dialog, Disclosure, Menu, Transition } from '@headlessui/react';
import {
  Bars3Icon,
  XMarkIcon,
  UserIcon,
  FireIcon,
  HomeIcon,
  TagIcon,
  MagnifyingGlassIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  ShieldCheckIcon,
  ShoppingBagIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  TvIcon,
  CameraIcon,
  HomeModernIcon,
  WrenchScrewdriverIcon,
  HeartIcon,
  MusicalNoteIcon,
  BookOpenIcon,
  TruckIcon,
  GiftIcon,
  SparklesIcon,
  BuildingStorefrontIcon,
  GlobeAltIcon,
  RocketLaunchIcon,
  SwatchIcon,
  PhoneIcon,
  UserGroupIcon,
  BeakerIcon,
  QueueListIcon,
  BriefcaseIcon,
  BookmarkIcon,
  HandThumbUpIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../hooks/useAuth';
import { categoryService } from '../../services/categoryService';
import { Category } from '../../types';

// Export this function so it can be imported in other components
export const getCategoryIcon = (categoryName: string) => {
  const iconMap: { [key: string]: React.ForwardRefExoticComponent<any> } = {
    'electronics': DevicePhoneMobileIcon,
    'computers': ComputerDesktopIcon,
    'gaming': RocketLaunchIcon,
    'home & garden': HomeModernIcon,
    'clothing & accessories': SwatchIcon,
    'beauty & health': HeartIcon,
    'toys & kids': GiftIcon,
    'books & media': BookOpenIcon,
    'food & drink': BuildingStorefrontIcon,
    'travel': GlobeAltIcon,
    'sports & outdoors': RocketLaunchIcon,
    'automotive': TruckIcon,
    'services': BriefcaseIcon,
    'entertainment': MusicalNoteIcon,
    'home improvement': WrenchScrewdriverIcon,
    'office supplies': BriefcaseIcon,
    'groceries': BuildingStorefrontIcon,
    'health & beauty': HeartIcon,
    'fashion & accessories': SwatchIcon,
    'culture & leisure': SparklesIcon,
    'broadband & phone contracts': PhoneIcon,
    'home & living': HomeIcon,
    'garden & do it yourself': WrenchScrewdriverIcon,
    'family & kids': UserGroupIcon,
    'other': QueueListIcon
  };

  // Convert category name to lowercase for matching
  const normalizedName = categoryName.toLowerCase();
  
  // Get the icon component
  const IconComponent = iconMap[normalizedName] || ShoppingBagIcon;
  
  // Return the JSX element
  return <IconComponent className="mr-3 h-5 w-5 text-gray-400" />;
};

const MainLayout: React.FC = () => {
  const { isAuthenticated, user, logout } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const location = useLocation();
  const navigate = useNavigate();
  const [categories, setCategories] = useState<Category[]>([]);

  // Simple function to check if a link should be active
  const isLinkActive = (path: string) => {
    // Parse the current URL's search params
    const currentParams = new URLSearchParams(window.location.search);
    // Parse the link's search params
    const linkParams = new URLSearchParams(path.split('?')[1] || '');
    
    // For "Getting Warm" and "Hottest", we only care about the sort parameter
    if (path.includes('sort=getting-warm') || path.includes('sort=hottest')) {
      return currentParams.get('sort') === linkParams.get('sort');
    }
    
    // For "Browse Deals", we want an exact match of both parameters
    if (path.includes('sort=newest')) {
      return currentParams.get('sort') === 'newest' && currentParams.get('status') === 'active';
    }
    
    return false;
  };

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const data = await categoryService.getCategories();
        setCategories(data);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };
    fetchCategories();
  }, []);
  
  // Check if user has admin access (userId 1 or 2)
  const hasAdminAccess = isAuthenticated && user && (user.id === 1 || user.id === 2);
  
  const navigation = [
    { name: 'Home', href: '/', icon: HomeIcon, current: location.pathname === '/' },
    { name: 'Browse Deals', href: '/dealsBrowse', icon: TagIcon, current: location.pathname === '/dealsBrowse' },
  ];
  
  // Add Admin link to navigation if user has admin access
  if (hasAdminAccess) {
    navigation.push({ 
      name: 'Admin', 
      href: '/admin', 
      icon: ShieldCheckIcon, 
      current: location.pathname.startsWith('/admin') 
    });
  }
  
  const userNavigation = [
    { name: 'Your Profile', href: '/user/profile', icon: UserCircleIcon },
    { name: 'Your Deals', href: '/user/deals', icon: TagIcon },
    { name: 'Your Saved Deals', href: '/user/saved-deals', icon: BookmarkIcon },
    { name: 'Your Comments', href: '/user/comments', icon: TagIcon },
  ];
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Redirect to deals browse page with search query
    navigate(`/dealsBrowse?search=${encodeURIComponent(searchQuery)}`, { replace: false });
  };
  
  return (
    <div className="min-h-screen">
      {/* Header - updated to match Loveable design */}
      <header className="sticky top-0 z-50 glass border-b border-white/30">
        <div className="container mx-auto px-0">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <div className="flex items-center">
              <Link to="/" className="flex items-center">
                <img src="/nicedeals-logo.png" alt="NiceDeals" className="h-10 object-contain" />
              </Link>
            </div>
            
            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-6" aria-label="Main navigation">
              <a
                href="/dealsBrowse?status=active&sort=newest"
                className={`nav-link ${isLinkActive('/dealsBrowse?status=active&sort=newest') ? 'active' : ''}`}
              >
                Browse Deals
              </a>
              <a
                href="/dealsBrowse?status=active&sort=getting-warm"
                className={`nav-link ${isLinkActive('/dealsBrowse?sort=getting-warm') ? 'active' : ''}`}
              >
                Getting Warm
              </a>
              <a
                href="/dealsBrowse?status=active&sort=hottest"
                className={`nav-link ${isLinkActive('/dealsBrowse?sort=hottest') ? 'active' : ''}`}
              >
                Hottest
              </a>

              {hasAdminAccess && (
                <Link 
                  to="/admin" 
                  className={`nav-link ${location.pathname.startsWith('/admin') ? 'active' : ''}`}
                >
                  Admin
                </Link>
              )}
            </nav>
            
            {/* Search Form */}
            <form className="hidden md:flex items-center flex-1 max-w-md mx-4" onSubmit={handleSearch}>
              <div className="relative w-full">
                
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search for deals..."
                  className="block w-full rounded-lg border border-gray-300 px-4 py-3 pl-10 focus:border-primary-500 focus:ring-primary-500 text-sm md:text-base"
                />
                <button
                  type="submit"
                  className="absolute inset-y-0 right-0 flex items-center pr-3"
                >
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </button>
              </div>
            </form>
            
            {/* User Navigation */}
            <div className="flex items-center">
              {!isAuthenticated ? (
                <div className="flex items-center space-x-2">
                  <Link
                    to="/login"
                    className="btn-tertiary py-1.5 px-4 text-sm"
                  >
                    Sign in
                  </Link>
                  <Link
                    to="/register"
                    className="btn-primary-new py-1.5 px-4 text-sm"
                  >
                    Join Free
                  </Link>
                </div>
              ) : (
                <Menu as="div" className="relative ml-3">
                  <div>
                    <Menu.Button className="flex rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-white/50">
                      <span className="sr-only">Open user menu</span>
                      <div className="h-8 w-8 rounded-full bg-gradient-to-r from-deal-orange to-deal-orange-dark text-white flex items-center justify-center font-medium">
                        {user?.username?.charAt(0).toUpperCase() || 'U'}
                      </div>
                    </Menu.Button>
                  </div>
                  <Transition
                    as={React.Fragment}
                    enter="transition ease-out duration-100"
                    enterFrom="transform opacity-0 scale-95"
                    enterTo="transform opacity-100 scale-100"
                    leave="transition ease-in duration-75"
                    leaveFrom="transform opacity-100 scale-100"
                    leaveTo="transform opacity-0 scale-95"
                  >
                    <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right glass rounded-md shadow-lg py-1 focus:outline-none">
                      <div className="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
                        Signed in as<br /><span className="font-semibold">{user?.username}</span>
                      </div>
                      {userNavigation.map((item) => (
                        <Menu.Item key={item.name}>
                          {({ active }) => (
                            <Link
                              to={item.href}
                              className={`${
                                active ? 'bg-white/50' : ''
                              } flex items-center px-4 py-2 text-sm text-gray-700 w-full text-left`}
                            >
                              <item.icon className="mr-3 h-5 w-5 text-gray-400" />
                              {item.name}
                            </Link>
                          )}
                        </Menu.Item>
                      ))}
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            onClick={logout}
                            className={`${
                              active ? 'bg-white/50' : ''
                            } flex items-center px-4 py-2 text-sm text-gray-700 w-full text-left`}
                          >
                            <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-400" />
                            Sign out
                          </button>
                        )}
                      </Menu.Item>
                    </Menu.Items>
                  </Transition>
                </Menu>
              )}
              
              {/* Mobile menu button */}
              <button
                type="button"
                className="md:hidden inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 ml-4"
                onClick={() => setMobileMenuOpen(true)}
              >
                <span className="sr-only">Open main menu</span>
                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>
          </div>
        </div>
      </header>
      
      {/* Mobile menu */}
      <Dialog
        as="div"
        className="md:hidden"
        open={mobileMenuOpen}
        onClose={setMobileMenuOpen}
      >
        <div className="fixed inset-0 z-40 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
        <Dialog.Panel className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto glass px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center" onClick={() => setMobileMenuOpen(false)}>
              <img src="/nicedeals-logo.png" alt="NiceDeals" className="h-10 object-contain" />
            </Link>
            <button
              type="button"
              className="-m-2.5 rounded-md p-2.5 text-gray-700"
              onClick={() => setMobileMenuOpen(false)}
            >
              <span className="sr-only">Close menu</span>
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
          
          {/* Mobile Search */}
          <form className="mt-6" onSubmit={handleSearch}>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search for deals..."
                className="search-input pl-10 pr-4 py-2 w-full"
              />
              <button
                type="submit"
                className="absolute inset-y-0 right-0 flex items-center pr-3"
              >
                <ArrowRightOnRectangleIcon className="h-5 w-5 text-gray-400" />
              </button>
            </div>
          </form>
          
          <div className="mt-6 flow-root">
            <div className="-my-6 divide-y divide-gray-500/10">
              <div className="space-y-2 py-6">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`-mx-3 flex items-center rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50 ${
                      item.current ? 'text-deal-orange' : ''
                    }`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <item.icon className="mr-3 h-6 w-6 flex-shrink-0 text-gray-400" />
                    {item.name}
                  </Link>
                ))}
                
                <Link
                  to="/dealsBrowse?sort=hottest"
                  className="-mx-3 flex items-center rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <FireIcon className="mr-3 h-6 w-6 flex-shrink-0 text-gray-400" />
                  Hot Deals
                </Link>
                
                <Link
                  to="/dealsBrowse?sort=newest"
                  className="-mx-3 flex items-center rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <SparklesIcon className="mr-3 h-6 w-6 flex-shrink-0 text-gray-400" />
                  New Arrivals
                </Link>
              </div>
              
              <div className="py-6">
                {!isAuthenticated ? (
                  <>
                    <Link
                      to="/login"
                      className="-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Sign in
                    </Link>
                    <Link
                      to="/register"
                      className="-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Create account
                    </Link>
                  </>
                ) : (
                  <>
                    {userNavigation.map((item) => (
                      <Link
                        key={item.name}
                        to={item.href}
                        className="-mx-3 flex items-center rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <item.icon className="mr-3 h-6 w-6 flex-shrink-0 text-gray-400" />
                        {item.name}
                      </Link>
                    ))}
                    <button
                      onClick={() => {
                        logout();
                        setMobileMenuOpen(false);
                      }}
                      className="-mx-3 flex w-full items-center rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                    >
                      <ArrowRightOnRectangleIcon className="mr-3 h-6 w-6 flex-shrink-0 text-gray-400" />
                      Sign out
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </Dialog.Panel>
      </Dialog>

      {/* Main content - updated to remove sidebar */}
      <div className="container mx-auto px-0">
        <main className="py-8">
          <Outlet />
        </main>
      </div>
      
      {/* Footer - updated to match Loveable design */}
      <footer className="glass py-10 border-t border-white/20">
        <div className="container mx-auto px-0">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-10">
            <div>
              <h3 className="font-display font-semibold text-gray-800 mb-4">About NiceDeals</h3>
              <p className="text-gray-600 text-sm">
                NiceDeals helps you discover amazing discounts, share great offers, and never miss a bargain again.
              </p>
            </div>
            
            <div>
              <h3 className="font-display font-semibold text-gray-800 mb-4">Quick Links</h3>
              <ul className="space-y-2 text-sm">
                <li><Link to="/dealsBrowse" className="text-gray-600 hover:text-deal-orange">Browse Deals</Link></li>
                <li><Link to="/dealsBrowse" className="text-gray-600 hover:text-deal-orange">Categories</Link></li>
                <li><Link to="/dealsBrowse?sort=hottest" className="text-gray-600 hover:text-deal-orange">Popular Deals</Link></li>
                <li><Link to="/dealsBrowse" className="text-gray-600 hover:text-deal-orange">Merchants</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-display font-semibold text-gray-800 mb-4">Customer Support</h3>
              <ul className="space-y-2 text-sm">
                <li><Link to="/" className="text-gray-600 hover:text-deal-orange">Help Center</Link></li>
                <li><Link to="/" className="text-gray-600 hover:text-deal-orange">Contact Us</Link></li>
                <li><Link to="/" className="text-gray-600 hover:text-deal-orange">FAQ</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-display font-semibold text-gray-800 mb-4">Subscribe</h3>
              <p className="text-gray-600 text-sm mb-3">
                Get the latest deals delivered to your inbox.
              </p>
              <div className="flex">
                <input 
                  type="email" 
                  placeholder="Your email" 
                  className="search-input flex-1 rounded-r-none"
                />
                <button className="btn-primary-new rounded-l-none">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
          
          <div className="mt-10 pt-6 border-t border-gray-200 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 text-sm">
              &copy; {new Date().getFullYear()} NiceDeals. All rights reserved.
            </p>
            
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link to="/" className="text-gray-500 text-sm hover:text-deal-orange">Terms</Link>
              <Link to="/" className="text-gray-500 text-sm hover:text-deal-orange">Privacy</Link>
              <Link to="/" className="text-gray-500 text-sm hover:text-deal-orange">Cookies</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default MainLayout;
