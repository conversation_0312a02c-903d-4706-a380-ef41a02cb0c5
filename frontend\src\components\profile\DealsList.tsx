import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import { getUserDeals, getUserSavedDeals } from '../../services/userService';
import { Deal } from '../../types';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/solid';
import { formatDistanceToNow } from 'date-fns';
import { getDealImage, handleImageError } from '../../utils/imageUtils';
import { useAuth } from '../../hooks/useAuth';

interface DealsListProps {
  userId: number;
  type: 'posted' | 'saved';
}

const DealsList: React.FC<DealsListProps> = ({ userId, type }) => {
  const { user: currentUser } = useAuth();
  
  // Query to fetch deals based on type
  const {
    data: deals,
    isLoading,
    isError,
    error,
  } = useQuery<Deal[]>(
    [type === 'posted' ? 'userDeals' : 'savedDeals', userId],
    () => type === 'posted' ? getUserDeals(userId) : getUserSavedDeals(userId),
    {
      enabled: !!userId,
      staleTime: 1000 * 60, // 1 minute
    }
  );

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-48 rounded-t-lg bg-gray-200"></div>
            <div className="space-y-2 rounded-b-lg border border-t-0 border-gray-200 p-4">
              <div className="h-4 w-3/4 rounded bg-gray-200"></div>
              <div className="h-4 w-1/2 rounded bg-gray-200"></div>
              <div className="h-4 w-full rounded bg-gray-200"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="py-4 text-center text-red-500">
        Error loading deals: {(error as Error).message}
      </div>
    );
  }

  if (!deals || deals.length === 0) {
    return (
      <div className="py-8 text-center">
        <p className="text-gray-500">
          {type === 'posted' 
            ? "You haven't posted any deals yet." 
            : "You haven't saved any deals yet."}
        </p>
        <Link
          to="/deals/new"
          className="mt-4 inline-block rounded-md bg-primary px-4 py-2 text-white hover:bg-primary-dark"
        >
          {type === 'posted' ? 'Post a Deal' : 'Browse Deals'}
        </Link>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
      {deals.map((deal) => {
        // For debugging
        console.log('Deal:', {
          id: deal.id,
          status: deal.status,
          userId: deal.userId,
          currentUserId: currentUser?.id,
          imageUrl: deal.imageUrl,
          thumbnailUrl: deal.thumbnailUrl
        });
        
        return (
          <div key={deal.id} className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm">
            <div className="relative h-48 w-full">
              <img
                src={getDealImage(deal, {
                  currentUserId: currentUser?.id,
                  useThumbnail: true
                })}
                alt={deal.title}
                className="h-full w-full object-cover"
                onError={handleImageError}
              />
              {deal.status === 'pending' && (
                <div className="absolute top-0 right-0 bg-yellow-500 px-2 py-1 text-xs font-semibold text-white">
                  Pending
                </div>
              )}
            </div>
            
            <div className="p-4">
              <div className="mb-2 flex items-center justify-between">
                <span className="rounded bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
                  {typeof deal.store === 'object' ? deal.store.name : deal.store || 'Unknown Store'}
                </span>
                <div className="flex items-center space-x-2">
                  <span className="flex items-center text-sm font-medium">
                    <ArrowUpIcon className="mr-1 h-4 w-4 text-green-500" />
                    {deal.temperature > 0 ? deal.temperature : 0}
                  </span>
                  <span className="flex items-center text-sm font-medium">
                    <ArrowDownIcon className="mr-1 h-4 w-4 text-red-500" />
                    {deal.temperature < 0 ? Math.abs(deal.temperature) : 0}
                  </span>
                </div>
              </div>
              
              <div className="flex-1">
                <Link to={`/dealDetail/${deal.id}`} className="hover:text-primary">
                  <h3 className="text-lg font-medium text-gray-900">{deal.title}</h3>
                </Link>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-lg font-bold text-primary">
                  {deal.price ? `£${deal.price.toFixed(2)}` : 'Free'}
                </span>
                <span className="text-xs text-gray-500">
                  {deal.createdAt ? formatDistanceToNow(new Date(deal.createdAt), { addSuffix: true }) : ''}
                </span>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default DealsList;
