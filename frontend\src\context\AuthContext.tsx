'use client';

import React, { createContext, useReducer, useEffect } from 'react';
import { AuthState, User, ApiResponse } from '../types';
import api from '../services/api';

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: true,
  error: null,
};

// Action types
type AuthAction =
  | { type: 'LOGIN_REQUEST' }
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'REGISTER_REQUEST' }
  | { type: 'REGISTER_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'REGISTER_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: User };

// Auth context type
interface AuthContextType extends AuthState {
  login: (credential: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string, termsAccepted: boolean) => Promise<void>;
  logout: () => void;
  updateUser: (user: User) => void;
}

// Create context
export const AuthContext = createContext<AuthContextType>({
  ...initialState,
  login: async () => {},
  register: async () => {},
  logout: () => {},
  updateUser: () => {},
});

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_REQUEST':
    case 'REGISTER_REQUEST':
      return {
        ...state,
        loading: true,
        error: null,
      };
    case 'LOGIN_SUCCESS':
    case 'REGISTER_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        loading: false,
        error: null,
      };
    case 'LOGIN_FAILURE':
    case 'REGISTER_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: null,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload,
      };
    default:
      return state;
  }
};

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check if user is logged in on initial load
  useEffect(() => {
    const loadUser = async () => {
      const token = localStorage.getItem('token');
      
      if (!token) {
        dispatch({ type: 'LOGOUT' });
        return;
      }
      
      try {
        // Set token in API headers
        api.setAuthToken(token);
        
        // Get current user
        const response = await api.get<ApiResponse<{ user: User }>>('/auth/me');
        
        if (response.data.success && response.data.data && response.data.data.user) {
          dispatch({
            type: 'LOGIN_SUCCESS',
            payload: { user: response.data.data.user, token },
          });
        } else {
          localStorage.removeItem('token');
          dispatch({ type: 'LOGOUT' });
        }
      } catch (error) {
        localStorage.removeItem('token');
        dispatch({ type: 'LOGOUT' });
      }
    };
    
    loadUser();
  }, []);

  // Login
  const login = async (credential: string, password: string) => {
    dispatch({ type: 'LOGIN_REQUEST' });
    
    try {
      const response = await api.post<ApiResponse<{ user: User; token: string }>>('/auth/login', {
        credential,
        password,
      });
      
      if (response.data.success && response.data.data) {
        const { user, token } = response.data.data;
        
        // Store token in localStorage
        localStorage.setItem('token', token);
        
        // Set token in API headers
        api.setAuthToken(token);
        
        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: { user, token },
        });
      } else {
        throw new Error(response.data.error || 'Login failed');
      }
    } catch (error: any) {
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: error.response?.data?.error || error.message || 'Login failed',
      });
      throw error;
    }
  };

  // Register
  const register = async (username: string, email: string, password: string, termsAccepted: boolean) => {
    dispatch({ type: 'REGISTER_REQUEST' });
    
    try {
      const response = await api.post<ApiResponse<{ user: User; token: string }>>('/auth/register', {
        username,
        email,
        password,
        termsAccepted
      });
      
      if (response.data.success && response.data.data) {
        const { user, token } = response.data.data;
        
        // Store token in localStorage
        localStorage.setItem('token', token);
        
        // Set token in API headers
        api.setAuthToken(token);
        
        dispatch({
          type: 'REGISTER_SUCCESS',
          payload: { user, token },
        });
      } else {
        throw new Error(response.data.error || 'Registration failed');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      console.error('Response data:', error.response?.data);
      
      dispatch({
        type: 'REGISTER_FAILURE',
        payload: error.response?.data?.error || error.message || 'Registration failed',
      });
      throw error;
    }
  };

  // Logout
  const logout = () => {
    // Remove token from localStorage
    localStorage.removeItem('token');
    
    // Remove token from API headers
    api.removeAuthToken();
    
    dispatch({ type: 'LOGOUT' });
  };

  // Update user
  const updateUser = (user: User) => {
    dispatch({ type: 'UPDATE_USER', payload: user });
  };

  return (
    <AuthContext.Provider
      value={{
        ...state,
        login,
        register,
        logout,
        updateUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
