import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from './useAuth';

/**
 * Custom hook for admin authentication
 * Restricts access to admin routes to only users with IDs 1 and 2
 * Redirects unauthorized users to the home page
 */
export const useAdminAuth = () => {
  const { user, isAuthenticated, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Wait until authentication check is complete
    if (!loading) {
      // Check if user is authenticated and has admin access
      const hasAdminAccess = isAuthenticated && user && (user.id === 1 || user.id === 2);
      
      if (!hasAdminAccess) {
        // Redirect unauthorized users to the home page
        navigate('/', { replace: true });
      }
    }
  }, [isAuthenticated, user, loading, navigate]);

  return {
    isAdmin: isAuthenticated && user && (user.id === 1 || user.id === 2),
    user,
    loading
  };
};
