import { useState, useEffect } from 'react';
import { Store } from '../types';
import { storeService } from '../services/storeService';

export const useStores = (category?: number) => {
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStores = async () => {
      try {
        const data = await storeService.getStores(category);
        setStores(data);
      } catch (err) {
        setError('Failed to fetch stores');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchStores();
  }, [category]); // Re-fetch when category changes

  return { stores, loading, error };
};