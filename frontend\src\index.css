/* Custom utility classes */
@layer components {
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
  }
  
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 transform hover:-translate-y-0.5;
  }
  
  .btn-secondary {
    @apply bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-primary-500 transform hover:-translate-y-0.5;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 transform hover:-translate-y-0.5;
  }
  
  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
  
  .form-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }
  
  .card {
    @apply bg-white overflow-hidden shadow-card rounded-lg transition-all duration-300 hover:shadow-card-hover border border-transparent hover:border-primary-100;
  }
  
  .vote-btn {
    @apply inline-flex items-center p-1 border border-transparent rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-transform duration-200 hover:scale-110;
  }

  .deal-title {
    @apply text-lg font-medium text-gray-900 hover:text-deal-orange transition-colors duration-200 line-clamp-2 leading-tight;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition duration-200;
  }
  
  .badge-hot {
    @apply bg-red-100 text-red-800 animate-pulse-slow;
  }
  
  .badge-new {
    @apply bg-secondary-100 text-secondary-800;
  }
  
  .badge-expired {
    @apply bg-gray-100 text-gray-800;
  }
  
  .badge-best {
    @apply bg-primary-100 text-primary-800 border border-primary-300;
  }
  
  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary-500 to-secondary-500;
  }
  
  .discount-badge {
    @apply bg-deal-hot text-white font-extrabold rounded-md px-2 py-1 flex items-center justify-center transform rotate-2 hover:rotate-0 transition-transform duration-200;
  }
  
  .price-tag {
    @apply bg-primary-50 text-primary-800 px-3 py-1 rounded-full font-semibold;
  }
  
  .original-price {
    @apply text-gray-500 line-through decoration-red-500 decoration-2;
  }

  /* New Loveable styles */
  .glass {
    @apply bg-white/90 backdrop-blur-md border border-white/30 shadow-sm;
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:shadow-md hover:-translate-y-1;
  }
  
  /* Deal card styles from Loveable */
  .deal-card {
    @apply glass rounded-xl overflow-hidden;
  }
  
  .deal-image {
    @apply object-cover h-48 w-full transition-transform duration-500 hover:scale-105;
  }
  
  .deal-price {
    @apply text-lg font-bold text-deal-orange;
  }
  
  .deal-original-price {
    @apply text-sm line-through text-gray-400;
  }
  
  /* Deal badges */
  .deal-badge {
    @apply inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .deal-badge-primary {
    @apply bg-deal-orange text-white;
  }
  
  .deal-badge-secondary {
    @apply bg-deal-blue-light text-deal-blue;
  }
  
  .deal-badge-success {
    @apply bg-deal-green-light text-deal-green;
  }
  
  /* Category styling */
  .category-item {
    @apply rounded-lg p-3 transition-all duration-300 hover:bg-white/90 hover:shadow-md flex items-center gap-3;
  }
  
  .category-icon {
    @apply transition-all duration-300;
  }
  
  .category-item:hover .category-icon {
    @apply text-deal-orange transform scale-110;
  }
  
  /* Navigation styling */
  .nav-link {
    @apply relative px-3 py-2 transition-colors duration-300 hover:text-deal-orange;
  }
  
  .nav-link.active {
    @apply text-deal-orange font-medium;
  }
  
  .nav-link.active::after {
    content: '';
    @apply absolute bottom-0 left-0 w-full h-0.5 bg-deal-orange rounded-full;
  }
  
  /* Search input */
  .search-input {
    @apply bg-white/70 backdrop-blur-sm border-2 border-gray-300 focus:border-deal-orange focus:ring-1 focus:ring-deal-orange placeholder:text-gray-400 rounded-full;
  }
  
  /* Button styles from Loveable */
  .btn-primary-new {
    @apply rounded-full bg-deal-orange hover:bg-deal-orange-dark text-white font-medium px-6 py-2.5 shadow-sm transition-all duration-300;
  }
  
  .btn-secondary-new {
    @apply rounded-full bg-white border border-deal-orange text-deal-orange font-medium px-6 py-2.5 shadow-sm hover:bg-deal-orange-light transition-all duration-300;
  }
  
  .btn-tertiary {
    @apply rounded-full bg-deal-blue hover:bg-white/50 text-white/80 font-medium px-6 py-2.5 transition-all duration-300;
  }
}

/* Define pattern backgrounds */
.bg-pattern-dots {
  background-image: radial-gradient(currentColor 1px, transparent 1px);
  background-size: calc(10 * 1px) calc(10 * 1px);
}

.bg-pattern-zigzag {
  background: linear-gradient(135deg, #f5f5f5 25%, transparent 25%) -10px 0,
              linear-gradient(225deg, #f5f5f5 25%, transparent 25%) -10px 0,
              linear-gradient(315deg, #f5f5f5 25%, transparent 25%),
              linear-gradient(45deg, #f5f5f5 25%, transparent 25%);
  background-size: 20px 20px;
  background-color: white;
}

.bg-confetti {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23f5a52c' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
}

/* Animated elements */
.bounce-hover:hover {
  animation: bounce 1s;
}

.pulse-animate {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

.animate-slide-up {
  animation: slide-up 0.5s ease-out;
}

.animate-slide-down {
  animation: slide-down 0.5s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

/* Hover card effects */
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
}

/* Set background gradient for body */
body {
  background-image: 
    radial-gradient(at 40% 20%, hsla(27, 100%, 97%, 1) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(216, 100%, 97%, 1) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(190, 100%, 97%, 1) 0px, transparent 50%);
  background-attachment: fixed;
  background-size: cover;
  color: #0f1924
}

/* Animation keyframes */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes slide-up {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-down {
  0% {
    transform: translateY(-20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}
