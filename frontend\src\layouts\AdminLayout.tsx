import React, { Fragment, useState, useEffect } from 'react';
import { Link, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Dialog, Transition, Disclosure } from '@headlessui/react';
import {
  Bars3Icon,
  XMarkIcon,
  HomeIcon,
  TagIcon,
  BuildingStorefrontIcon,
  DocumentTextIcon,
  UserGroupIcon,
  ServerStackIcon,
  ArrowLeftOnRectangleIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../hooks/useAuth';
import adminService from '../services/adminService';

const navigation = [
  { name: 'Dashboard', href: '/admin', icon: HomeIcon },
  { name: 'Categories', href: '/admin/categories', icon: TagIcon },
  { name: 'Stores', href: '/admin/stores', icon: BuildingStorefrontIcon },
  { 
    name: 'Deals', 
    icon: DocumentTextIcon,
    submenu: [
      { name: 'View All', href: '/admin/deals' },
      { name: 'View Pending', href: '/admin/deals?status=pending', showPendingCount: true },
      { name: 'View Active', href: '/admin/deals?status=active', showActiveCount: true }
    ]
  },
  { name: 'Users', href: '/admin/users', icon: UserGroupIcon },
  { name: 'Scrapers', href: '/admin/scrapers', icon: ServerStackIcon },
];

const AdminLayout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [pendingDealsCount, setPendingDealsCount] = useState<number>(0);
  const [activeDealsCount, setActiveDealsCount] = useState<number>(0);

  // Fetch deals counts on mount
  useEffect(() => {
    const fetchDealsCounts = async () => {
      try {
        const [pendingCount, activeCount] = await Promise.all([
          adminService.getPendingDealsCount(),
          adminService.getActiveDealsCount()
        ]);
        setPendingDealsCount(pendingCount);
        setActiveDealsCount(activeCount);
      } catch (error) {
        console.error('Error fetching deals counts:', error);
      }
    };

    fetchDealsCounts();
    
    // Set up polling interval to refresh counts every minute
    const intervalId = setInterval(fetchDealsCounts, 60000);
    
    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const isActive = (href: string) => {
    // Parse the href to separate path and query parameters
    const [hrefPath, hrefQuery] = href.split('?');
    
    if (hrefPath === '/admin') {
      return location.pathname === '/admin';
    }
    
    // If this is a path with query params (like /admin/deals?status=pending)
    if (hrefQuery) {
      // Check if both path and query match
      const locationQueryParams = new URLSearchParams(location.search);
      const hrefQueryParams = new URLSearchParams(`?${hrefQuery}`);
      
      // Convert the href query params to an object
      const hrefParamsObj: Record<string, string> = {};
      // Use forEach instead of for...of to avoid TypeScript iteration errors
      hrefQueryParams.forEach((value, key) => {
        hrefParamsObj[key] = value;
      });
      
      // Check if the location path matches and if all href query params are in the location query
      return location.pathname === hrefPath && 
        Object.entries(hrefParamsObj).every(([key, value]) => 
          locationQueryParams.get(key) === value
        );
    }
    
    // For paths without query params, check if the location pathname starts with the href path
    // but make sure we're not matching a partially matching path with query params
    // e.g. /admin/deals should not match /admin/deals?status=pending
    return location.pathname === hrefPath;
  };

  const isSubmenuActive = (submenu: any[]) => {
    return submenu.some(item => {
      const [baseHref, query] = item.href.split('?');
      
      // If there's a query parameter in the item href
      if (query) {
        // Check exact match including query parameters
        return location.pathname === baseHref && location.search.includes(`?${query}`);
      }
      
      // Otherwise just check if the pathname matches
      return location.pathname === baseHref || 
        (location.pathname.startsWith(baseHref) && baseHref !== '/admin/deals');
    });
  };

  return (
    <div className="h-full">
      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10 lg:hidden" onClose={setSidebarOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-600 bg-opacity-75" />
          </Transition.Child>

          <div className="fixed inset-0 z-40 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative flex w-full max-w-xs flex-1 flex-col bg-white pt-5 pb-4">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute top-0 right-0 -mr-12 pt-2">
                    <button
                      type="button"
                      className="ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                      onClick={() => setSidebarOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <div className="flex flex-shrink-0 items-center px-4">
                  <Link to="/" className="flex items-center">
                    <span className="text-xl font-bold text-primary-600">NiceDeals</span>
                    <span className="ml-2 bg-primary-100 px-2 py-0.5 text-xs font-medium text-primary-800 rounded-md">
                      Admin
                    </span>
                  </Link>
                </div>

                <div className="mt-5 h-0 flex-1 overflow-y-auto">
                  <nav className="space-y-1 px-2">
                    {navigation.map((item) => {
                      const isItemActive = item.submenu ? isSubmenuActive(item.submenu) : isActive(item.href);
                      
                      return item.submenu ? (
                        <Disclosure key={item.name} defaultOpen={isItemActive}>
                          {({ open }) => (
                            <>
                              <Disclosure.Button
                                className={`
                                  group flex w-full items-center px-2 py-2 text-sm font-medium rounded-md
                                  ${isItemActive ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}
                                `}
                              >
                                <item.icon
                                  className={`
                                    mr-3 h-6 w-6 flex-shrink-0
                                    ${isItemActive ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}
                                  `}
                                  aria-hidden="true"
                                />
                                <span className="flex-1">{item.name}</span>
                                <ChevronDownIcon
                                  className={`
                                    ml-3 h-5 w-5 transform transition-transform duration-150
                                    ${open ? 'rotate-180' : 'rotate-0'}
                                  `}
                                />
                              </Disclosure.Button>
                              <Disclosure.Panel className="space-y-1 pl-10 pt-2">
                                {item.submenu.map((subItem) => (
                                  <Link
                                    key={subItem.name}
                                    to={subItem.href}
                                    onClick={() => setSidebarOpen(false)}
                                    className={`
                                      group flex items-center rounded-md px-2 py-2 text-sm font-medium
                                      ${isActive(subItem.href)
                                        ? 'bg-gray-50 text-primary-600' 
                                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}
                                    `}
                                  >
                                    {subItem.name}
                                    {subItem.showPendingCount && pendingDealsCount > 0 && (
                                      <span className="ml-2 rounded-full bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800">
                                        {pendingDealsCount}
                                      </span>
                                    )}
                                    {subItem.showActiveCount && activeDealsCount > 0 && (
                                      <span className="ml-2 rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                        {activeDealsCount}
                                      </span>
                                    )}
                                  </Link>
                                ))}
                              </Disclosure.Panel>
                            </>
                          )}
                        </Disclosure>
                      ) : (
                        <Link
                          key={item.name}
                          to={item.href}
                          className={`
                            group flex items-center px-2 py-2 text-sm font-medium rounded-md
                            ${isActive(item.href) ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}
                          `}
                          onClick={() => setSidebarOpen(false)}
                        >
                          <item.icon
                            className={`
                              mr-3 h-6 w-6 flex-shrink-0
                              ${isActive(item.href) ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}
                            `}
                            aria-hidden="true"
                          />
                          {item.name}
                        </Link>
                      );
                    })}
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
            <div className="w-14 flex-shrink-0" aria-hidden="true">
              {/* Dummy element to force sidebar to shrink to fit close icon */}
            </div>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static sidebar for desktop */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-grow flex-col overflow-y-auto border-r border-gray-200 bg-white pt-5">
          <div className="flex flex-shrink-0 items-center px-4">
            <Link to="/" className="flex items-center">
              <span className="text-xl font-bold text-primary-600">NiceDeals</span>
              <span className="ml-2 bg-primary-100 px-2 py-0.5 text-xs font-medium text-primary-800 rounded-md">
                Admin
              </span>
            </Link>
          </div>
          <div className="mt-5 flex flex-grow flex-col">
            <nav className="flex-1 space-y-1 px-2 pb-4">
              {navigation.map((item) => {
                const isItemActive = item.submenu ? isSubmenuActive(item.submenu) : isActive(item.href);
                
                return item.submenu ? (
                  <Disclosure key={item.name} defaultOpen={isItemActive}>
                    {({ open }) => (
                      <>
                        <Disclosure.Button
                          className={`
                            group flex w-full items-center px-2 py-2 text-sm font-medium rounded-md
                            ${isItemActive ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}
                          `}
                        >
                          <item.icon
                            className={`
                              mr-3 h-6 w-6 flex-shrink-0
                              ${isItemActive ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}
                            `}
                            aria-hidden="true"
                          />
                          <span className="flex-1">{item.name}</span>
                          <ChevronDownIcon
                            className={`
                              ml-3 h-5 w-5 transform transition-transform duration-150
                              ${open ? 'rotate-180' : 'rotate-0'}
                            `}
                          />
                        </Disclosure.Button>
                        <Disclosure.Panel className="space-y-1 pl-10 pt-2">
                          {item.submenu.map((subItem) => (
                            <Link
                              key={subItem.name}
                              to={subItem.href}
                              className={`
                                group flex items-center rounded-md px-2 py-2 text-sm font-medium
                                ${isActive(subItem.href)
                                  ? 'bg-gray-50 text-primary-600' 
                                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}
                              `}
                            >
                              {subItem.name}
                              {subItem.showPendingCount && pendingDealsCount > 0 && (
                                <span className="ml-2 rounded-full bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800">
                                  {pendingDealsCount}
                                </span>
                              )}
                              {subItem.showActiveCount && activeDealsCount > 0 && (
                                <span className="ml-2 rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                  {activeDealsCount}
                                </span>
                              )}
                            </Link>
                          ))}
                        </Disclosure.Panel>
                      </>
                    )}
                  </Disclosure>
                ) : (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`
                      group flex items-center px-2 py-2 text-sm font-medium rounded-md
                      ${isActive(item.href) ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}
                    `}
                  >
                    <item.icon
                      className={`
                        mr-3 h-6 w-6 flex-shrink-0
                        ${isActive(item.href) ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'}
                      `}
                      aria-hidden="true"
                    />
                    {item.name}
                  </Link>
                );
              })}
            </nav>
          </div>

          {/* User info */}
          <div className="flex flex-shrink-0 border-t border-gray-200 p-4">
            <div className="flex items-center">
              <div>
                <div className="text-sm font-medium text-gray-700">
                  {user?.email || 'Admin User'}
                </div>
                <button
                  onClick={handleLogout}
                  className="flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mt-1"
                >
                  <ArrowLeftOnRectangleIcon className="mr-1.5 h-4 w-4" />
                  Sign out
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-1 flex-col lg:pl-64">
        <div className="sticky top-0 z-10 flex h-16 flex-shrink-0 bg-white shadow">
          <button
            type="button"
            className="border-r border-gray-200 px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>
          <div className="flex flex-1 items-center justify-between px-4">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Admin Panel</h1>
            </div>
            <div className="ml-4 flex items-center md:ml-6">
              <Link
                to="/"
                className="rounded-md bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Back to Site
              </Link>
            </div>
          </div>
        </div>

        <main className="flex-1">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;








