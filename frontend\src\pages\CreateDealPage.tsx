import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { PaperClipIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { DealFormValues, Category, Store } from '../types';
import { createDeal, dealService } from '../services/dealService';
import { getCategories } from '../services/categoryService';
import { getStores } from '../services/storeService';
import { useAuth } from '../hooks/useAuth';

// Validation schema
const validationSchema = Yup.object({
  title: Yup.string().required('Title is required').max(100, 'Title must be at most 100 characters'),
  description: Yup.string().required('Description is required'),
  dealUrl: Yup.string().url('Must be a valid URL').required('Deal URL is required'),
  price: Yup.number().required('Price is required').min(0, 'Price must be positive'),
  originalPrice: Yup.number().required('Original price is required').min(0, 'Original price must be positive'),
  categoryId: Yup.number().required('Category is required').min(1, 'Please select a category'),
  storeId: Yup.number().required('Store is required').min(1, 'Please select a store'),
  coupon: Yup.string().max(50, 'Coupon code must be at most 50 characters'),
  expiresAt: Yup.mixed().when('$expirationDateEnabled', {
    is: true,
    then: () => Yup.date().nullable().min(new Date(), 'Expiration date must be in the future'),
    otherwise: () => Yup.mixed().nullable()
  }),
  // This will be validated at handleSubmit time since it's not a direct form field
  imageUrl: Yup.string()
});

const CreateDealPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [expirationDateEnabled, setExpirationDateEnabled] = useState(false);
  
  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // Fetch categories
  const { data: categories = [] } = useQuery<Category[]>('categories', () => getCategories());
  
  // Fetch stores
  const { data: stores = [] } = useQuery<Store[]>('stores', () => getStores());
  
  // Initial form values
  const initialValues: DealFormValues = {
    title: '',
    description: '',
    dealUrl: '',
    price: 0, // Use 0 as a placeholder that will be validated by our schema
    originalPrice: 0, // Use 0 as a placeholder that will be validated by our schema
    categoryId: 0, // Use 0 as a placeholder that will be validated by our schema
    storeId: 0, // Use 0 as a placeholder that will be validated by our schema
    coupon: '',
    expiresAt: undefined,
  };
  
  // Handle image selection
  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (file.size > 2 * 1024 * 1024) {
        alert('Image size must be less than 2MB');
        return;
      }
      
      if (!file.type.startsWith('image/')) {
        alert('Selected file must be an image');
        return;
      }
      
      setSelectedImage(file);
      setImagePreview(URL.createObjectURL(file));
    }
  };
  
  // Clear selected image
  const clearImage = () => {
    setSelectedImage(null);
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview);
    }
    setImagePreview(null);
  };
  
  // Handle form submission
  const handleSubmit = async (values: DealFormValues) => {
    try {
      setSubmitError(null);
      
      // Validate image is selected
      if (!selectedImage) {
        setSubmitError('Please upload an image for the deal');
        return;
      }
      
      // Create FormData if image is selected
      try {
        // Upload the image using the service function
        const imageResult = await dealService.uploadDealImage(selectedImage);
        values.imageUrl = imageResult.imageUrl;
        values.thumbnailUrl = imageResult.thumbnailUrl;
      } catch (error) {
        throw new Error('Failed to upload image');
      }
      
      // Submit the deal
      const newDeal = await createDeal(values);
      
      // Redirect to the confirmation page with deal data
      navigate('/deals/confirmation', { 
        state: { deal: newDeal }
      });
    } catch (error) {
      console.error('Error creating deal:', error);
      setSubmitError(error instanceof Error ? error.message : 'An error occurred while creating the deal');
    }
  };

  return (
    <div className="mx-auto max-w-3xl px-4 py-8 sm:px-6 lg:px-8">
      <h1 className="mb-6 text-2xl font-bold text-gray-900">Post a New Deal</h1>
      
      {submitError && (
        <div className="mb-6 rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XMarkIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">{submitError}</div>
            </div>
          </div>
        </div>
      )}
      
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          validateOnChange={false}
          validateOnBlur={true}
          validationContext={{ expirationDateEnabled }}
        >
          {({ isSubmitting, errors, touched, setFieldValue }) => (
            <Form className="space-y-6 p-6">
              {/* Title Field */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                  Title <span className="text-red-500">*</span>
                </label>
                <Field
                  type="text"
                  name="title"
                  id="title"
                  className={`mt-1 block w-full rounded-md border ${
                    errors.title && touched.title ? 'border-red-300' : 'border-gray-300'
                  } px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm`}
                  placeholder="e.g., 50% off Sony WH-1000XM4 Headphones"
                />
                <ErrorMessage name="title" component="p" className="mt-1 text-sm text-red-600" />
              </div>
              
              {/* Description Field */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description <span className="text-red-500">*</span>
                </label>
                <Field
                  as="textarea"
                  name="description"
                  id="description"
                  rows={5}
                  className={`mt-1 block w-full rounded-md border ${
                    errors.description && touched.description ? 'border-red-300' : 'border-gray-300'
                  } px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm`}
                  placeholder="Describe the deal, include details about the product, coupon codes, etc."
                />
                <ErrorMessage name="description" component="p" className="mt-1 text-sm text-red-600" />
              </div>
              
              {/* URL Field */}
              <div>
                <label htmlFor="dealUrl" className="block text-sm font-medium text-gray-700">
                  Deal URL <span className="text-red-500">*</span>
                </label>
                <Field
                  type="url"
                  name="dealUrl"
                  id="dealUrl"
                  className={`mt-1 block w-full rounded-md border ${
                    errors.dealUrl && touched.dealUrl ? 'border-red-300' : 'border-gray-300'
                  } px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm`}
                  placeholder="https://example.com/product"
                />
                <ErrorMessage name="dealUrl" component="p" className="mt-1 text-sm text-red-600" />
              </div>
              
              {/* Price Fields */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                    Current Price
                  </label>
                  <div className="relative mt-1 rounded-md shadow-sm">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <span className="text-gray-500 sm:text-sm">£</span>
                    </div>
                    <Field
                      type="number"
                      step="0.01"
                      name="price"
                      id="price"
                      className={`block w-full rounded-md border ${
                        errors.price && touched.price ? 'border-red-300' : 'border-gray-300'
                      } pl-7 pr-12 focus:border-primary-500 focus:ring-primary-500 sm:text-sm`}
                      placeholder="0.00"
                    />
                  </div>
                  <ErrorMessage name="price" component="p" className="mt-1 text-sm text-red-600" />
                </div>
                
                <div>
                  <label htmlFor="originalPrice" className="block text-sm font-medium text-gray-700">
                    Original Price
                  </label>
                  <div className="relative mt-1 rounded-md shadow-sm">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <span className="text-gray-500 sm:text-sm">£</span>
                    </div>
                    <Field
                      type="number"
                      step="0.01"
                      name="originalPrice"
                      id="originalPrice"
                      className={`block w-full rounded-md border ${
                        errors.originalPrice && touched.originalPrice ? 'border-red-300' : 'border-gray-300'
                      } pl-7 pr-12 focus:border-primary-500 focus:ring-primary-500 sm:text-sm`}
                      placeholder="0.00"
                    />
                  </div>
                  <ErrorMessage name="originalPrice" component="p" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              {/* Category and Store Fields */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700">
                    Category <span className="text-red-500">*</span>
                  </label>
                  <Field
                    as="select"
                    name="categoryId"
                    id="categoryId"
                    className={`mt-1 block w-full rounded-md border ${
                      errors.categoryId && touched.categoryId ? 'border-red-300' : 'border-gray-300'
                    } px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm`}
                  >
                    <option value="">Select a category</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </Field>
                  <ErrorMessage name="categoryId" component="p" className="mt-1 text-sm text-red-600" />
                </div>
                
                <div>
                  <label htmlFor="storeId" className="block text-sm font-medium text-gray-700">
                    Store <span className="text-red-500">*</span>
                  </label>
                  <Field
                    as="select"
                    name="storeId"
                    id="storeId"
                    className={`mt-1 block w-full rounded-md border ${
                      errors.storeId && touched.storeId ? 'border-red-300' : 'border-gray-300'
                    } px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm`}
                  >
                    <option value="">Select a store</option>
                    {stores.map((store) => (
                      <option key={store.id} value={store.id}>
                        {store.name}
                      </option>
                    ))}
                  </Field>
                  <ErrorMessage name="storeId" component="p" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              {/* Coupon Field */}
              <div>
                <label htmlFor="coupon" className="block text-sm font-medium text-gray-700">
                  Coupon Code
                </label>
                <Field
                  type="text"
                  name="coupon"
                  id="coupon"
                  className={`mt-1 block w-full rounded-md border ${
                    errors.coupon && touched.coupon ? 'border-red-300' : 'border-gray-300'
                  } px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm`}
                  placeholder="Optional: Enter coupon code if applicable"
                />
                <ErrorMessage name="coupon" component="p" className="mt-1 text-sm text-red-600" />
              </div>
              
              {/* Expiration Date Field with Toggle */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <label htmlFor="expiresAt" className="block text-sm font-medium text-gray-700">
                    Expiration Date
                  </label>
                  <div className="flex items-center">
                    <span className="text-sm text-gray-500 mr-2">
                      {expirationDateEnabled ? 'Enabled' : 'Disabled'}
                    </span>
                    <button
                      type="button"
                      className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none ${
                        expirationDateEnabled ? 'bg-primary-600' : 'bg-gray-200'
                      }`}
                      onClick={() => {
                        setExpirationDateEnabled(!expirationDateEnabled);
                        // Reset the field value when toggling off
                        if (expirationDateEnabled) {
                          setFieldValue('expiresAt', undefined);
                        }
                      }}
                    >
                      <span className="sr-only">Toggle expiration date</span>
                      <span
                        className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                          expirationDateEnabled ? 'translate-x-5' : 'translate-x-0'
                        }`}
                      />
                    </button>
                  </div>
                </div>
                <Field
                  type="datetime-local"
                  name="expiresAt"
                  id="expiresAt"
                  disabled={!expirationDateEnabled}
                  className={`mt-1 block w-full rounded-md border ${
                    errors.expiresAt && touched.expiresAt ? 'border-red-300' : 'border-gray-300'
                  } px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm ${
                    !expirationDateEnabled ? 'bg-gray-100 cursor-not-allowed' : ''
                  }`}
                />
                <ErrorMessage name="expiresAt" component="p" className="mt-1 text-sm text-red-600" />
              </div>
              
              {/* Image Upload Field */}
              <div>
                <label className="block text-sm font-medium text-gray-700">Deal Image</label>
                <div className="mt-1 flex flex-col items-center space-y-4">
                  {imagePreview ? (
                    <div className="relative">
                      <img
                        src={imagePreview}
                        alt="Preview"
                        className="h-48 w-auto rounded-md object-cover"
                      />
                      <button
                        type="button"
                        onClick={clearImage}
                        className="absolute -right-2 -top-2 rounded-full bg-red-100 p-1 text-red-600 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                      >
                        <XMarkIcon className="h-4 w-4" aria-hidden="true" />
                      </button>
                    </div>
                  ) : (
                    <div className="flex max-w-lg justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-5 pb-6">
                      <div className="space-y-1 text-center">
                        <PaperClipIcon className="mx-auto h-12 w-12 text-gray-400" aria-hidden="true" />
                        <div className="flex text-sm text-gray-600">
                          <label
                            htmlFor="image-upload"
                            className="relative cursor-pointer rounded-md bg-white font-medium text-primary-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 hover:text-primary-500"
                          >
                            <span>Upload an image</span>
                            <input
                              id="image-upload"
                              name="image-upload"
                              type="file"
                              accept="image/*"
                              className="sr-only"
                              onChange={handleImageChange}
                            />
                          </label>
                          <p className="pl-1">or drag and drop</p>
                        </div>
                        <p className="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => navigate(-1)}
                  className="mr-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="inline-flex items-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                >
                  {isSubmitting ? 'Posting...' : 'Post Deal'}
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default CreateDealPage;
