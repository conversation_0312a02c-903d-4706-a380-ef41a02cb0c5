import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { CheckCircleIcon, ArrowLeftIcon, EyeIcon } from '@heroicons/react/24/outline';
import { formatPrice } from '../utils/formatters';
import { Deal } from '../types';
import { getFullImageUrl, handleImageError } from '../utils/imageUtils';

const DealConfirmationPage: React.FC = () => {
  const location = useLocation();
  const deal = location.state?.deal as Deal | undefined;

  if (!deal) {
    return (
      <div className="container mx-auto mt-8 max-w-4xl px-4">
        <div className="rounded-lg bg-white p-6 shadow-md">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Deal Submitted</h1>
            <p className="mt-2 text-lg text-gray-600">
              Your deal has been submitted and will be reviewed shortly.
            </p>
            <div className="mt-6">
              <Link
                to="/dealsBrowse"
                className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                <ArrowLeftIcon className="mr-1 h-4 w-4" />
                Back to Deals
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto mt-8 max-w-4xl px-4">
      <div className="rounded-lg bg-white p-6 shadow-md">
        <div className="mb-8 text-center">
          <CheckCircleIcon className="mx-auto h-16 w-16 text-green-500" />
          <h1 className="mt-4 text-2xl font-bold text-gray-900">Thank You for Your Submission!</h1>
          <p className="mt-2 text-lg text-gray-600">
            Your deal has been submitted and will be reviewed by our team shortly.
          </p>
        </div>

        <div className="mb-6 rounded-lg bg-gray-50 p-6">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">Deal Details</h2>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <h3 className="text-lg font-medium text-gray-900">{deal.title}</h3>
              <div className="mt-4">
                {deal.price !== undefined && (
                  <div className="text-lg font-bold text-primary-600">
                    {formatPrice(deal.price)}
                    {deal.originalPrice && (
                      <span className="ml-2 text-sm text-gray-500 line-through">
                        {formatPrice(deal.originalPrice)}
                      </span>
                    )}
                  </div>
                )}
              </div>
              <div className="mt-4 text-sm text-gray-600">
                {deal.description?.split('\n').slice(0, 3).map((paragraph, index) => (
                  <p key={index} className="mb-2">
                    {paragraph}
                  </p>
                ))}
                {deal.description?.split('\n').length > 3 && <p>...</p>}
              </div>
            </div>

            <div>
              <div className="overflow-hidden rounded-lg bg-gray-200">
                <img
                  src={getFullImageUrl(deal)}
                  alt={deal.title}
                  className="h-48 w-full object-cover"
                  onError={handleImageError}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 border-t border-gray-200 pt-6">
          <div className="flex flex-col justify-between gap-4 sm:flex-row">
            <Link
              to="/dealsBrowse"
              className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
            >
              <ArrowLeftIcon className="mr-1 h-4 w-4" />
              Back to Deals
            </Link>

            <Link
              to={`/dealDetail/${deal.id}`}
              className="inline-flex items-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              <EyeIcon className="mr-2 h-5 w-5" />
              View Deal Details
            </Link>
          </div>
        </div>

        <div className="mt-6 rounded-lg bg-blue-50 p-4">
          <p className="text-sm text-blue-700">
            <strong>Note:</strong> Your deal is currently in "pending" status and will be visible to
            others after our team reviews it. You can still view and edit it from your account
            dashboard.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DealConfirmationPage;
