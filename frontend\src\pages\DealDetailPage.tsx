import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { format } from 'date-fns';
import { Helmet } from 'react-helmet';
import {
  ChevronUpIcon,
  ChevronDownIcon,
  ChatBubbleLeftIcon,
  ClockIcon,
  LinkIcon,
  PencilIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  ArrowLeftIcon,
  ArrowTopRightOnSquareIcon,
  ShareIcon,
  BookmarkIcon,
  HeartIcon,
  CalendarIcon,
  ShoppingBagIcon,
  InformationCircleIcon,
  CheckIcon,
  GiftIcon,
  HandThumbUpIcon,
  ClipboardIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import toast from 'react-hot-toast';
import { getDealById, deleteDeal, saveDeal, unsaveDeal, checkIfDealSaved, getRelatedDeals } from '../services/dealService';
import { useAuth } from '../hooks/useAuth';
import CommentSection from '../components/comments/CommentSection';
import { getFullImageUrl, handleImageError } from '../utils/imageUtils';
import { Tabs, TabsList, TabsContent, TabsTrigger } from '../components/ui/tabs';
import { Deal } from '../types';
import { formatPrice, getAffiliateUrl } from '../utils/formatters';

const DEBUG: number = 1; // Debug flag: Set to 1 to enable console logging, 0 to disable

// Debug logging helper
const debugLog = (...args: any[]) => {
  if (DEBUG > 0) {
    console.log(...args);
  }
};

// Add detailed debug log for vote counts
const logVoteCounts = (deal: Deal | undefined) => {
  if (!deal || DEBUG === 0) return;
  
  console.log('Deal vote details:', {
    id: deal.id,
    temperature: deal.temperature,
    upvotes: deal.upvotes,
    downvotes: deal.downvotes,
    totalVotes: deal.totalVotes,
    userVote: deal.userVote
  });
};

const DealDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isPendingDeal, setIsPendingDeal] = useState(false);
  const [pendingDealError, setPendingDealError] = useState<string | null>(null);
  const [isSaved, setIsSaved] = useState(false);
  const [copied, setCopied] = useState(false);
  const [expandedSection, setExpandedSection] = useState<string | null>(null);
  const [mainImage, setMainImage] = useState<string | undefined>(undefined);
  const [relatedDeals, setRelatedDeals] = useState<Deal[]>([]);

  // Toggle sections in the description
  const toggleSection = (section: string) => {
    if (expandedSection === section) {
      setExpandedSection(null);
    } else {
      setExpandedSection(section);
    }
  };

  // Copy coupon code to clipboard
  const copyCode = (code: string) => {
    if (!code) return;
    
    navigator.clipboard.writeText(code);
    setCopied(true);
    toast.success(`${code} has been copied to your clipboard.`);
    
    // Reset the copied state after 3 seconds
    setTimeout(() => setCopied(false), 3000);
  };

  // Check if deal is saved
  useEffect(() => {
    const checkSavedStatus = async () => {
      if (isAuthenticated && id && user?.id) {
        try {
          const saved = await checkIfDealSaved(parseInt(id), user.id);
          setIsSaved(saved);
        } catch (error) {
          console.error('Error checking saved status:', error);
          setIsSaved(false);
        }
      }
    };
    
    checkSavedStatus();
  }, [isAuthenticated, id, user?.id]);

  // Handle save deal
  const handleSave = async () => {
    if (!isAuthenticated || !user?.id || !id) {
      toast.error('Please log in to save deals');
      return;
    }

    try {
      if (isSaved) {
        await unsaveDeal(parseInt(id), user.id);
      } else {
        await saveDeal(parseInt(id), user.id);
      }
      setIsSaved(!isSaved);
      toast.success(isSaved ? "Removed from saved deals" : "Deal saved successfully");
    } catch (error: any) {
      toast.error(error.message || 'Failed to save deal');
    }
  };

  // Fetch deal data
  const {
    data: deal,
    isLoading,
    isError,
    error,
  } = useQuery(['deal', parseInt(id!, 10)], () => getDealById(parseInt(id!, 10)), {
    enabled: !!id,
    onSuccess: (data) => {
      debugLog('Fetched deal data:', data); // Wrapped in debug check
      logVoteCounts(data); // Log the vote counts
      if (data.isPending) {
        setIsPendingDeal(true);
      }
      // Set main image when data loads
      if (data.imageUrl) {
        setMainImage(getFullImageUrl(data, {
          currentUserId: user?.id,
          forceOwner: data.userId === user?.id
        }));
      }
      
      // Fetch related deals when the main deal loads
      if (data.categoryId) {
        fetchRelatedDeals(data.categoryId, data.id);
      }
    },
    onError: (err: any) => {
      if (err.isPendingDeal) {
        setPendingDealError(err.message);
      }
    }
  });

  // Fetch related deals
  const fetchRelatedDeals = async (categoryId: number, dealId: number) => {
    try {
      const deals = await getRelatedDeals(categoryId, dealId, 4);
      setRelatedDeals(deals);
    } catch (error) {
      console.error("Failed to fetch related deals:", error);
    }
  };

  debugLog('Deal datas:', deal); // Wrapped in debug check
  if (deal) {
    debugLog('Deal temperature:', deal.temperature); // Wrapped in debug check
  }

  // Delete deal mutation
  const deleteMutation = useMutation(() => deleteDeal(parseInt(id!, 10)), {
    onSuccess: () => {
      toast.success('Deal deleted successfully');
      navigate('/deals');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete deal');
    },
  });

  // Vote mutation
  const voteMutation = useMutation(
    async (value: 1 | -1) => {
      const response = await fetch(`/api/votes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ dealId: id, voteType: value }),
      });
      
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to vote');
      }

      return data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['deal', parseInt(id!, 10)]);
      },
      onError: (error: any) => {
        toast.error(error.message);
      },
    }
  );

  // Handle vote
  const handleVote = (value: 1 | -1) => {
    if (!isAuthenticated) {
      toast.error('Please log in to vote');
      return;
    }

    if (deal?.userId === user?.id) {
      toast.error('You cannot vote on your own deal');
      return;
    }

    // Check if user has already voted
    if (deal?.userVote !== undefined) {
      toast('You have already voted on this deal', {
        icon: '⚠️',
        style: {
          background: '#fef3c7',
          color: '#92400e',
          border: '1px solid #f59e0b',
        },
      });
      return;
    }

    voteMutation.mutate(value);
  };

  // Handle delete
  const handleDelete = () => {
    deleteMutation.mutate();
  };

  // Format the discount percentage
  const discountPercentage = deal?.originalPrice && deal?.price
    ? Math.round(((deal.originalPrice - deal.price) / deal.originalPrice) * 100)
    : null;

  // Calculate you save amount
  const savingsAmount = deal?.originalPrice && deal?.price
    ? deal.originalPrice - deal.price
    : null;

  // Check if deal is expired
  const isExpired = deal?.status === 'expired' ||
    (deal?.expiresAt && typeof deal.expiresAt === 'string' && new Date(deal.expiresAt) < new Date());

  // Determine vote button styles
  const getUpvoteButtonStyle = () => {
    if (deal?.userVote === 1) {
      return 'text-green-600 hover:text-green-700';
    }
    return 'text-gray-400 hover:text-green-600';
  };

  const getDownvoteButtonStyle = () => {
    if (deal?.userVote === -1) {
      return 'text-red-600 hover:text-red-700';
    }
    return 'text-gray-400 hover:text-red-600';
  };

  // Temperature color based on deal temperature (votes)
  const getTemperatureColor = () => {
    if (!deal) return 'text-gray-500';

    if (deal.temperature > 50) return 'text-orange-500';
    if (deal.temperature > 25) return 'text-orange-500';
    if (deal.temperature > 0) return 'text-yellow-500';
    if (deal.temperature < -25) return 'text-blue-600';
    if (deal.temperature < 0) return 'text-blue-400';
    return 'text-gray-500';
  };

  // Check if user is the author or admin
  const isAuthorOrAdmin =
    deal?.userId === user?.id ||
    user?.role === 'admin' ||
    user?.role === 'moderator';

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-64">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
              <p className="mt-4 text-gray-600">Loading deal...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Handle the pending deal error case specifically
  if (pendingDealError) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
          {/* Back button */}
          <Link
            to="/dealsBrowse"
            className="flex items-center text-sm font-medium text-gray-500 hover:text-orange-500 transition-colors"
          >
            <ArrowLeftIcon className="mr-1 h-4 w-4" />
            Back to Deals
          </Link>

          {/* Error message for pending deals */}
          <div className="mt-4 glass rounded-xl p-6">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" aria-hidden="true" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Deal Not Available</h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>{pendingDealError}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <div className="glass rounded-xl p-6 flex flex-col items-center justify-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mb-4" />
            <p className="text-gray-800 text-lg font-medium">Error Loading Deal</p>
            <p className="text-gray-600 mt-2">{(error as Error)?.message || 'Failed to load deal details'}</p>
            <div className="mt-4">
              <Link
                to="/dealsBrowse"
                className="inline-flex items-center rounded-md border border-transparent bg-orange-500 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-orange-600"
              >
                <ArrowLeftIcon className="mr-2 h-5 w-5" />
                Back to Deals
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!deal) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <div className="glass rounded-xl p-6 flex flex-col items-center justify-center">
            <h2 className="text-2xl font-bold text-gray-900">Deal not found</h2>
            <p className="mt-2 text-gray-600">The deal you're looking for doesn't exist or has been removed.</p>
            <div className="mt-6">
              <Link
                to="/dealsBrowse"
                className="inline-flex items-center rounded-md border border-transparent bg-orange-500 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-orange-600"
              >
                <ArrowLeftIcon className="mr-2 h-5 w-5" />
                Back to Deals
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {deal && (
        <Helmet>
          <title>{deal.title} | NiceDeals</title>
          <meta name="description" content={`Grab a deal on ${deal.title}${deal.store ? ` from ${typeof deal.store === 'object' ? deal.store.name : deal.store}` : ''} today. Visit Nicedeals and comment and vote and find other great UK deals.`} />
          
          {/* Open Graph / Facebook */}
          <meta property="og:type" content="website" />
          <meta property="og:url" content={`https://www.nicedeals.app/dealDetail/${deal.id}`} />
          <meta property="og:title" content={deal.title} />
          <meta property="og:description" content={`Grab a deal on ${deal.title}${deal.store ? ` from ${typeof deal.store === 'object' ? deal.store.name : deal.store}` : ''} today. Visit Nicedeals and comment and vote and find other great UK deals.`} />
          <meta property="og:image" content={deal.imageUrl?.startsWith('http') ? deal.imageUrl : `https://www.nicedeals.app${deal.imageUrl}`} />
          <meta property="og:site_name" content="Nicedeals" />
          <meta property="fb:app_id" content="1156897059246722" />
          <meta property="og:price:amount" content={String(deal.price)} />
          <meta property="og:price:currency" content="GBP" />
          
          {/* Twitter */}
          <meta name="twitter:card" content="summary" />
          <meta name="twitter:site" content="@NicedealsApp" />
          <meta name="twitter:title" content={deal.title} />
          <meta property="twitter:description" content={`Grab a deal on ${deal.title}${deal.store ? ` from ${typeof deal.store === 'object' ? deal.store.name : deal.store}` : ''} today. Visit Nicedeals and comment and vote and find other great UK deals.`} />
          <meta name="twitter:image" content={deal.imageUrl?.startsWith('http') ? deal.imageUrl : `https://www.nicedeals.app${deal.imageUrl}`} />
        </Helmet>
      )}
      <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
        <div className="flex flex-col gap-6">
          {/* Breadcrumb Navigation */}
          <nav className="flex items-center text-sm text-gray-500">
            <Link to="/dealsBrowse" className="flex items-center hover:text-orange-500 transition-colors">
              <ArrowLeftIcon className="w-4 h-4 mr-1" />
              Back to Deals
            </Link>
          </nav>
          
          {/* Deal Score Banner - New from Loveable design */}
          <div className="glass rounded-xl bg-gradient-to-r from-orange-500/90 to-orange-600/90 text-white p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="bg-white/20 rounded-full p-2">
                <HandThumbUpIcon className="w-6 h-6" />
              </div>
              <div>
                {/* If the temperature doesn't match upvotes-downvotes, show calculated value */}
                {deal && (deal.upvotes || 0) - (deal.downvotes || 0) !== deal.temperature ? (
                  <div>
                    <h2 className="text-xl font-bold">
                      Deal Score: {(deal.upvotes || 0) - (deal.downvotes || 0)}
                      <span className="text-sm ml-1 opacity-50">(Server reported: {deal.temperature})</span>
                    </h2>
                    <p className="text-sm text-white/80">Based on community votes</p>
                  </div>
                ) : (
                  <div>
                    <h2 className="text-xl font-bold">Deal Score: {deal?.temperature || 0}</h2>
                    <p className="text-sm text-white/80">Based on community votes</p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Voting Controls */}
            <div className="flex items-center gap-2">
              <div className="flex flex-col items-center">
                <button 
                  className={`rounded-full p-2 ${
                    deal?.userVote === 1 
                      ? 'bg-white text-orange-500' 
                      : 'text-white hover:bg-white/20'
                  }`}
                  onClick={() => handleVote(1)}
                  disabled={deal?.userVote !== undefined || !isAuthenticated || deal?.userId === user?.id}
                  title={!isAuthenticated ? "Please log in to vote" : 
                    (deal?.userVote !== undefined ? 
                      `You've already voted ${deal.userVote === 1 ? 'up' : 'down'} on this deal` : 
                      (deal?.userId === user?.id ? "You cannot vote on your own deal" : "Vote up"))}
                >
                  <ChevronUpIcon className="w-5 h-5" />
                </button>
                <span className="text-sm font-medium">
                  {deal?.upvotes || 0}
                </span>
              </div>
              
              <div className="flex flex-col items-center">
                <button
                  className={`rounded-full p-2 ${
                    deal?.userVote === -1 
                      ? 'bg-white text-orange-500' 
                      : 'text-white hover:bg-white/20'
                  }`} 
                  onClick={() => handleVote(-1)}
                  disabled={deal?.userVote !== undefined || !isAuthenticated || deal?.userId === user?.id}
                  title={!isAuthenticated ? "Please log in to vote" : 
                    (deal?.userVote !== undefined ? 
                      `You've already voted ${deal.userVote === 1 ? 'up' : 'down'} on this deal` : 
                      (deal?.userId === user?.id ? "You cannot vote on your own deal" : "Vote down"))}
                >
                  <ChevronDownIcon className="w-5 h-5" />
                </button>
                <span className="text-sm font-medium">
                  {deal?.downvotes || 0}
                </span>
              </div>
            </div>
          </div>
          
          {/* Pending Deal Warning Banner */}
          {isPendingDeal && (
            <div className="rounded-xl bg-yellow-50 p-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" aria-hidden="true" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">Pending Deal</h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>{pendingDealError || 'This deal is pending approval and not viewable by the public.'}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Product Details Section */}
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
            {/* Product Images - Left Column */}
            <div className="col-span-2">
              <div className="glass rounded-xl overflow-hidden">
                {/* Main Product Image */}
                <div className="aspect-square relative overflow-hidden rounded-t-xl">
                  <img 
                    src={mainImage || getFullImageUrl(deal, {
                      currentUserId: user?.id,
                      forceOwner: deal.userId === user?.id
                    })} 
                    alt={deal.title} 
                    className="object-cover w-full h-full"
                    onError={handleImageError}
                  />
                  <div className="absolute top-4 left-4">
                    {discountPercentage && (
                      <div className="bg-orange-500 text-white px-2 py-1 rounded-full text-sm font-bold animate-pulse-subtle">
                        {discountPercentage}% OFF
                      </div>
                    )}
                  </div>
                  
                  {/* Status badges */}
                  {isExpired && (
                    <div className="absolute right-4 top-4 bg-gray-600 px-3 py-1 text-sm font-semibold text-white rounded-md z-10">
                      Expired
                    </div>
                  )}
                  
                  {deal.source === 'hotukdeals' && (
                    <div className="absolute left-4 bottom-4 bg-orange-600 px-3 py-1 text-sm font-semibold text-white rounded-md z-10">
                      HotUKDeals
                    </div>
                  )}
                  
                  <button 
                    className="absolute top-4 right-4 p-2 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white transition-colors"
                    onClick={handleSave}
                    disabled={!isAuthenticated}
                    title={!isAuthenticated ? "Please log in to save deals" : (isSaved ? "Remove from saved deals" : "Save this deal")}
                  >
                    {isSaved ? 
                      <HeartIconSolid className="w-5 h-5 text-red-500" /> : 
                      <HeartIcon className="w-5 h-5 text-gray-600" />
                    }
                  </button>
                </div>
                
                {/* Thumbnail Navigation - only if we have multiple images in the future */}
                {/* For now, placeholder for future functionality */}
              </div>
              
              {/* Social Sharing */}
              <div className="mt-4 flex items-center gap-4">
                <button className="text-gray-500 hover:text-orange-500 transition-colors flex items-center gap-1 text-sm">
                  <ShareIcon className="w-4 h-4" />
                  Share
                </button>
                <button className="text-gray-500 hover:text-orange-500 transition-colors flex items-center gap-1 text-sm">
                  <ChatBubbleLeftIcon className="w-4 h-4" />
                  Comment ({deal.commentCount || 0})
                </button>
                
              </div>
            </div>
            
            {/* Product Details - Right Column */}
            <div className="lg:col-span-3 flex flex-col gap-6">
              {/* Title and Price Section */}
              <div className="glass rounded-xl p-6">
                <div className="flex items-start justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      {deal.category && (
                        <Link to={`/dealsBrowse?category=${deal.categoryId}`} className="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full hover:bg-blue-200 transition-colors">
                          {deal.category.name}
                        </Link>
                      )}
                      <div className="text-xs text-gray-500">
                        Posted {format(new Date(deal.createdAt || 'No Date Available'), 'PPP')} by {deal.userUsername || 'System'}
                      </div>
                    </div>
                    
                    <h1 className="text-2xl font-bold text-gray-800 mb-2">{deal.title}</h1>
                    
                    <div className="flex items-center gap-2">
                      {deal.store && (
                        <div className="flex items-center gap-1">
                          {deal.store.logoUrl && (
                            <img 
                              src={deal.store.logoUrl}
                              alt={deal.store.name}
                              className="w-6 h-6 rounded-full"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = '/images/placeholder-store.png';
                              }}
                            />
                          )}
                          <span className="text-sm text-gray-600">{deal.store.name}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-1 text-sm">
                        <ShoppingBagIcon className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-600">{deal.totalVotes || 0} votes</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    {deal.price && (
                      <div className="text-3xl font-bold text-orange-500">£{deal.price.toFixed(2)}</div>
                    )}
                    {deal.originalPrice && deal.originalPrice > (deal.price || 0) && (
                      <div className="text-sm line-through text-gray-400">£{deal.originalPrice.toFixed(2)}</div>
                    )}
                    {savingsAmount && (
                      <div className="text-sm text-green-600 mt-1">You save: £{savingsAmount.toFixed(2)}</div>
                    )}
                  </div>
                </div>

                {/* Deal Status */}
                <div className="mt-6 flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    {deal.expiresAt && (
                      <div className="flex items-center gap-1 text-sm">
                        <CalendarIcon className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-600">Expires: {format(new Date(deal.expiresAt), 'PPP')}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Coupon Code Card */}
              {deal.coupon && (
                <div className="glass rounded-xl p-6 relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 via-orange-400 to-orange-500"></div>
                  
                  <div className="flex flex-col sm:flex-row items-center gap-4">
                    <div className="flex-1">
                      <div className="text-sm text-gray-500 mb-1">
                        <GiftIcon className="w-4 h-4 inline-block mr-1 text-orange-500" />
                        Use this coupon code to get the deal
                      </div>
                      <div className="relative">
                        <div className="bg-gray-100 border border-gray-200 rounded-md px-4 py-2 font-mono font-semibold text-gray-700">
                          {deal.coupon}
                        </div>
                      </div>
                    </div>
                    
                    <button 
                      onClick={() => copyCode(deal.coupon || '')}
                      className={`inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium shadow-sm 
                        ${copied ? 'bg-green-600 text-white' : 'bg-orange-500 hover:bg-orange-600 text-white'} 
                        w-full sm:w-auto transition-colors`}
                    >
                      {copied ? (
                        <>
                          <CheckIcon className="w-4 h-4 mr-2" />
                          Copied!
                        </>
                      ) : (
                        'Copy Code'
                      )}
                    </button>
                  </div>
                </div>
              )}
              
              {/* Action Buttons - Updated Save Deal button to match Loveable design */}
              <div className="flex flex-col sm:flex-row gap-4">
                {deal.url && (
                  <a 
                    href={getAffiliateUrl(deal.url, deal.storeId)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center rounded-md bg-orange-500 px-4 py-3 text-sm font-medium text-white shadow-sm hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 flex-1 h-12"
                  >
                    <ArrowTopRightOnSquareIcon className="w-5 h-5 mr-2" />
                    Get This Deal
                  </a>
                )}
                
                <button 
                  onClick={handleSave}
                  className={`inline-flex items-center justify-center rounded-md px-4 py-3 text-sm font-medium shadow-sm flex-1 h-12 border ${
                    isSaved 
                      ? 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100' 
                      : 'border-orange-500 text-orange-500 hover:bg-orange-50'
                  } transition-colors duration-200`}
                  disabled={!isAuthenticated}
                  title={!isAuthenticated ? "Please log in to save deals" : ""}
                >
                  {isSaved ? (
                    <CheckIcon className="w-5 h-5 mr-2" />
                  ) : (
                    <HeartIcon className="w-5 h-5 mr-2" />
                  )}
                  {isSaved ? 'Saved' : 'Save Deal'}
                </button>
              </div>
            </div>
          </div>
          
          {/* Product Information Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Product Description Tabs - Main Column */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="description" className="glass rounded-xl p-6">
                <TabsList className="grid w-full grid-cols-3 mb-6">
                  <TabsTrigger value="description">Description</TabsTrigger>
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="comments">Comments</TabsTrigger>
                </TabsList>
                
                <TabsContent value="description" className="space-y-6">
                  {/* Deal Description */}
                  <div>
                    <button 
                      className="flex items-center justify-between w-full text-left mb-3"
                      onClick={() => toggleSection('description')}
                    >
                      <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                        <ShoppingBagIcon className="w-5 h-5 mr-2 text-orange-500" />
                        Deal Description
                      </h3>
                      {expandedSection === 'description' ? (
                        <ChevronUpIcon className="w-5 h-5 text-gray-500" />
                      ) : (
                        <ChevronDownIcon className="w-5 h-5 text-gray-500" />
                      )}
                    </button>
                    
                    {(expandedSection === 'description' || expandedSection === null) && (
                      <div className="pl-7 prose max-w-none text-gray-700 whitespace-pre-wrap">
                        {deal.description}
                      </div>
                    )}
                  </div>
                  
                  {/* Edit/Delete buttons for owner */}
                  {isAuthenticated && isAuthorOrAdmin && (
                    <div className="flex justify-end gap-3 pl-7 pt-3 border-t border-gray-200">
                      <Link
                        to={`/deals/edit/${deal.id}`}
                        className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                      >
                        <PencilIcon className="mr-2 h-4 w-4 text-gray-500" /> Edit
                      </Link>
                      <button
                        onClick={() => setShowDeleteModal(true)}
                        className="inline-flex items-center rounded-md border border-transparent bg-red-100 px-4 py-2 text-sm font-medium text-red-700 hover:bg-red-200"
                      >
                        <TrashIcon className="mr-2 h-4 w-4 text-red-500" /> Delete
                      </button>
                    </div>
                  )}
                </TabsContent>
                
                <TabsContent value="details" className="space-y-6">
                  {/* Deal Details */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 flex items-center mb-3">
                      <ClockIcon className="w-5 h-5 mr-2 text-orange-500" />
                      Deal Details
                    </h3>
                    
                    <ul className="pl-7 space-y-2 text-gray-600">
                      {deal.store && (
                        <li className="flex items-start">
                          <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Available at: {deal.store.name}</span>
                        </li>
                      )}
                      {deal.category && (
                        <li className="flex items-start">
                          <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Category: {deal.category.name}</span>
                        </li>
                      )}
                      {deal.expiresAt && (
                        <li className="flex items-start">
                          <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Expires on: {format(new Date(deal.expiresAt), 'PPP')}</span>
                        </li>
                      )}
                      {deal.userUsername && (
                        <li className="flex items-start">
                          <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Posted by: {deal.userUsername}</span>
                        </li>
                      )}
                      {deal.createdAt && (
                        <li className="flex items-start">
                          <CheckIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Posted on: {format(new Date(deal.createdAt), 'PPP')}</span>
                        </li>
                      )}
                    
                    </ul>
                  </div>
                </TabsContent>
                
                <TabsContent value="comments" className="space-y-6">
                  {/* Comment Section */}
                  <CommentSection dealId={parseInt(id!, 10)} />
                </TabsContent>
              </Tabs>
            </div>
            
            {/* Related Deals Sidebar - replacing temperature module */}
            <div className="lg:col-span-1">
              <div className="glass rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <ShoppingBagIcon className="w-5 h-5 mr-2 text-orange-500" />
                  Related Deals
                </h3>
                
                <div className="space-y-4">
                  {relatedDeals.length > 0 ? (
                    relatedDeals.map((relatedDeal) => (
                      <Link 
                        key={relatedDeal.id} 
                        to={`/dealDetail/${relatedDeal.id}`} 
                        className="flex gap-3 group"
                      >
                        <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                          <img 
                            src={getFullImageUrl(relatedDeal, {
                              currentUserId: user?.id,
                              forceOwner: relatedDeal.userId === user?.id
                            })}
                            alt={relatedDeal.title} 
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                            onError={handleImageError}
                          />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-800 group-hover:text-orange-500 transition-colors line-clamp-2">
                            {relatedDeal.title}
                          </h4>
                          <div className="text-sm font-semibold text-orange-500 mt-1">
                            {relatedDeal.price ? `£${relatedDeal.price.toFixed(2)}` : ''}
                            {relatedDeal.originalPrice && relatedDeal.originalPrice > (relatedDeal.price || 0) && (
                              <span className="ml-1 text-xs line-through text-gray-400">
                                £{relatedDeal.originalPrice.toFixed(2)}
                              </span>
                            )}
                          </div>
                          <div className="text-xs text-gray-500">
                            {relatedDeal.store?.name || relatedDeal.storeName || 'Unknown Store'}
                          </div>
                        </div>
                      </Link>
                    ))
                  ) : (
                    <div className="text-sm text-gray-500 italic">
                      No related deals found
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Delete confirmation modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">
              &#8203;
            </span>

            <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
              <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <ExclamationTriangleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                    <h3 className="text-lg font-medium leading-6 text-gray-900">Delete Deal</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete this deal? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button
                  type="button"
                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDelete}
                  disabled={deleteMutation.isLoading}
                >
                  {deleteMutation.isLoading ? 'Deleting...' : 'Delete'}
                </button>
                <button
                  type="button"
                  className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                  onClick={() => setShowDeleteModal(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DealDetailPage;
