import React, { useState, useEffect, useRef } from 'react';
import { useQuery } from 'react-query';
import { useSearchParams, Link } from 'react-router-dom';
import { PlusIcon } from '@heroicons/react/24/outline';
import { 
  LayoutGrid, 
  List, 
  ArrowUpCircle, 
  Filter, 
  ChevronDown,
  ArrowUpDown 
} from 'lucide-react';
import DealCard from '../components/deals/DealCard';
import GridDealCard from '../components/deals/GridDealCard';
import ListDealCard from '../components/deals/ListDealCard';
import FilterBar from '../components/deals/FilterBar';
import Pagination from '../components/common/Pagination';
import { getDeals } from '../services/dealService';
import { Deal, DealFilters } from '../types';
import { useAuth } from '../hooks/useAuth';
import { useCategories } from '../hooks/useCategories';
import { useStores } from '../hooks/useStores';

// Price ranges for sidebar filtering
const priceRanges = [
  { name: 'Under £25', min: 0, max: 25 },
  { name: '£25 - £50', min: 25, max: 50 },
  { name: '£50 - £100', min: 50, max: 100 },
  { name: '£100 - £200', min: 100, max: 200 },
  { name: 'Over £200', min: 200, max: null }
];

// Deal types for sidebar filtering
const dealTypes = [
  { name: 'Hot Deals', filter: 'hot' },
  { name: 'Warm', filter: 'warm' },
  { name: 'Free', filter: 'free' },
  { name: 'Discounted', filter: 'discounted' }
];

// Hot threshold for deal temperature
const GETTING_WARM_TEMPERATURE = 1; // We'll use this as default since it's 1 in .env

const DealsPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const isInitialMount = useRef(true);
  
  // Scroll to top on component mount
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  
  // View mode state (grid or list)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  
  // Scroll-to-top button visibility
  const [showScrollButton, setShowScrollButton] = useState(false);
  
  // Sidebar state
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [selectedPriceRanges, setSelectedPriceRanges] = useState<string[]>([]);
  const [selectedDealTypes, setSelectedDealTypes] = useState<string[]>([]);
  const [categoriesExpanded, setCategoriesExpanded] = useState(true);
  const [priceRangeExpanded, setPriceRangeExpanded] = useState(true);
  const [storesExpanded, setStoresExpanded] = useState(true);
  const [dealTypeExpanded, setDealTypeExpanded] = useState(true);

  // Parse search params
  const parseSearchParams = (): DealFilters => {
    return {
      search: searchParams.get('search') || undefined,
      category: searchParams.get('category') ? parseInt(searchParams.get('category') as string, 10) : undefined,
      store: searchParams.get('store') ? parseInt(searchParams.get('store') as string, 10) : undefined,
      status: (searchParams.get('status') as 'active' | 'expired' | 'all') || 'active',
      sort: (searchParams.get('sort') as DealFilters['sort']) || 'newest',
      page: searchParams.get('page') ? parseInt(searchParams.get('page') as string, 10) : 1,
      pageSize: searchParams.get('pageSize') ? parseInt(searchParams.get('pageSize') as string, 10) : 30,
      minPrice: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice') as string, 10) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice') as string, 10) : undefined,
      dealType: searchParams.get('dealType') || undefined,
    };
  };
  
  // State for filters
  const [filters, setFilters] = useState<DealFilters>(parseSearchParams());
  const prevFiltersRef = useRef<DealFilters>();
  const isUpdatingRef = useRef(false);
  
  // Fetch categories and stores
  const { categories } = useCategories();
  const { stores } = useStores(filters.category);
  
  // Set initial URL params if none exist
  useEffect(() => {
    if (isInitialMount.current) {
      const newSearchParams = new URLSearchParams();
      
      if (!searchParams.get('status')) newSearchParams.set('status', 'active');
      if (!searchParams.get('sort')) newSearchParams.set('sort', 'newest');
      
      if (newSearchParams.toString()) {
        setSearchParams(newSearchParams, { replace: true });
      }
      
      isInitialMount.current = false;
    }
  }, [searchParams, setSearchParams]);

  // Handle URL and filter synchronization
  useEffect(() => {
    // If we're in the middle of an update, skip this effect
    if (isUpdatingRef.current) {
      isUpdatingRef.current = false;
      return;
    }

    const newFilters = parseSearchParams();
    const currentFiltersStr = JSON.stringify(filters);
    const newFiltersStr = JSON.stringify(newFilters);
    
    // Only update filters if they're different
    if (currentFiltersStr !== newFiltersStr) {
      isUpdatingRef.current = true;
      setFilters(newFilters);
    }
  }, [searchParams]);

  // Update URL when filters change
  useEffect(() => {
    if (isInitialMount.current || isUpdatingRef.current) {
      return;
    }

    const newSearchParams = new URLSearchParams();
    
    if (filters.search) newSearchParams.set('search', filters.search);
    if (filters.category) newSearchParams.set('category', filters.category.toString());
    if (filters.store) newSearchParams.set('store', filters.store.toString());
    if (filters.status) newSearchParams.set('status', filters.status);
    if (filters.sort) newSearchParams.set('sort', filters.sort);
    if (filters.page && filters.page > 1) newSearchParams.set('page', filters.page.toString());
    if (filters.pageSize && filters.pageSize !== 30) newSearchParams.set('pageSize', filters.pageSize.toString());
    if (filters.minPrice) newSearchParams.set('minPrice', filters.minPrice.toString());
    if (filters.maxPrice) newSearchParams.set('maxPrice', filters.maxPrice.toString());
    if (filters.dealType) newSearchParams.set('dealType', filters.dealType);
    
    const currentParams = new URLSearchParams(searchParams);
    if (newSearchParams.toString() !== currentParams.toString()) {
      isUpdatingRef.current = true;
      setSearchParams(newSearchParams);
    }
  }, [filters, setSearchParams, searchParams]);
  
  // Scroll-to-top button visibility handler
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollButton(true);
      } else {
        setShowScrollButton(false);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  
  // Fetch deals
  console.log('API URL:', process.env.REACT_APP_API_URL || 'http://localhost:5010/api');
  
  const { data: dealsResponse, isLoading, isError, error } = useQuery(
    ['deals', filters],
    async () => {
      console.log('Fetching deals with filters:', filters);
      try {
        const response = await getDeals(filters);
        console.log('API Response:', {
          deals: response?.deals?.length,
          totalCount: response?.totalCount,
          page: response?.page,
          totalPages: response?.totalPages
        });
        return response;
      } catch (err: any) {
        console.error('Error fetching deals:', err);
        throw err;
      }
    },
    {
      keepPreviousData: true
    }
  );

  useEffect(() => {
    console.log('Current dealsResponse:', {
      response: dealsResponse,
      deals: dealsResponse?.deals,
      filters
    });
  }, [dealsResponse, filters]);
  
  // Handle filter change
  const handleFilterChange = (newFilters: DealFilters) => {
    setFilters({ ...filters, ...newFilters, page: 1 });
  };
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page });
  };
  
  // Toggle view mode
  const toggleViewMode = (mode: 'grid' | 'list') => {
    setViewMode(mode);
  };

  // Handle category filter
  const handleCategoryChange = (categoryId: number) => {
    if (filters.category === categoryId) {
      handleFilterChange({ ...filters, category: undefined });
    } else {
      handleFilterChange({ ...filters, category: categoryId });
    }
  };

  // Handle store filter
  const handleStoreChange = (storeId: number) => {
    if (filters.store === storeId) {
      handleFilterChange({ ...filters, store: undefined });
    } else {
      handleFilterChange({ ...filters, store: storeId });
    }
  };

  // Handle price range filter
  const handlePriceRangeChange = (name: string, min: number, max: number | null) => {
    const index = selectedPriceRanges.indexOf(name);
    let newRanges = [...selectedPriceRanges];
    let newMinPrice = filters.minPrice;
    let newMaxPrice = filters.maxPrice;
    
    if (index > -1) {
      // Remove range
      newRanges.splice(index, 1);
      if (newRanges.length === 0) {
        newMinPrice = undefined;
        newMaxPrice = undefined;
      }
    } else {
      // Add range
      newRanges.push(name);
      if (!newMinPrice || min < newMinPrice) newMinPrice = min;
      if (!newMaxPrice || (max && (max > newMaxPrice || !newMaxPrice))) newMaxPrice = max || undefined;
    }
    
    setSelectedPriceRanges(newRanges);
    handleFilterChange({ ...filters, minPrice: newMinPrice, maxPrice: newMaxPrice });
  };

  // Handle deal type filter
  const handleDealTypeChange = (name: string, type: string) => {
    const index = selectedDealTypes.indexOf(name);
    let newTypes = [...selectedDealTypes];
    
    if (index > -1) {
      // Remove type
      newTypes.splice(index, 1);
      handleFilterChange({ ...filters, dealType: undefined });
    } else {
      // Add type
      newTypes.push(name);
      handleFilterChange({ ...filters, dealType: type });
    }
    
    setSelectedDealTypes(newTypes);
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedPriceRanges([]);
    setSelectedDealTypes([]);
    handleFilterChange({
      search: undefined,
      category: undefined,
      store: undefined,
      status: 'active',
      sort: 'newest',
      page: 1,
      pageSize: filters.pageSize,
      minPrice: undefined,
      maxPrice: undefined,
      dealType: undefined
    });
  };

  // Toggle filter sections
  const toggleCategoriesSection = () => setCategoriesExpanded(!categoriesExpanded);
  const togglePriceRangeSection = () => setPriceRangeExpanded(!priceRangeExpanded);
  const toggleStoresSection = () => setStoresExpanded(!storesExpanded);
  const toggleDealTypeSection = () => setDealTypeExpanded(!dealTypeExpanded);
  
  // Get page title based on filters
  const getPageTitle = () => {
    const categoryId = filters.category;
    const storeId = filters.store;
    const sort = filters.sort;

    // Only show category/store info if sort is 'newest'
    if (sort !== 'newest') {
      return 'Browse Deals';
    }

    const category = categories?.find(c => c.id === categoryId);
    const store = stores?.find(s => s.id === storeId);

    if (category && store) {
      return `Browse Deals in ${category.name} at ${store.name}`;
    }
    if (category) {
      return `Browse Deals in ${category.name}`;
    }
    if (store) {
      return `Browse Deals at ${store.name}`;
    }
    return 'Browse Deals';
  };

  return (
    <div>
      <div className="mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-1">{getPageTitle()}</h1>
            <p className="text-gray-600 text-sm">Find the best deals from your favorite stores</p>
          </div>
          
          {isAuthenticated && (
            <Link
              to="/deals/create"
              className="inline-flex items-center rounded-lg border border-transparent bg-primary-500 px-5 py-3 text-base font-medium text-white shadow-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
            >
              <PlusIcon className="mr-2 h-5 w-5" aria-hidden="true" />
              Post Deal
            </Link>
          )}
        </div>
      </div>
      
      {/* Mobile filter toggle */}
      <div className="md:hidden mb-4">
        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="w-full py-2 px-4 bg-white border border-gray-300 rounded-lg shadow-sm flex items-center justify-center gap-2 text-gray-700"
        >
          <Filter className="w-4 h-4" />
          {sidebarOpen ? "Hide Filters" : "Show Filters"}
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Left Sidebar Filters - Based on Loveable Design */}
        <aside className={`${sidebarOpen ? 'block' : 'hidden'} md:block h-fit sticky top-20`}>
          <div className="bg-white p-4 rounded-xl shadow-md border border-gray-100">
            <div className="space-y-6">
              {/* Categories */}
              <div>
                <h3 
                  className="text-sm font-semibold mb-3 flex items-center justify-between cursor-pointer"
                  onClick={toggleCategoriesSection}
                >
                  Categories
                  <ChevronDown className={`w-4 h-4 transition-transform ${categoriesExpanded ? 'rotate-180' : ''}`} />
                </h3>
                {categoriesExpanded && (
                  <ul className="space-y-2 max-h-[250px] overflow-y-auto pr-2">
                    {categories?.map((category) => (
                      <li key={category.id}>
                        <label className="flex items-center gap-2 cursor-pointer">
                          <input 
                            type="checkbox" 
                            checked={filters.category === category.id}
                            onChange={() => handleCategoryChange(category.id)}
                            className="rounded border-gray-300 text-primary-500 focus:ring-primary-500"
                          />
                          <span className="text-sm">{category.name}</span>
                          <span className="ml-auto text-xs text-gray-500">{category.dealsCount || 0}</span>
                        </label>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
              
              {/* Price Range */}
              <div>
                <h3 
                  className="text-sm font-semibold mb-3 flex items-center justify-between cursor-pointer"
                  onClick={togglePriceRangeSection}
                >
                  Price Range
                  <ChevronDown className={`w-4 h-4 transition-transform ${priceRangeExpanded ? 'rotate-180' : ''}`} />
                </h3>
                {priceRangeExpanded && (
                  <ul className="space-y-2">
                    {priceRanges.map((range) => (
                      <li key={range.name}>
                        <label className="flex items-center gap-2 cursor-pointer">
                          <input 
                            type="checkbox" 
                            checked={selectedPriceRanges.includes(range.name)}
                            onChange={() => handlePriceRangeChange(range.name, range.min, range.max)}
                            className="rounded border-gray-300 text-primary-500 focus:ring-primary-500"
                          />
                          <span className="text-sm">{range.name}</span>
                        </label>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
              
              {/* Stores */}
              <div>
                <h3 
                  className="text-sm font-semibold mb-3 flex items-center justify-between cursor-pointer"
                  onClick={toggleStoresSection}
                >
                  Stores
                  <ChevronDown className={`w-4 h-4 transition-transform ${storesExpanded ? 'rotate-180' : ''}`} />
                </h3>
                {storesExpanded && (
                  <ul className="space-y-2 max-h-[250px] overflow-y-auto pr-2">
                    {stores?.map((store) => (
                      <li key={store.id}>
                        <label className="flex items-center gap-2 cursor-pointer">
                          <input 
                            type="checkbox" 
                            checked={filters.store === store.id}
                            onChange={() => handleStoreChange(store.id)}
                            className="rounded border-gray-300 text-primary-500 focus:ring-primary-500"
                          />
                          <span className="text-sm">{store.name}</span>
                          <span className="ml-auto text-xs text-gray-500">{store.dealsCount || 0}</span>
                        </label>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
              
              {/* Deal Type */}
              <div>
                <h3 
                  className="text-sm font-semibold mb-3 flex items-center justify-between cursor-pointer"
                  onClick={toggleDealTypeSection}
                >
                  Deal Type
                  <ChevronDown className={`w-4 h-4 transition-transform ${dealTypeExpanded ? 'rotate-180' : ''}`} />
                </h3>
                {dealTypeExpanded && (
                  <ul className="space-y-2">
                    {dealTypes.map((type) => (
                      <li key={type.name}>
                        <label className="flex items-center gap-2 cursor-pointer">
                          <input 
                            type="checkbox" 
                            checked={selectedDealTypes.includes(type.name)}
                            onChange={() => handleDealTypeChange(type.name, type.filter)}
                            className="rounded border-gray-300 text-primary-500 focus:ring-primary-500"
                          />
                          <span className="text-sm">{type.name}</span>
                        </label>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
              
              {/* Clear Filters Button */}
              <button 
                onClick={clearAllFilters}
                className="w-full py-2 px-4 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 transition-colors"
              >
                Clear All Filters
              </button>
            </div>
          </div>
        </aside>
        
        {/* Main Content Area */}
        <div className="md:col-span-3">
          {/* Filter bar for search and sort options */}
          <div className="mb-6">
            <FilterBar filters={filters} onFilterChange={handleFilterChange} />
          </div>
      
      {/* Deals list */}
      <div className="space-y-6">
        {isLoading ? (
          // Loading state with improved styling
          <div className="space-y-6">
            {[...Array(5)].map((_, index) => (
              <div 
                key={index} 
                className="h-40 animate-pulse rounded-lg bg-gray-200"
                style={{ animationDelay: `${index * 0.1}s` }}
              ></div>
            ))}
          </div>
        ) : isError ? (
          // Error state with improved styling
          <div className="rounded-xl bg-red-50 p-6 border border-red-100">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-red-800">Error loading deals</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>
                    {(error as any)?.message ||
                      'There was an error loading the deals. Please try again.'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : dealsResponse?.deals?.length === 0 ? (
          // Empty state with more engaging styling
          <div className="rounded-xl bg-white p-10 text-center shadow-sm border border-gray-100">
            <div className="mx-auto h-24 w-24 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-full w-full" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="mt-4 text-xl font-bold text-gray-900">No deals found</h3>
            <p className="mt-2 text-gray-600 max-w-md mx-auto">
              Try adjusting your filters or search term to find what you're looking for.
            </p>
            {isAuthenticated && (
              <div className="mt-8">
                <Link
                  to="/deals/create"
                  className="inline-flex items-center rounded-lg bg-primary-600 px-6 py-3 text-base font-medium text-white shadow-md hover:bg-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 hover:-translate-y-1"
                >
                  <PlusIcon className="mr-2 h-5 w-5" aria-hidden="true" />
                  Post a Deal
                </Link>
              </div>
            )}
          </div>
        ) : (
          // Deals list
          <div> 
                {/* Results found indicator and view toggle */}
                <div className="mb-4 pb-2 border-b border-gray-200 flex justify-between items-center">
                  <p className="text-sm text-gray-600">
                  
                    
                     Showing{' '}
                    <span className="font-medium">
                      {((dealsResponse?.page || 1) - 1) * (dealsResponse?.pageSize || 30) + 1}
                    </span>{' '}
                    to{' '}
                    <span className="font-medium">
                      {Math.min(
                        (dealsResponse?.page || 1) * (dealsResponse?.pageSize || 30),
                        dealsResponse?.totalCount || 0
                      )}
                    </span> of    <span className="font-semibold text-primary-700">
                      {dealsResponse?.totalCount ?? dealsResponse?.deals?.length ?? 0}
                    </span> Deals
                    
                    
                    {filters.search ? ` matching "${filters.search}"` : ''}
                  </p>

            


                  
                  {/* View toggle buttons */}
                  <div className="flex space-x-2">
                    <button 
                      onClick={() => toggleViewMode('grid')}
                      className={`p-2 rounded-md ${viewMode === 'grid' 
                        ? 'bg-primary-100 text-primary-700' 
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
                      title="Grid view"
                    >
                      <LayoutGrid size={16} />
                    </button>
                    <button 
                      onClick={() => toggleViewMode('list')}
                      className={`p-2 rounded-md ${viewMode === 'list' 
                        ? 'bg-primary-100 text-primary-700' 
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
                      title="List view"
                    >
                      <List size={16} />
                    </button>
                  </div>
                </div>
                
                {viewMode === 'grid' ? (
                  // Grid view
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {dealsResponse?.deals?.map((deal: Deal) => (
                      <GridDealCard key={deal.id} deal={deal} />
                    ))}
                  </div>
                ) : (
                  // List view
                  <div className="space-y-4">
                    {dealsResponse?.deals?.map((deal: Deal) => (
                      <ListDealCard key={deal.id} deal={deal} />
                    ))}
                  </div>
                )}
            
                {/* Pagination with improved styling */}
                <div className="mt-8">
            
                  <Pagination
                    currentPage={dealsResponse?.page || 1}
                    totalPages={dealsResponse?.totalPages || 1}
                    onPageChange={handlePageChange}
                    totalItems={dealsResponse?.totalCount}
                    pageSize={filters.pageSize}
                    itemName="Deals"
                  />
                </div>
          </div> 
        )}
      </div>
        </div>
      </div>
      
      {/* Scroll to top button */}
      {showScrollButton && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 p-3 rounded-full bg-primary-500 text-white shadow-lg hover:bg-primary-600 transition-all duration-300 z-50 animate-bounce-subtle"
          title="Scroll to top"
        >
          <ArrowUpCircle size={24} />
        </button>
      )}
    </div>
  );
};

export default DealsPage;


