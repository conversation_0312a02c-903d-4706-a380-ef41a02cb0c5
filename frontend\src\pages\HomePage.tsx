import React from 'react';
import { useQuery } from 'react-query';
import { 
  getNewestDeals, 
  getTrendingDeals, 
  getGettingWarmDeals, 
  getMostCommentedDeals 
} from '../services/dealService';
import { ArrowUp } from 'lucide-react';
import Hero from '../components/Hero';
import CategoryList from '../components/CategoryList';
import FeaturedDeals from '../components/FeaturedDeals';
import InfoSection from '../components/InfoSection';
import { useAuth } from '../hooks/useAuth';

const HomePage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [showScrollTop, setShowScrollTop] = React.useState(false);
  
  // Keep the existing queries to maintain compatibility with the rest of the app
  // These queries are also used by the FeaturedDeals component
  const { data: trendingDeals, isLoading: trendingLoading } = useQuery(
    'trendingDeals',
    () => getTrendingDeals(8)
  );
  
  const { data: newestDeals, isLoading: newestLoading } = useQuery(
    'newestDeals',
    () => getNewestDeals(8)
  );
  
  const { data: gettingWarmDeals, isLoading: gettingWarmLoading } = useQuery(
    'gettingWarmDeals',
    () => getGettingWarmDeals(10)
  );
  
  const { data: mostCommentedDeals, isLoading: mostCommentedLoading } = useQuery(
    'mostCommentedDeals',
    () => getMostCommentedDeals(10)
  );

  // Scroll to top functionality
  React.useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  
  return (
    <div className="min-h-screen">
      <main className="pb-20">
        <Hero />
        
        <section className="container mx-auto px-0 pt-10 pb-4">
          <CategoryList />
        </section>
        
        <section className="py-10">
          <FeaturedDeals />
        </section>
        
        <section className="pt-6 pb-16">
          <InfoSection />
        </section>
      </main>
      
      {/* Scroll to top button */}
      {showScrollTop && (
        <button 
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 rounded-full bg-deal-orange text-white shadow-lg hover:bg-deal-orange-dark transition-all duration-300 animate-fade-in"
          aria-label="Scroll to top"
        >
          <ArrowUp className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};

export default HomePage;

