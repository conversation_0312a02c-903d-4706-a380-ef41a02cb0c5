import React from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { useAuth } from '../hooks/useAuth';
import { LoginFormValues } from '../types';

// Validation schema
const loginSchema = Yup.object().shape({
  credential: Yup.string()
    .required('Username or Email is required'),
  password: Yup.string()
    .required('Password is required'),
});

const LoginPage: React.FC = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get the redirect path from the location state or default to '/'
  const from = (location.state as any)?.from?.pathname || '/';
  
  // Login mutation
  const loginMutation = useMutation(
    async (values: LoginFormValues) => {
      await login(values.credential, values.password);
    },
    {
      onSuccess: () => {
        toast.success('Logged in successfully');
        navigate(from, { replace: true });
      },
      onError: (error: any) => {
        toast.error(error.message || 'Login failed. Please check your credentials.');
      },
    }
  );
  
  // Initial form values
  const initialValues: LoginFormValues = {
    credential: '',
    password: '',
  };
  
  return (
    <div className="mx-auto max-w-md">
      <div className="rounded-lg bg-white p-8 shadow-sm">
        <div className="mb-6 text-center">
          <Link to="/" className="inline-flex items-center justify-center">
            <img src="/nicedeals-logo.png" alt="NiceDeals" className="h-16 object-contain" />
          </Link>
          <h2 className="mt-4 text-2xl font-bold text-gray-900">Sign in to your account</h2>
          <p className="mt-2 text-sm text-gray-600">
            Or{' '}
            <Link to="/register" className="font-medium text-primary-600 hover:text-primary-500">
              create a new account
            </Link>
          </p>
        </div>
        
        <Formik
          initialValues={initialValues}
          validationSchema={loginSchema}
          onSubmit={(values) => {
            loginMutation.mutate(values);
          }}
        >
          {({ isSubmitting }) => (
            <Form className="space-y-6">
              <div>
                <label htmlFor="credential" className="block text-sm font-medium text-gray-700">
                  Username or Email
                </label>
                <div className="mt-1">
                  <Field
                    id="credential"
                    name="credential"
                    type="text"
                    autoComplete="username email"
                    className="form-input"
                    placeholder="Username or Email"
                  />
                  <ErrorMessage name="credential" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              <div>
                <div className="flex items-center justify-between">
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                    Password
                  </label>
                  <div className="text-sm">
                    <a href="#" className="font-medium text-primary-600 hover:text-primary-500">
                      Forgot your password?
                    </a>
                  </div>
                </div>
                <div className="mt-1">
                  <Field
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    className="form-input"
                  />
                  <ErrorMessage name="password" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              <div>
                <Field
                  type="checkbox"
                  name="remember"
                  id="remember"
                  className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <label htmlFor="remember" className="ml-2 text-sm text-gray-900">
                  Remember me
                </label>
              </div>
              
              <div>
                <button
                  type="submit"
                  disabled={isSubmitting || loginMutation.isLoading}
                  className="flex w-full justify-center rounded-md border border-transparent bg-primary-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-75"
                >
                  {(isSubmitting || loginMutation.isLoading) ? 'Signing in...' : 'Sign in'}
                </button>
              </div>
            </Form>
          )}
        </Formik>
        
        {loginMutation.isError && (
          <div className="mt-4 rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Sign in failed</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>The username or password you entered is incorrect. Please try again.</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoginPage;
