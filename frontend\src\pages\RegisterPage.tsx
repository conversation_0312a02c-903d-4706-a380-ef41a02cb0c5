import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { useAuth } from '../hooks/useAuth';
import { RegisterFormValues } from '../types';

// Validation schema
const registerSchema = Yup.object().shape({
  username: Yup.string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be at most 20 characters')
    .matches(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens')
    .required('Username is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\\]{8,}$/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and can include special characters (except backslash)'
    )
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Confirm password is required'),
  termsAccepted: Yup.boolean()
    .oneOf([true], 'You must accept the Terms of Service and Privacy Policy')
    .required('You must accept the Terms of Service and Privacy Policy'),
});

const RegisterPage: React.FC = () => {
  const { register } = useAuth();
  const navigate = useNavigate();
  
  // Register mutation
  const registerMutation = useMutation(
    async (values: RegisterFormValues) => {
      await register(values.username, values.email, values.password, values.termsAccepted);
    },
    {
      onSuccess: () => {
        toast.success('Registration successful. Please check your email to verify your account before logging in.');
        navigate('/verify-email-sent');
      },
      onError: (error: any) => {
        toast.error(error.message || 'Registration failed. Please try again.');
      },
    }
  );
  
  // Initial form values
  const initialValues: RegisterFormValues = {
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    termsAccepted: false,
  };
  
  return (
    <div className="mx-auto max-w-md">
      <div className="rounded-lg bg-white p-8 shadow-sm">
        <div className="mb-6 text-center">
          <Link to="/" className="inline-flex items-center justify-center">
            <img src="/nicedeals-logo.png" alt="NiceDeals" className="h-16 object-contain" />
          </Link>
          <h2 className="mt-4 text-2xl font-bold text-gray-900">Create a new account</h2>
          <p className="mt-2 text-sm text-gray-600">
            Or{' '}
            <Link to="/login" className="font-medium text-primary-600 hover:text-primary-500">
              sign in to your existing account
            </Link>
          </p>
        </div>
        
        <Formik
          initialValues={initialValues}
          validationSchema={registerSchema}
          onSubmit={(values, { setSubmitting }) => {
            console.log('Form values before submission:', values);
            registerMutation.mutate(values);
          }}
        >
          {({ isSubmitting }) => (
            <Form className="space-y-6">
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                  Username
                </label>
                <div className="mt-1">
                  <Field
                    id="username"
                    name="username"
                    type="text"
                    autoComplete="username"
                    className="form-input"
                    placeholder="johndoe"
                  />
                  <ErrorMessage name="username" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email address
                </label>
                <div className="mt-1">
                  <Field
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    className="form-input"
                    placeholder="<EMAIL>"
                  />
                  <ErrorMessage name="email" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <div className="mt-1">
                  <Field
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="new-password"
                    className="form-input"
                  />
                  <ErrorMessage name="password" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                  Confirm Password
                </label>
                <div className="mt-1">
                  <Field
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    autoComplete="new-password"
                    className="form-input"
                  />
                  <ErrorMessage name="confirmPassword" component="div" className="mt-1 text-sm text-red-600" />
                </div>
              </div>
              
              <div className="flex items-center">
                <Field
                  type="checkbox"
                  name="termsAccepted"
                  id="termsAccepted"
                  className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <label htmlFor="termsAccepted" className="ml-2 block text-sm text-gray-900">
                  I agree to the{' '}
                  <a href="#" className="font-medium text-primary-600 hover:underline">
                    Terms of Service
                  </a>{' '}
                  and{' '}
                  <a href="#" className="font-medium text-primary-600 hover:underline">
                    Privacy Policy
                  </a>
                </label>
                <ErrorMessage name="termsAccepted" component="div" className="mt-1 text-sm text-red-600" />
              </div>
              
              <div>
                <button
                  type="submit"
                  disabled={isSubmitting || registerMutation.isLoading}
                  className="flex w-full justify-center rounded-md border border-transparent bg-primary-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-75"
                >
                  {(isSubmitting || registerMutation.isLoading) ? 'Creating account...' : 'Create account'}
                </button>
              </div>
            </Form>
          )}
        </Formik>
        
        {registerMutation.isError && (
          <div className="mt-4 rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Registration failed</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>
                    {(registerMutation.error as any)?.message ||
                      'There was an error creating your account. Please try again.'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RegisterPage;
