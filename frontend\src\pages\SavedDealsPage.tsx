import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';
import { BookmarkIcon } from '@heroicons/react/24/outline';
import { getSavedDeals } from '../services/dealService';
import DealCard from '../components/deals/DealCard';
import Pagination from '../components/common/Pagination';

const SavedDealsPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 30;

  const {
    data: dealsResponse,
    isLoading,
    isError,
    error
  } = useQuery(
    ['savedDeals', currentPage],
    () => getSavedDeals(currentPage, pageSize),
    {
      keepPreviousData: true
    }
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700">Error loading saved deals: {(error as Error).message}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Your Saved Deals</h1>
            <p className="text-sm text-gray-600">Deals you've bookmarked for later</p>
          </div>
        </div>

        {dealsResponse?.deals?.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm p-6 text-center">
            <BookmarkIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No saved deals</h3>
            <p className="mt-1 text-sm text-gray-500">
              You haven't saved any deals yet. Browse deals and click the save button to add them here.
            </p>
            <div className="mt-6">
              <Link
                to="/dealsBrowse"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Browse Deals
              </Link>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {dealsResponse?.deals?.map((deal) => (
                <DealCard key={deal.id} deal={deal} />
              ))}
            </div>

            <div className="mt-8">
              <Pagination
                currentPage={currentPage}
                totalPages={dealsResponse?.totalPages || 1}
                onPageChange={handlePageChange}
                totalItems={dealsResponse?.totalCount}
                pageSize={pageSize}
                itemName="Deals"
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SavedDealsPage; 