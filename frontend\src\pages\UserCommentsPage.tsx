import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';
import { ChatBubbleLeftIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { getUserComments } from '../services/commentService';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import Pagination from '../components/common/Pagination';
import Alert from '../components/common/Alert';
import { formatPrice } from '../utils/formatters';
import { getThumbnailUrl, handleImageError } from '../utils/imageUtils';
import { useAuth } from '../hooks/useAuth';

// Define a proper type for our user comments response
interface UserCommentResponse {
  data: Array<{
    id: number;
    text: string;
    dealId: number;
    userId: number;
    parentId?: number;
    createdAt: string;
    dealTitle: string;
    price?: number;
    originalPrice?: number;
    imageUrl?: string;
    thumbnailUrl?: string;
    dealOwnerUsername?: string;
    categoryName?: string;
    dealOwnerId?: number;
    categoryId?: number;
    commenterUsername?: string;
    status?: string;
  }>;
  pagination: {
    total: number;
    totalPages: number;
    page: number;
    pageSize: number;
  };
}

const UserCommentsPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 20;
  const { user } = useAuth(); // Get current user for image permission checks

  const {
    data: commentsData,
    isLoading,
    isError,
    error
  } = useQuery<UserCommentResponse>(
    ['userComments', currentPage, pageSize],
    () => getUserComments(currentPage, pageSize),
    {
      keepPreviousData: true,
      onSuccess: (data) => {
        console.log('Received comments data:', data);
        // Inspect the first comment if available
        if (data?.data?.length > 0) {
          console.log('First comment details:', data.data[0]);
          console.log('createdAt type:', typeof data.data[0].createdAt, 'value:', data.data[0].createdAt);
          console.log('thumbnailUrl type:', typeof data.data[0].thumbnailUrl, 'value:', data.data[0].thumbnailUrl);
        }
      }
    }
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (isError) {
    return (
      <Alert variant="destructive">
        {(error as Error)?.message || 'Failed to load your comments'}
      </Alert>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="mb-6 text-2xl font-bold">Your Comments</h1>

      {commentsData?.data?.length === 0 ? (
        <div className="rounded-md bg-gray-50 p-8 text-center">
          <ChatBubbleLeftIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">No comments yet</h3>
          <p className="mt-1 text-sm text-gray-500">
            You haven't made any comments on deals yet.
          </p>
          <div className="mt-6">
            <Link
              to="/dealsBrowse"
              className="inline-flex items-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              <ArrowLeftIcon className="mr-2 h-5 w-5" />
              Browse Deals
            </Link>
          </div>
        </div>
      ) : (
        <>
          <div className="space-y-4">
            {commentsData?.data?.map((comment) => (
              <div key={comment.id} className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
                <div className="flex">
                  {/* Deal thumbnail */}
                  <div className="w-24 sm:w-32 md:w-40 p-2">
                    <Link to={`/dealDetail/${comment.dealId}`}>
                      <div className="relative overflow-hidden rounded h-24 md:h-28 w-full bg-gray-100">
                        <img
                          src={getThumbnailUrl(comment)}
                          alt={comment.dealTitle}
                          className="h-full w-full object-cover"
                          onError={handleImageError}
                        />
                      </div>
                    </Link>
                  </div>

                  {/* Comment details */}
                  <div className="flex-1 p-4">
                    <div className="mb-1 text-sm text-gray-500">
                      <span>You commented on </span>
                      <Link
                        to={`/dealDetail/${comment.dealId}`}
                        className="font-medium text-primary-600 hover:text-primary-500"
                      >
                        {comment.dealTitle}
                      </Link>
                    </div>
                    
                    <div className="mb-2 text-sm text-gray-700">
                      {comment.text}
                    </div>
                    
                    <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500">
                      <span>
                        {/* Display date in a clean, readable format */}
                        {(() => {
                          if (!comment.createdAt) return 'Unknown date';
                          
                          try {
                            // Parse the date from MySQL format "YYYY-MM-DD HH:MM:SS"
                            const match = comment.createdAt.match(/^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/);
                            if (match) {
                              const [_, year, month, day, hour, minute] = match;
                              
                              // Format month name
                              const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                              const monthIndex = parseInt(month, 10) - 1;
                              const monthName = monthNames[monthIndex];
                              
                              // Format hour in 12-hour format
                              let hourNum = parseInt(hour, 10);
                              const ampm = hourNum >= 12 ? 'PM' : 'AM';
                              hourNum = hourNum % 12;
                              hourNum = hourNum === 0 ? 12 : hourNum;
                              
                              // Format the date in a nice readable format: "8 Mar 2025 at 10:30 PM"
                              return `${parseInt(day, 10)} ${monthName} ${year} at ${hourNum}:${minute} ${ampm}`;
                            }
                            
                            // Fallback
                            return comment.createdAt;
                          } catch (error) {
                            console.error('Error formatting date:', error);
                            return comment.createdAt;
                          }
                        })()}
                      </span>
                      
                      <span className="text-gray-400">•</span>
                      
                      <span>
                        Deal by: {comment.dealOwnerUsername}
                      </span>
                      
                      {comment.price !== undefined && comment.price !== null && (
                        <>
                          <span className="text-gray-400">•</span>
                          <span>
                            Price: <span className="font-semibold text-primary-600">{formatPrice(comment.price)}</span>
                            {comment.originalPrice !== undefined && comment.originalPrice !== null && comment.originalPrice > comment.price && (
                              <span className="ml-1 line-through text-gray-400">
                                {formatPrice(comment.originalPrice)}
                              </span>
                            )}
                          </span>
                        </>
                      )}
                      
                      {(comment.categoryName) && (
                        <>
                          <span className="text-gray-400">•</span>
                          <span>
                            Category: {comment.categoryName}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {commentsData?.pagination && commentsData.pagination.totalPages > 1 && (
            <div className="mt-8">
              <Pagination
                currentPage={currentPage}
                totalPages={commentsData.pagination.totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default UserCommentsPage; 