import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';
import { 
  PlusCircleIcon, 
  PencilIcon, 
  TrashIcon, 
  EyeIcon, 
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { dealService } from '../services/dealService';
import { formatPrice } from '../utils/formatters';
import { Deal } from '../types';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { getDealImage, handleImageError } from '../utils/imageUtils';
import { useAuth } from '../hooks/useAuth';

const UserDealsPage: React.FC = () => {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    const fetchUserDeals = async () => {
      try {
        setLoading(true);
        const fetchedDeals = await dealService.getCurrentUserDeals();
        console.log('Fetched deals:', fetchedDeals.map(deal => ({
          dealId: deal.id,
          userId: deal.userId,
          currentUserId: user?.id,
          thumbnailUrl: deal.thumbnailUrl,
          imageUrl: deal.imageUrl,
          status: deal.status,
          rawDeal: deal
        })));
        setDeals(fetchedDeals);
        setError(null);
      } catch (err) {
        console.error('Error fetching user deals:', err);
        setError('Failed to load your deals. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchUserDeals();
  }, [user]);

  // Get status badge style and text
  const getStatusBadge = (status: string) => {
    switch(status) {
      case 'active':
        return {
          text: 'Active',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          icon: <CheckCircleIcon className="mr-1 h-4 w-4" />
        };
      case 'pending':
        return {
          text: 'Pending Review',
          bgColor: 'bg-yellow-100',
          textColor: 'text-yellow-800',
          icon: <ClockIcon className="mr-1 h-4 w-4" />
        };
      case 'expired':
        return {
          text: 'Expired',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          icon: <ExclamationTriangleIcon className="mr-1 h-4 w-4" />
        };
      case 'rejected':
        return {
          text: 'Rejected',
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          icon: <ExclamationTriangleIcon className="mr-1 h-4 w-4" />
        };
      default:
        return {
          text: status.charAt(0).toUpperCase() + status.slice(1),
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          icon: null
        };
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto mt-8 px-4">
        <div className="flex justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto mt-8 max-w-4xl px-4">
        <div className="rounded-lg bg-red-50 p-4">
          <h2 className="text-lg font-semibold text-red-800">Error</h2>
          <p className="text-red-700">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto mt-8 px-4">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">My Deals</h1>
        <Link
          to="/deals/create"
          className="inline-flex items-center rounded-md bg-primary-600 px-4 py-2 text-sm font-medium text-white hover:bg-primary-700"
        >
          <PlusCircleIcon className="mr-2 h-5 w-5" />
          Post New Deal
        </Link>
      </div>

      {deals.length === 0 ? (
        <div className="rounded-lg border border-gray-200 bg-white p-8 text-center">
          <h2 className="mb-2 text-xl font-semibold text-gray-900">You haven't posted any deals yet</h2>
          <p className="mb-6 text-gray-600">
            Share a great deal you've found with the community!
          </p>
          <Link
            to="/deals/create"
            className="inline-flex items-center rounded-md bg-primary-600 px-4 py-2 text-sm font-medium text-white hover:bg-primary-700"
          >
            <PlusCircleIcon className="mr-2 h-5 w-5" />
            Post Your First Deal
          </Link>
        </div>
      ) : (
        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Deal
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Date Posted
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {deals.map((deal) => {
                  const statusBadge = getStatusBadge(deal.status);
                  const createdDate = deal.createdAt;
                  
                  return (
                    <tr key={deal.id} className="hover:bg-gray-50">
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0">
                            <img 
                              className="h-10 w-10 rounded-md object-cover" 
                              src={getDealImage(deal, {
                                currentUserId: user?.id,
                                useThumbnail: true,
                                forceOwner: true
                              })}
                              alt={deal.title}
                              onError={handleImageError}
                            />
                          </div>
                          <div className="ml-4">
                            <div className="max-w-xs overflow-hidden text-ellipsis text-sm font-medium text-gray-900">
                              {deal.title}
                            </div>
                            <div className="text-xs text-gray-500">
                              {deal.category?.name || 'Uncategorized'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                        {deal.price !== undefined ? formatPrice(deal.price) : '—'}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {createdDate && typeof createdDate === 'string' 
                          ? format(new Date(createdDate), 'MMM d, yyyy')
                          : '—'
                        }
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm">
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${statusBadge.bgColor} ${statusBadge.textColor}`}>
                          {statusBadge.icon}
                          {statusBadge.text}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                        <div className="flex space-x-2">
                          <Link
                            to={`/dealDetail/${deal.id}`}
                            className="rounded p-1 text-gray-500 hover:bg-blue-100 hover:text-blue-900"
                            title="View Deal"
                          >
                            <EyeIcon className="h-5 w-5" />
                          </Link>
                          <Link
                            to={`/deals/edit/${deal.id}`}
                            className="rounded p-1 text-gray-500 hover:bg-green-100 hover:text-green-900"
                            title="Edit Deal"
                          >
                            <PencilIcon className="h-5 w-5" />
                          </Link>
                          <button
                            className="rounded p-1 text-gray-500 hover:bg-red-100 hover:text-red-900"
                            title="Delete Deal"
                            onClick={() => {
                              if (window.confirm('Are you sure you want to delete this deal?')) {
                                // delete functionality can be added here
                                dealService.deleteDeal(deal.id)
                                  .then(() => {
                                    setDeals(deals.filter(d => d.id !== deal.id));
                                  })
                                  .catch(err => {
                                    console.error('Failed to delete deal:', err);
                                    alert('Failed to delete deal. Please try again.');
                                  });
                              }
                            }}
                          >
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserDealsPage;
