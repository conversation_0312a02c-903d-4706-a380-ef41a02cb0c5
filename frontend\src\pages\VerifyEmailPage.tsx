import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import toast from 'react-hot-toast';
import api from '../services/api';
import { useAuth } from '../hooks/useAuth';

const VerifyEmailPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { login } = useAuth();
  const [verifying, setVerifying] = useState(true);

  useEffect(() => {
    const verifyEmail = async () => {
      const token = searchParams.get('token');
      
      if (!token) {
        toast.error('Invalid verification link');
        navigate('/login');
        return;
      }

      try {
        const response = await api.get(`/auth/verify-email?token=${token}`);
        
        if (response.data.success) {
          toast.success('Email verified successfully! You can now log in.');
          navigate('/login');
        } else {
          toast.error(response.data.error || 'Verification failed');
          navigate('/login');
        }
      } catch (error: any) {
        toast.error(error.response?.data?.error || 'Verification failed');
        navigate('/login');
      } finally {
        setVerifying(false);
      }
    };

    verifyEmail();
  }, [navigate, searchParams]);

  if (verifying) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-semibold mb-4">Verifying your email...</h1>
          <p>Please wait while we verify your email address.</p>
        </div>
      </div>
    );
  }

  return null;
};

export default VerifyEmailPage;
