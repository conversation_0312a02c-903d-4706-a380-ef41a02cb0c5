import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';
import api from '../services/api';

const VerifyEmailSentPage: React.FC = () => {
  const [resending, setResending] = useState(false);

  const handleResendEmail = async () => {
    try {
      setResending(true);
      const response = await api.post('/auth/resend-verification');
      if (response.data.success) {
        toast.success('Verification email has been resent. Please check your inbox.');
      } else {
        toast.error(response.data.error || 'Failed to resend verification email');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to resend verification email');
    } finally {
      setResending(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center max-w-md p-6">
        <h1 className="text-2xl font-semibold mb-4">Check Your Email</h1>
        <p className="mb-4">
          We've sent you a verification email. Please check your inbox and click the verification link to activate your account.
        </p>
        <p className="text-sm text-gray-600 mb-6">
          If you don't see the email, check your spam folder.
        </p>
        <div className="space-y-4">
          <button
            onClick={handleResendEmail}
            disabled={resending}
            className="block w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {resending ? 'Sending...' : 'Resend Verification Email'}
          </button>
          <Link
            to="/login"
            className="block w-full px-4 py-2 text-sm font-medium text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50"
          >
            Return to Login
          </Link>
        </div>
      </div>
    </div>
  );
};

export default VerifyEmailSentPage;
