import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { getCategories, createCategory, updateCategory, deleteCategory } from '../../services/categoryService';
import { Category } from '../../types';

// Extended category interface with admin-specific properties
interface AdminCategory extends Category {
  dealsCount?: number;
}

// Validation schema for category form
const categorySchema = Yup.object({
  name: Yup.string().required('Name is required').max(50, 'Name must be at most 50 characters'),
  slug: Yup.string()
    .required('Slug is required')
    .matches(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
    .max(50, 'Slug must be at most 50 characters'),
});

const AdminCategoriesPage: React.FC = () => {
  const queryClient = useQueryClient();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<AdminCategory | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch categories
  const { data: categories = [], isLoading, isError } = useQuery('categories', getCategories);
  
  // Create category mutation
  const createMutation = useMutation(createCategory, {
    onSuccess: () => {
      queryClient.invalidateQueries('categories');
      setIsModalOpen(false);
    },
    onError: (error: any) => {
      setError(error.message || 'Error creating category');
    },
  });
  
  // Update category mutation
  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: Partial<AdminCategory> }) => updateCategory(id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('categories');
        setIsModalOpen(false);
        setEditingCategory(null);
      },
      onError: (error: any) => {
        setError(error.message || 'Error updating category');
      },
    }
  );
  
  // Delete category mutation
  const deleteMutation = useMutation(deleteCategory, {
    onSuccess: () => {
      queryClient.invalidateQueries('categories');
    },
    onError: (error: any) => {
      setError(error.message || 'Error deleting category');
    },
  });
  
  // Open modal for creating or editing
  const openModal = (category?: AdminCategory) => {
    setError(null);
    setEditingCategory(category || null);
    setIsModalOpen(true);
  };
  
  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingCategory(null);
    setError(null);
  };
  
  // Handle form submission
  const handleSubmit = (values: { name: string; slug: string }) => {
    if (editingCategory) {
      updateMutation.mutate({ id: editingCategory.id, data: values });
    } else {
      createMutation.mutate(values);
    }
  };
  
  // Handle category deletion
  const handleDelete = (id: number) => {
    if (window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      deleteMutation.mutate(id);
    }
  };
  
  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-');
  };

  return (
    <div className="py-6">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">Categories</h1>
          <button
            type="button"
            onClick={() => openModal()}
            className="inline-flex items-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            Add Category
          </button>
        </div>
      </div>
      
      <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <div className="py-4">
          {isError ? (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error loading categories</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>
                      There was an error loading the categories. Please try again.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : isLoading ? (
            <div className="text-center">
              <p className="text-gray-500">Loading categories...</p>
            </div>
          ) : categories.length === 0 ? (
            <div className="rounded-md bg-yellow-50 p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">No categories found</h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      No categories have been created yet. Click the "Add Category" button to create one.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="mt-8 overflow-hidden rounded-lg border border-gray-200 shadow-sm">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Name
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Slug
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Deals
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {categories.map((category) => (
                    <tr key={category.id}>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                        {category.name}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {category.slug}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {category.dealsCount || 0}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                        <button
                          type="button"
                          onClick={() => openModal(category)}
                          className="mr-2 inline-flex items-center rounded-md border border-gray-300 bg-white px-2.5 py-1.5 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                        >
                          <PencilIcon className="mr-1 h-4 w-4" />
                          Edit
                        </button>
                        <button
                          type="button"
                          onClick={() => handleDelete(category.id)}
                          disabled={(category.dealsCount ?? 0) > 0}
                          className={`inline-flex items-center rounded-md border border-gray-300 bg-white px-2.5 py-1.5 text-xs font-medium ${
                            (category.dealsCount ?? 0) > 0
                              ? 'cursor-not-allowed text-gray-400'
                              : 'text-red-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
                          }`}
                          title={
                            (category.dealsCount ?? 0) > 0
                              ? 'Cannot delete category with deals'
                              : 'Delete category'
                          }
                        >
                          <TrashIcon className="mr-1 h-4 w-4" />
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
      
      {/* Modal for creating/editing categories */}
      {isModalOpen && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">&#8203;</span>
            <div className="inline-block transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  type="button"
                  onClick={closeModal}
                  className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                >
                  <span className="sr-only">Close</span>
                  <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                </button>
              </div>
              <div>
                <div className="text-center">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">
                    {editingCategory ? 'Edit Category' : 'Create Category'}
                  </h3>
                </div>
                {error && (
                  <div className="mt-4 rounded-md bg-red-50 p-4">
                    <div className="flex">
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">Error</h3>
                        <div className="mt-2 text-sm text-red-700">{error}</div>
                      </div>
                    </div>
                  </div>
                )}
                <Formik
                  initialValues={{
                    name: editingCategory?.name || '',
                    slug: editingCategory?.slug || '',
                  }}
                  validationSchema={categorySchema}
                  onSubmit={handleSubmit}
                >
                  {({ isSubmitting, setFieldValue, values }) => (
                    <Form className="mt-5 space-y-4">
                      <div>
                        <label
                          htmlFor="name"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Name
                        </label>
                        <Field
                          type="text"
                          name="name"
                          id="name"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            const { value } = e.target;
                            setFieldValue('name', value);
                            // Only auto-generate slug if it's a new category or slug hasn't been manually edited
                            if (!editingCategory || values.slug === editingCategory.slug) {
                              setFieldValue('slug', generateSlug(value));
                            }
                          }}
                        />
                        <ErrorMessage
                          name="name"
                          component="p"
                          className="mt-1 text-sm text-red-600"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="slug"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Slug
                        </label>
                        <Field
                          type="text"
                          name="slug"
                          id="slug"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        />
                        <ErrorMessage
                          name="slug"
                          component="p"
                          className="mt-1 text-sm text-red-600"
                        />
                        <p className="mt-1 text-xs text-gray-500">
                          Used in URLs, should contain only lowercase letters, numbers, and hyphens.
                        </p>
                      </div>
                      <div className="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse">
                        <button
                          type="submit"
                          disabled={isSubmitting}
                          className="inline-flex w-full justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                          {isSubmitting
                            ? 'Saving...'
                            : editingCategory
                            ? 'Update'
                            : 'Create'}
                        </button>
                        <button
                          type="button"
                          onClick={closeModal}
                          className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                        >
                          Cancel
                        </button>
                      </div>
                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminCategoriesPage;
