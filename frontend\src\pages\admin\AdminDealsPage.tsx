import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { PlusIcon, PencilIcon, TrashIcon, CheckIcon } from '@heroicons/react/24/outline';
import { dealService } from '../../services/dealService';
import { categoryService } from '../../services/categoryService';
import { storeService } from '../../services/storeService';
import { formatDate } from '../../utils/formatters';
import Pagination from '../../components/common/Pagination';
import AlertModal from '../../components/common/AlertModal';
import { Deal, Category, Store, FilterParams, ProcessingProgress } from '../../types';
import { getThumbnailUrl, PLACEHOLDER_IMAGE, isExternalUrl } from '../../utils/imageUtils';
import toast from 'react-hot-toast';
import adminApi from '../../services/adminApi';


const AdminDealsPage: React.FC = () => {
  // Track initial render and prevent URL updates during init
  const initialRenderComplete = useRef(false);
  const isUpdatingFromUrl = useRef(false);
  const [searchParams, setSearchParams] = useSearchParams();

  // Initialize state from URL, but only on component mount
  const initialPage = parseInt(searchParams.get('page') || '1', 10);
  const initialStatus = searchParams.get('status') || 'all';
  const initialCategory = searchParams.get('category') || 'all';
  const initialStore = searchParams.get('store') || 'all';
  const initialSearchTerm = searchParams.get('search') || '';
  const initialSort = searchParams.get('sort') || 'created_desc'; // Add default sort

  // All state variables
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [itemsPerPage] = useState(30);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [dealToDelete, setDealToDelete] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>(initialStatus);
  const [categoryFilter, setCategoryFilter] = useState<string>(initialCategory);
  const [storeFilter, setStoreFilter] = useState<string>(initialStore);
  const [categories, setCategories] = useState<Category[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [sortField, setSortField] = useState<string>(initialSort);

  // Add state for selected deals
  const [selectedDeals, setSelectedDeals] = useState<number[]>([]);
  const [processingDeals, setProcessingDeals] = useState(false);
  const [processingProgress, setProcessingProgress] = useState<ProcessingProgress>({
    total: 0,
    completed: 0,
    currentAction: '',
    error: ''
  });
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [batchDeleteModalOpen, setBatchDeleteModalOpen] = useState(false);

  // Function to sync URL with current state
  const updateUrlFromState = useCallback(() => {
    // Skip URL updates during the initial render or when updating from URL
    if (!initialRenderComplete.current || isUpdatingFromUrl.current) return;

    const newParams = new URLSearchParams();
    
    if (currentPage > 1) newParams.set('page', currentPage.toString());
    if (statusFilter !== 'all') newParams.set('status', statusFilter);
    if (categoryFilter !== 'all') newParams.set('category', categoryFilter);
    if (storeFilter !== 'all') newParams.set('store', storeFilter);
    if (searchTerm) newParams.set('search', searchTerm);
    if (sortField) newParams.set('sort', sortField);
    
    // Use replace to avoid browser history build-up
    setSearchParams(newParams, { replace: true });
  }, [currentPage, statusFilter, categoryFilter, storeFilter, searchTerm, sortField, setSearchParams]);

  // Effect to update state from URL changes
  useEffect(() => {
    if (isUpdatingFromUrl.current) return;

    const urlPage = parseInt(searchParams.get('page') || '1', 10);
    const urlStatus = searchParams.get('status') || 'all';
    const urlCategory = searchParams.get('category') || 'all';
    const urlStore = searchParams.get('store') || 'all';
    const urlSearch = searchParams.get('search') || '';
    const urlSort = searchParams.get('sort') || 'created_desc';

    // Only update if values are different
    if (urlPage !== currentPage) setCurrentPage(urlPage);
    if (urlStatus !== statusFilter) setStatusFilter(urlStatus);
    if (urlCategory !== categoryFilter) setCategoryFilter(urlCategory);
    if (urlStore !== storeFilter) setStoreFilter(urlStore);
    if (urlSearch !== searchTerm) setSearchTerm(urlSearch);
    if (urlSort !== sortField) setSortField(urlSort);
  }, [searchParams, currentPage, statusFilter, categoryFilter, storeFilter, searchTerm, sortField]);

  // Effect to update URL from state changes
  useEffect(() => {
    if (!initialRenderComplete.current) {
      initialRenderComplete.current = true;
      return;
    }

    if (isUpdatingFromUrl.current) return;

    isUpdatingFromUrl.current = true;
    console.log('Updating URL from state change:', {
      currentPage,
      statusFilter,
      categoryFilter,
      storeFilter,
      searchTerm,
      sortField
    });
    updateUrlFromState();
    setTimeout(() => {
      isUpdatingFromUrl.current = false;
    }, 0);
  }, [
    searchParams,
    currentPage,
    statusFilter,
    categoryFilter,
    storeFilter,
    searchTerm,
    sortField,
    updateUrlFromState
  ]);

  // Fetch deals whenever filters/pagination changes
  const fetchDeals = useCallback(async () => {
    setLoading(true);
    try {
      console.log('Fetching deals with params:', {
        page: currentPage,
        limit: itemsPerPage,
        status: statusFilter,
        category: categoryFilter,
        store: storeFilter,
        search: searchTerm,
        sort: sortField
      });

      const filters: FilterParams = {
        page: currentPage,
        limit: itemsPerPage,
        sort: sortField
      };

      if (statusFilter !== 'all') {
        filters.status = statusFilter;
      }

      if (categoryFilter !== 'all') {
        filters.category = categoryFilter;
      }

      if (storeFilter !== 'all') {
        filters.store = storeFilter;
      }

      if (searchTerm) {
        filters.search = searchTerm;
      }

      // Get actual deals from the API
      const response = await dealService.getAdminDeals(filters);
      console.log('Deals response:', {
        totalDeals: response.totalCount,
        dealsReceived: response.deals.length,
        currentPage: response.page,
        totalPages: response.totalPages
      });

      setDeals(response.deals);
      setTotalCount(response.totalCount);
      setTotalPages(response.totalPages);
    } catch (err) {
      setError('Failed to fetch deals');
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [currentPage, statusFilter, categoryFilter, storeFilter, searchTerm, itemsPerPage, sortField]);

  // Fetch deals when dependencies change
  useEffect(() => {
    fetchDeals();
  }, [fetchDeals]);

  // Fetch categories and stores only once on component mount
  useEffect(() => {
    const fetchCategoriesAndStores = async () => {
      try {
        const [categoriesData, storesData] = await Promise.all([
          categoryService.getCategories(),
          storeService.getStores()
        ]);
        setCategories(categoriesData);
        setStores(storesData);
      } catch (err) {
        console.error('Failed to fetch categories or stores:', err);
      }
    };

    fetchCategoriesAndStores();
  }, []);

  const handlePageChange = useCallback((page: number) => {
    console.log('Page change requested:', { from: currentPage, to: page });
    if (page === currentPage) {
      console.log('Same page requested, ignoring');
      return;
    }
    
    // Update URL directly instead of relying on state change
    const newParams = new URLSearchParams(searchParams);
    newParams.set('page', page.toString());
    
    isUpdatingFromUrl.current = true;
    setSearchParams(newParams, { replace: true });
    setCurrentPage(page);
    
    setTimeout(() => {
      isUpdatingFromUrl.current = false;
    }, 0);
  }, [currentPage, searchParams, setSearchParams]);

  const handleDeleteClick = (id: string) => {
    setDealToDelete(id);
    setDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (!dealToDelete) return;

    try {
      await adminApi.deleteDeal(parseInt(dealToDelete, 10));
      setDeals(deals.filter(deal => {
        if (typeof deal.id === 'number' && dealToDelete) {
          return deal.id !== parseInt(dealToDelete, 10);
        }
        return true;
      }));
      toast.success('Deal deleted successfully');
      setDeleteModalOpen(false);
      setDealToDelete(null);
    } catch (error) {
      console.error('Error deleting deal:', error);
      toast.error('Failed to delete deal');
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
  };

  // Filter change handlers
  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
    setCurrentPage(1);
  };

  const handleCategoryFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setCategoryFilter(e.target.value);
    setCurrentPage(1);
  };

  const handleStoreFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStoreFilter(e.target.value);
    setCurrentPage(1);
  };

  const handleSearchTermChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Select/deselect all deals
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      const pendingDealsIds = deals
        .filter(deal => deal.status === 'pending')
        .map(deal => typeof deal.id === 'number' ? deal.id : parseInt(String(deal.id), 10));
      setSelectedDeals(pendingDealsIds);
    } else {
      setSelectedDeals([]);
    }
  };

  // Select/deselect a single deal
  const handleSelectDeal = (dealId: number, checked: boolean) => {
    if (checked) {
      setSelectedDeals(prev => [...prev, dealId]);
    } else {
      setSelectedDeals(prev => prev.filter(id => id !== dealId));
    }
  };

  // Process the selected deals
  const handleActivateDeals = async () => {
    if (selectedDeals.length === 0) {
      toast.error('No deals selected for activation');
      return;
    }

    setProcessingDeals(true);
    setProcessingProgress({
      total: selectedDeals.length,
      completed: 0,
      currentAction: 'Starting batch processing...',
      error: ''
    });
    setShowProcessingModal(true);

    try {
      // Process each deal one by one
      for (let i = 0; i < selectedDeals.length; i++) {
        const dealId = selectedDeals[i];
        const deal = deals.find(d => {
          const dId = typeof d.id === 'number' ? d.id : parseInt(String(d.id), 10);
          return dId === dealId;
        });

        if (!deal) continue;

        try {
          // Update progress for current deal
          setProcessingProgress(prev => ({
            ...prev,
            currentAction: `Processing deal ${i + 1}/${selectedDeals.length}: ${deal.title}`,
            error: ''
          }));

          // Activate the deal
          await dealService.activatePendingDeal(dealId);
          
          // Update progress after successful activation
          setProcessingProgress(prev => ({
            ...prev,
            completed: i + 1,
            currentAction: `Successfully processed: ${deal.title}`,
            error: ''
          }));

          // Wait 5 seconds between deals to avoid overloading
          if (i < selectedDeals.length - 1) {
            setProcessingProgress(prev => ({
              ...prev,
              currentAction: `Processing complete. Waiting 5 seconds before next deal (${i + 2}/${selectedDeals.length})...`
            }));
            await new Promise(resolve => setTimeout(resolve, 5000));
          } else {
            setProcessingProgress(prev => ({
              ...prev,
              currentAction: 'All deals processed successfully!'
            }));
          }
        } catch (error: any) {
          console.error(`Error processing deal ${dealId}:`, error);
          setProcessingProgress(prev => ({
            ...prev,
            error: `Error processing deal: ${deal.title} - ${error.message}`
          }));
          // Wait 2 seconds to show the error before continuing
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      // Refresh the list after processing all deals
      await fetchDeals();
      toast.success(`Successfully activated ${selectedDeals.length} deal(s)`);
      setSelectedDeals([]);
    } catch (error) {
      console.error('Error activating deals:', error);
      toast.error('Failed to activate some deals. Please check the console for details.');
    } finally {
      // Keep modal open for a moment to show final status
      setTimeout(() => {
        setProcessingDeals(false);
        setShowProcessingModal(false);
      }, 2000);
    }
  };

  const handleSort = (field: string) => {
    const newSort = sortField.endsWith('_desc') ? field + '_asc' : field + '_desc';
    setSortField(newSort);
    setCurrentPage(1);
    
    // Update URL
    const newParams = new URLSearchParams(searchParams);
    newParams.set('sort', newSort);
    newParams.set('page', '1');
    setSearchParams(newParams);
  };

  const getSortIcon = (field: string) => {
    if (!sortField.startsWith(field)) return '↕️';
    return sortField.endsWith('_desc') ? '↓' : '↑';
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Deals Management</h1>
          <p className="mt-2 text-sm text-gray-700">
            A list of all deals with options to edit, delete, and change their status.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Link
            to="/deals/create"
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:w-auto"
          >
            <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            Add Deal
          </Link>
        </div>
      </div>

      <div className="mt-6 bg-white shadow overflow-x-auto sm:rounded-md">
        {/* Filters */}
        <div className="border-b border-gray-200 bg-white p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-4">
            {/* Search */}
            <div className="col-span-1 sm:col-span-2">
              <form onSubmit={handleSearch}>
                <label htmlFor="search" className="block text-sm font-medium text-gray-700">
                  Search
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <input
                    type="text"
                    name="search"
                    id="search"
                    value={searchTerm}
                    onChange={handleSearchTermChange}
                    className="flex-1 min-w-0 block w-full px-3 py-2 rounded-md border border-gray-300 focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                    placeholder="Search deals by title or description..."
                  />
                  <button
                    type="submit"
                    className="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Search
                  </button>
                </div>
              </form>
            </div>

            {/* Status Filter */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                id="status"
                name="status"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                value={statusFilter}
                onChange={handleStatusFilterChange}
              >
                <option value="all">All Statusess</option>
                <option value="active">Active</option>
                <option value="expired">Expired</option>
                <option value="pending">Pending</option>
              </select>
            </div>

            {/* Category Filter */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                Category
              </label>
              <select
                id="category"
                name="category"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                value={categoryFilter}
                onChange={handleCategoryFilterChange}
              >
                <option value="all">All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Store Filter */}
            <div>
              <label htmlFor="store" className="block text-sm font-medium text-gray-700">
                Store
              </label>
              <select
                id="store"
                name="store"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                value={storeFilter}
                onChange={handleStoreFilterChange}
              >
                <option value="all">All Stores</option>
                {stores.map((store) => (
                  <option key={store.id} value={store.id}>
                    {store.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Deals Table */}
        {loading ? (
          <div className="text-center py-10">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary-600 border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" />
            <p className="mt-2 text-sm text-gray-500">Loading deals...</p>
          </div>
        ) : error ? (
          <div className="text-center py-10">
            <p className="text-red-500">{error}</p>
          </div>
        ) : deals.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-gray-500">No deals found.</p>
          </div>
        ) : 
          <>
            <table className="w-full divide-y divide-gray-300">
              <thead className="bg-gray-50">
                <tr>
                  {/* Add checkbox column for pending deals */}
                  {statusFilter === 'pending' && (
                    <th scope="col" className="relative w-12 px-4 sm:w-16 sm:px-6">
                      <input
                        type="checkbox"
                        className="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        onChange={handleSelectAll}
                        checked={selectedDeals.length > 0 && selectedDeals.length === deals.filter(d => d.status === 'pending').length}
                        disabled={deals.filter(d => d.status === 'pending').length === 0}
                      />
                    </th>
                  )}
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap cursor-pointer" onClick={() => handleSort('title')}>
                    Deal {getSortIcon('title')}
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap cursor-pointer" onClick={() => handleSort('category')}>
                    Category {getSortIcon('category')}
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap cursor-pointer" onClick={() => handleSort('store')}>
                    Store {getSortIcon('store')}
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                    Status
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap cursor-pointer" onClick={() => handleSort('created')}>
                    Created {getSortIcon('created')}
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap cursor-pointer" onClick={() => handleSort('updated')}>
                    Updated {getSortIcon('updated')}
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                    Expires
                  </th>
                  <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {deals.map((deal) => {
                  const dealId = typeof deal.id === 'number' ? deal.id : parseInt(String(deal.id), 10);
                  const isPendingDeal = deal.status === 'pending';
                  
                  return (
                    <tr key={deal.id}>
                      {/* Add checkbox for pending deals */}
                      {statusFilter === 'pending' && (
                        <td className="relative w-12 px-4 sm:w-16 sm:px-6">
                          {isPendingDeal && (
                            <input
                              type="checkbox"
                              className="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                              checked={selectedDeals.includes(dealId)}
                              onChange={(e) => handleSelectDeal(dealId, e.target.checked)}
                            />
                          )}
                        </td>
                      )}
                      <td className="px-4 py-4">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {isExternalUrl(deal.imageUrl) ? (
                              <img
                                className="h-10 w-10 rounded-md object-cover"
                                src={PLACEHOLDER_IMAGE}
                                alt={deal.title}
                              />
                            ) : (
                              <img
                                className="h-10 w-10 rounded-md object-cover"
                                src={getThumbnailUrl(deal) || PLACEHOLDER_IMAGE}
                                alt={deal.title}
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = PLACEHOLDER_IMAGE;
                                }}
                              />
                            )}
                          </div>
                          <div className="ml-4 min-w-0">
                            {/* Added min-w-0 to allow text truncation */}
                            <div className="text-sm font-medium text-gray-900  max-w-xs">
                              {deal.title}
                            </div>
                            <div className="text-sm text-gray-500">
                              {deal.price ? (
                                <span className="font-medium text-primary-600">
                                  ${deal.price.toFixed(2)}
                                  {deal.originalPrice && (
                                    <span className="line-through text-gray-400 ml-2">
                                      ${deal.originalPrice.toFixed(2)}
                                    </span>
                                  )}
                                </span>
                              ) : (
                                <span className="text-gray-400">No price</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className=" max-w-[150px]">{deal.category?.name || 'N/A'}</div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className=" max-w-[150px]">{deal.store?.name || 'N/A'}</div>
                      </td>
                      <td className="px-4 py-4">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(
                            deal.status
                          )}`}
                        >
                          {deal.status.charAt(0).toUpperCase() + deal.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-500 whitespace-nowrap">
                        {formatDate(deal.createdAt || '')}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-500 whitespace-nowrap">
                        {formatDate(deal.updatedAt || '')}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-500 whitespace-nowrap">
                        {deal.expiresAt ? formatDate(deal.expiresAt) : 'No expiry'}
                      </td>
                      <td className="px-4 py-4 text-right text-sm font-medium whitespace-nowrap">
                        <div className="flex justify-end space-x-2">
                          <Link
                            to={`/admin/deals/edit/${deal.id?.toString()}`}
                            className="text-primary-600 hover:text-primary-900"
                          >
                            <PencilIcon className="h-5 w-5" aria-hidden="true" />
                            <span className="sr-only">Edit</span>
                          </Link>
                          <button
                            onClick={() => handleDeleteClick(deal.id.toString())}
                            className="text-red-600 hover:text-red-900"
                          >
                            <TrashIcon className="h-5 w-5" aria-hidden="true" />
                            <span className="sr-only">Delete</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
            
            {/* Activate/Delete Deals Buttons */}
            {deals.length > 0 && (
              <div className="px-4 py-4 sm:px-6 border-t border-gray-200 bg-gray-50">
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-700">
                    {selectedDeals.length} deal{selectedDeals.length !== 1 ? 's' : ''} selected
                  </div>
                  <div className="flex space-x-4">
                    {statusFilter === 'pending' && (
                      <button
                        type="button"
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={handleActivateDeals}
                        disabled={selectedDeals.length === 0 || processingDeals}
                      >
                        <CheckIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                        Activate Selected Deals
                      </button>
                    )}
                    <button
                      type="button"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={() => setBatchDeleteModalOpen(true)}
                      disabled={selectedDeals.length === 0 || processingDeals}
                    >
                      <TrashIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                      Delete Selected Deals
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Pagination and Results Count */}
            <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex items-center">
                <p className="text-sm text-gray-700">
                  {deals.length > 0 ? (
                    <>
                      Viewing{' '}
                      <span className="font-medium">
                        {(currentPage - 1) * itemsPerPage + 1}
                      </span>{' '}
                      to{' '}
                      <span className="font-medium">
                        {Math.min(currentPage * itemsPerPage, totalCount)}
                      </span>{' '}
                      of{' '}
                      <span className="font-medium">{totalCount}</span> deals
                    </>
                  ) : (
                    'No deals found'
                  )}
                </p>
              </div>
              {totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              )}
            </div>
          </>
        }
      </div>

      {/* Delete Confirmation Modal */}
      <AlertModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setDealToDelete(null);
        }}
        title="Delete Deal"
        message="Are you sure you want to delete this deal? This action cannot be undone."
        confirmLabel="Delete"
        confirmButtonType="danger"
        onConfirm={confirmDelete}
      />

      {/* Processing Modal */}
      <AlertModal
        isOpen={showProcessingModal}
        onClose={() => {}}
        title="Processing Pending Deals"
        message={
          <div className="space-y-4">
            <p className="font-medium text-gray-900">
              Processing {processingProgress.completed} of {processingProgress.total} selected deals
            </p>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary-600 h-2 rounded-full transition-all duration-500"
                style={{
                  width: `${(processingProgress.completed / processingProgress.total) * 100}%`
                }}
              />
            </div>
            
            {/* Current Action */}
            {processingProgress.currentAction && (
              <div className="text-sm text-gray-600">
                <p>{processingProgress.currentAction}</p>
              </div>
            )}
            
            {/* Error Message */}
            {processingProgress.error && (
              <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                <p>{processingProgress.error}</p>
              </div>
            )}
          </div>
        }
        confirmLabel=""
        confirmButtonType="primary"
        showCancelButton={false}
        showConfirmButton={false}
        onConfirm={() => {}}
      />

      {/* Batch Delete Confirmation Modal */}
      <AlertModal
        isOpen={batchDeleteModalOpen}
        onClose={() => setBatchDeleteModalOpen(false)}
        title="Delete Selected Deals"
        message={
          <div>
            <p>Are you sure you want to delete {selectedDeals.length} selected deal{selectedDeals.length !== 1 ? 's' : ''}?</p>
            <p className="mt-2 text-sm text-red-600">This action cannot be undone and will remove all associated data including images and backup records.</p>
          </div>
        }
        confirmLabel="Delete"
        confirmButtonType="danger"
        onConfirm={async () => {
          try {
            setBatchDeleteModalOpen(false);
            setProcessingDeals(true);
            setShowProcessingModal(true);
            setProcessingProgress({
              completed: 0,
              total: selectedDeals.length,
              currentAction: 'Starting batch deletion...',
              error: ''
            });

            for (let i = 0; i < selectedDeals.length; i++) {
              const dealId = selectedDeals[i];
              setProcessingProgress(prev => ({
                ...prev,
                completed: i,
                currentAction: `Deleting deal ${i + 1} of ${selectedDeals.length}...`,
                error: ''
              }));

              try {
                await adminApi.deleteDeal(dealId);
                await new Promise(resolve => setTimeout(resolve, 500));
              } catch (error) {
                console.error(`Error deleting deal ${dealId}:`, error);
                setProcessingProgress(prev => ({
                  ...prev,
                  error: `Failed to delete deal ${dealId}. Continuing with remaining deals...`
                }));
                await new Promise(resolve => setTimeout(resolve, 2000));
              }
            }

            // Update the deals list after batch deletion
            setDeals(deals.filter(deal => {
              const dealId = typeof deal.id === 'number' ? deal.id : parseInt(String(deal.id), 10);
              return !selectedDeals.includes(dealId);
            }));
            setSelectedDeals([]);
            toast.success('Selected deals deleted successfully');
          } catch (error) {
            console.error('Batch deletion error:', error);
            toast.error('Failed to complete batch deletion');
          } finally {
            setProcessingDeals(false);
            setShowProcessingModal(false);
            setProcessingProgress({
              completed: 0,
              total: 0,
              currentAction: '',
              error: ''
            });
          }
        }}
      />
    </div>
  );
};

export default AdminDealsPage;

