import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'react-hot-toast';
import { 
  PLACEHOLDER_IMAGE, 
  getThumbnailUrl, 
  isExternalUrl,
  getImageUrl,
  getAdminImageUrl,
  handleImageError
} from '../../utils/imageUtils';
import { useCategories } from '../../hooks/useCategories';
import { useStores } from '../../hooks/useStores';
import { useImageLocalization } from '../../hooks/useImageLocalization';
import { Category, Store } from '../../types';
import adminApi from '../../services/adminApi';
import aiService from '../../services/aiService';
import { SparklesIcon, DocumentTextIcon, TrashIcon } from '@heroicons/react/24/outline';

// SVG for Twitter/X logo
const XLogo: React.FC = () => (
  <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
);

const dealSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  price: z.number().min(0, 'Price must be positive'),
  originalPrice: z.number().min(0, 'Original price must be positive').nullable().optional(),
  url: z.string().url('Must be a valid URL'),
  status: z.enum(['active', 'expired', 'deleted', 'pending']),
  storeId: z.number().int().positive('Store is required'),
  categoryId: z.number().int().positive('Category is required'),
  coupon: z.string().max(50, 'Coupon code must be at most 50 characters').optional(),
  imageUrl: z.string()
    .min(1, 'Image URL is required')
    .refine(
      (val) => val.startsWith('http') || val.startsWith('https') || val.startsWith('/uploads/'),
      'Image URL must be a valid URL or a valid uploads path'
    )
});

type DealFormData = z.infer<typeof dealSchema>;

export default function AdminEditDealPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [currentImageUrl, setCurrentImageUrl] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [isImprovingTitle, setIsImprovingTitle] = useState(false);
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { categories } = useCategories();
  const { stores } = useStores();
  const { localizeImage, isProcessingImage } = useImageLocalization();

  const { register, handleSubmit, reset, setValue, watch, getValues, formState: { errors, isSubmitting } } = useForm<DealFormData>({
    resolver: zodResolver(dealSchema)
  });

  useEffect(() => {
    const fetchDeal = async () => {
      try {
        setIsLoading(true);
        const response = await adminApi.getDeal(Number(id));
        console.log('Fetched deal data:', response); // Debug log
        
        // Make sure we have all required data before resetting the form
        if (!response.deal || !response.deal.store || !response.deal.category) {
          throw new Error('Invalid deal data received');
        }
        
        // Ensure we have valid IDs before setting the form
        const storeId = Number(response.deal.store.id);
        const categoryId = Number(response.deal.category.id);
        
        if (isNaN(storeId) || isNaN(categoryId)) {
          throw new Error('Invalid store or category ID');
        }
        
        // Reset form with validated data
        reset({
          title: response.deal.title,
          description: response.deal.description,
          price: response.deal.price,
          originalPrice: response.deal.originalPrice,
          url: response.deal.url,
          status: response.deal.status,
          storeId: storeId,
          categoryId: categoryId,
          coupon: response.deal.coupon,
          imageUrl: response.deal.imageUrl
        });

        setCurrentImageUrl(response.deal.imageUrl);
        setShowPreview(!!response.deal.imageUrl);
      } catch (error) {
        console.error('Error fetching deal:', error);
        toast.error('Failed to fetch deal data');
        navigate('/admin/deals');
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchDeal();
    }
  }, [id, navigate, reset]);

  // Watch imageUrl changes
  const imageUrl = watch('imageUrl');
  
  useEffect(() => {
    console.log('Current image URL:', imageUrl); // Debug log
    console.log('Is external URL:', isExternalUrl(imageUrl)); // Debug log
    console.log('Constructed URL:', getImageUrl(imageUrl)); // Debug log
    setCurrentImageUrl(imageUrl || '');
    setShowPreview(!!imageUrl);
  }, [imageUrl]);

  // Watch these values for conditional rendering
  const description = watch('description');
  const url = watch('url');

  const handleLocalizeImage = async () => {
    const localizedUrl = await localizeImage(currentImageUrl, Number(id));
    if (localizedUrl) {
      setCurrentImageUrl(localizedUrl);
      setValue('imageUrl', localizedUrl);
      setShowPreview(true);
    }
  };

  const handleImproveTitle = async () => {
    try {
      const currentTitle = watch('title');
      
      if (!currentTitle) {
        toast.error('Please enter a title first');
        return;
      }
      
      setIsImprovingTitle(true);
      const improvedTitle = await aiService.improveTitle(currentTitle);
      
      if (improvedTitle) {
        setValue('title', improvedTitle, { shouldValidate: true });
        toast.success('Title improved successfully');
      }
    } catch (error) {
      console.error('Error improving title:', error);
      toast.error('Failed to improve title');
    } finally {
      setIsImprovingTitle(false);
    }
  };

  const handleGenerateDescription = async () => {
    try {
      const currentUrl = watch('url');
      
      if (!currentUrl) {
        toast.error('Please enter a product URL first');
        return;
      }
      
      setIsGeneratingDescription(true);
      const generatedDescription = await aiService.generateDescription(currentUrl, getValues('title'));
      
      if (generatedDescription) {
        setValue('description', generatedDescription, { shouldValidate: true });
        toast.success('Description generated successfully');
      }
    } catch (error) {
      console.error('Error generating description:', error);
      toast.error('Failed to generate description');
    } finally {
      setIsGeneratingDescription(false);
    }
  };

  const onSubmit = async (data: DealFormData) => {
    try {
      await adminApi.updateDeal(Number(id), data);
      toast.success('Deal updated successfully');
      navigate('/admin/deals');
    } catch (error) {
      console.error('Error updating deal:', error);
      toast.error('Failed to update deal');
    }
  };

  const handleDelete = async () => {
    try {
      await adminApi.deleteDeal(Number(id));
      toast.success('Deal deleted successfully');
      navigate('/admin/deals');
    } catch (error) {
      console.error('Error deleting deal:', error);
      toast.error('Failed to delete deal');
    }
  };

  const handlePromoteOnTwitter = () => {
    const { title, price, originalPrice, status, imageUrl, storeId } = getValues();
    
    // Get store name
    const selectedStore = stores.find((store: Store) => store.id === storeId);
    const storeName = selectedStore?.name || '';
    
    // Format the price message based on whether there's an original price
    let priceMessage;
    if (originalPrice && originalPrice > 0) {
      priceMessage = `Price currently down to £${price} from £${originalPrice}`;
    } else {
      priceMessage = `Price just £${price} currently`;
    }
    
    // Create the tweet text in the exact format requested
    const tweetText = `Nicedeals UK presents ${title} on ${storeName.replace(/\s+/g, '')}\n\n${priceMessage}\n\nhttps://www.nicedeals.app/dealDetail/${id}\n\n#deal #ukdeals #bargains #${storeName.replace(/\s+/g, '')}`;
    
    // Open Twitter intent URL
    window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(tweetText)}`, '_blank');
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Edit Deal</h1>
      
      <form onSubmit={handleSubmit(onSubmit)} className="max-w-2xl space-y-6">
        <div className="form-group">
          <label className="form-label">Title</label>
          <div className="flex gap-2">
            <input {...register('title')} className="form-input w-full" />
            <button
              type="button"
              onClick={handleImproveTitle}
              disabled={isImprovingTitle}
              className="flex items-center gap-1 px-3 py-2 bg-gradient-to-r from-purple-500 to-indigo-600 text-white text-sm font-medium rounded hover:from-purple-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 transition-all duration-200 whitespace-nowrap"
              title="Use AI to improve the title"
            >
              <SparklesIcon className="h-4 w-4" />
              {isImprovingTitle ? 'Improving...' : 'Improve Title'}
            </button>
          </div>
          {errors.title && <span className="form-error">{errors.title.message}</span>}
        </div>

        <div className="form-group">
          <label className="form-label">Description</label>
          <div className="flex flex-col gap-2">
            <textarea {...register('description')} className="form-textarea w-full h-32" />
            {(!description || description.trim() === '') && url && (
              <button
                type="button"
                onClick={handleGenerateDescription}
                disabled={isGeneratingDescription || !url}
                className="self-start flex items-center gap-1 px-3 py-2 bg-gradient-to-r from-green-500 to-teal-600 text-white text-sm font-medium rounded hover:from-green-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 transition-all duration-200 whitespace-nowrap"
                title="Use AI to generate a description from the product URL"
              >
                <DocumentTextIcon className="h-4 w-4" />
                {isGeneratingDescription ? 'Generating...' : 'Grab a Description'}
              </button>
            )}
          </div>
          {errors.description && <span className="form-error">{errors.description.message}</span>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="form-group">
            <label className="form-label">Price</label>
            <input 
              {...register('price', { valueAsNumber: true })} 
              type="number" 
              step="0.01" 
              className="form-input w-full" 
            />
            {errors.price && <span className="form-error">{errors.price.message}</span>}
          </div>

          <div className="form-group">
            <label className="form-label">Original Price</label>
            <input 
              {...register('originalPrice', { valueAsNumber: true })} 
              type="number" 
              step="0.01" 
              className="form-input w-full" 
            />
            {errors.originalPrice && <span className="form-error">{errors.originalPrice.message}</span>}
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">URL</label>
          <input {...register('url')} className="form-input w-full" />
          {errors.url && <span className="form-error">{errors.url.message}</span>}
        </div>

        <div className="form-group">
          <label className="form-label">Coupon Code</label>
          <input {...register('coupon')} className="form-input w-full" placeholder="Optional: Enter coupon code if applicable" />
          {errors.coupon && <span className="form-error">{errors.coupon.message}</span>}
        </div>

        <div className="form-group">
          <label className="form-label">Image URL</label>
          <input {...register('imageUrl')} className="form-input w-full" />
          {errors.imageUrl && <span className="form-error">{errors.imageUrl.message}</span>}
          
          {showPreview && currentImageUrl && (
            <div className="mt-2">
              {isExternalUrl(currentImageUrl) && (
                <button
                  type="button"
                  onClick={handleLocalizeImage}
                  disabled={isProcessingImage}
                  className="mb-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessingImage ? 'Localizing...' : 'Localize Image'}
                </button>
              )}
              {isExternalUrl(currentImageUrl) ? (
                <div className="flex flex-col items-start gap-2">
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md mb-2">
                    <p className="text-yellow-800 text-sm">
                      External image detected. Please localize this image before using its.
                    </p>
                  </div>
                  <img
                    src={PLACEHOLDER_IMAGE}
                    alt="Deal placeholder"
                    className="max-w-xs rounded shadow-sm"
                  />
                </div>
              ) : (
                <img
                  src={getAdminImageUrl({ imageUrl: currentImageUrl })}
                  alt="Deal preview"
                  className="h-32 w-32 rounded-lg object-cover"
                  onError={handleImageError}
                />
              )}
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="form-group">
            <label className="form-label">Status</label>
            <select {...register('status')} className="form-select w-full">
              <option value="active">Active</option>
              <option value="pending">Pending</option>
              <option value="expired">Expired</option>
              <option value="deleted">Deleted</option>
            </select>
            {errors.status && <span className="form-error">{errors.status.message}</span>}
          </div>

          <div className="form-group">
            <label className="form-label">Store</label>
            <select {...register('storeId', { valueAsNumber: true })} className="form-select w-full">
              <option value="">Select Store</option>
              {stores?.map((store: Store) => (
                <option key={store.id} value={store.id}>{store.name}</option>
              ))}
            </select>
            {errors.storeId && <span className="form-error">{errors.storeId.message}</span>}
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">Category</label>
          <select {...register('categoryId', { valueAsNumber: true })} className="form-select w-full">
            <option value="">Select Category</option>
            {categories?.map((category: Category) => (
              <option key={category.id} value={category.id}>{category.name}</option>
            ))}
          </select>
          {errors.categoryId && <span className="form-error">{errors.categoryId.message}</span>}
        </div>

        <div className="flex justify-between items-center mt-8">
          <button
            type="button"
            onClick={() => setShowDeleteModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <TrashIcon className="h-5 w-5 mr-2" />
            Delete Deal
          </button>
          
          <div className="flex space-x-4">
            <button
              type="button"
              onClick={() => navigate('/admin/deals')}
              className="btn btn-secondary"
            >
              Cancel
            </button>
            {/* Only show "Promote on X" button if status is active and image is local */}
            {watch('status') === 'active' && !isExternalUrl(currentImageUrl) && currentImageUrl && (
              <button
                type="button"
                onClick={handlePromoteOnTwitter}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                <XLogo />
                Promote on X
              </button>
            )}
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn btn-primary"
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </form>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
              <div className="sm:flex sm:items-start">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                  <TrashIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Delete Deal
                  </h3>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      Are you sure you want to delete this deal? This action cannot be undone and will remove all associated data including images and backup records.
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDelete}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:w-auto sm:text-sm"
                  onClick={() => setShowDeleteModal(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
