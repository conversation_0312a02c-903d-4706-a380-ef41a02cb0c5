import React, { useState, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  XMarkIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';
import { getStores, createStore, updateStore, deleteStore, uploadStoreLogo } from '../../services/storeService';
import { Store } from '../../types';
import { getAffiliateUrl } from '../../utils/formatters';

// Extended store interface with admin-specific properties
interface AdminStore extends Store {
  dealsCount?: number;
}

// Validation schema for store form
const storeSchema = Yup.object({
  name: Yup.string().required('Name is required').max(50, 'Name must be at most 50 characters'),
  url: Yup.string().url('Must be a valid URL').nullable(),
});

const AdminStoresPage: React.FC = () => {
  const queryClient = useQueryClient();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingStore, setEditingStore] = useState<AdminStore | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Fetch stores
  const { data: stores = [], isLoading, isError } = useQuery<Store[]>('stores', () => getStores());
  
  // Create store mutation
  const createMutation = useMutation(createStore, {
    onSuccess: () => {
      queryClient.invalidateQueries('stores');
      setIsModalOpen(false);
    },
    onError: (error: any) => {
      setError(error.message || 'Error creating store');
    },
  });
  
  // Update store mutation
  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: Partial<AdminStore> }) => updateStore(id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('stores');
        setIsModalOpen(false);
        setEditingStore(null);
      },
      onError: (error: any) => {
        setError(error.message || 'Error updating store');
      },
    }
  );
  
  // Delete store mutation
  const deleteMutation = useMutation(deleteStore, {
    onSuccess: () => {
      queryClient.invalidateQueries('stores');
    },
    onError: (error: any) => {
      setError(error.message || 'Error deleting store');
    },
  });
  
  // Upload logo mutation
  const uploadLogoMutation = useMutation(
    ({ id, file }: { id: number; file: File }) => uploadStoreLogo(id, file),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('stores');
      },
      onError: (error: any) => {
        alert(error.message || 'Error uploading logo');
      },
    }
  );
  
  // Open modal for creating or editing
  const openModal = (store?: AdminStore) => {
    setError(null);
    setEditingStore(store || null);
    setIsModalOpen(true);
  };
  
  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingStore(null);
    setError(null);
  };
  
  // Handle form submission
  const handleSubmit = (values: { name: string; url?: string }) => {
    if (editingStore) {
      updateMutation.mutate({ id: editingStore.id, data: values });
    } else {
      createMutation.mutate(values);
    }
  };
  
  // Handle store deletion
  const handleDelete = (id: number) => {
    if (window.confirm('Are you sure you want to delete this store? This action cannot be undone.')) {
      deleteMutation.mutate(id);
    }
  };
  
  // Handle logo upload
  const handleLogoUpload = (store: AdminStore) => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
      fileInputRef.current.onchange = (e) => {
        const files = (e.target as HTMLInputElement).files;
        if (files && files.length > 0) {
          const file = files[0];
          
          // Validate file size (1MB max)
          if (file.size > 1024 * 1024) {
            alert('Logo file size must be less than 1MB.');
            return;
          }
          
          // Validate file type
          if (!file.type.startsWith('image/')) {
            alert('Only image files are allowed.');
            return;
          }
          
          uploadLogoMutation.mutate({ id: store.id, file });
        }
      };
    }
  };

  return (
    <div className="py-6">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">Stores</h1>
          <button
            type="button"
            onClick={() => openModal()}
            className="inline-flex items-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            Add Store
          </button>
        </div>
      </div>
      
      {/* Hidden file input for logo upload */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/*"
      />
      
      <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
        <div className="py-4">
          {isError ? (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error loading stores</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>
                      There was an error loading the stores. Please try again.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : isLoading ? (
            <div className="text-center">
              <p className="text-gray-500">Loading stores...</p>
            </div>
          ) : stores.length === 0 ? (
            <div className="rounded-md bg-yellow-50 p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">No stores found</h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      No stores have been created yet. Click the "Add Store" button to create one.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="mt-8 overflow-hidden rounded-lg border border-gray-200 shadow-sm">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Store
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      URL
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Deals
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {stores.map((store) => (
                    <tr key={store.id}>
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0">
                            {store.logoUrl ? (
                              <img
                                className="h-10 w-10 rounded-full object-cover"
                                src={`${process.env.REACT_APP_API_URL}${store.logoUrl}`}
                                alt={store.name}
                              />
                            ) : (
                              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100">
                                <span className="text-xs font-medium text-gray-500">
                                  {store.name.substring(0, 2).toUpperCase()}
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{store.name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {store.url ? (
                          <a
                            href={store.id === -42 ? getAffiliateUrl(store.url, -42) : store.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary-600 hover:text-primary-900"
                          >
                            {store.url}
                          </a>
                        ) : (
                          <span className="text-gray-400">No URL</span>
                        )}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {store.dealsCount || 0}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                        <button
                          type="button"
                          onClick={() => handleLogoUpload(store)}
                          className="mr-2 inline-flex items-center rounded-md border border-gray-300 bg-white px-2.5 py-1.5 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                        >
                          <PhotoIcon className="mr-1 h-4 w-4" />
                          Logo
                        </button>
                        <button
                          type="button"
                          onClick={() => openModal(store)}
                          className="mr-2 inline-flex items-center rounded-md border border-gray-300 bg-white px-2.5 py-1.5 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                        >
                          <PencilIcon className="mr-1 h-4 w-4" />
                          Edit
                        </button>
                        <button
                          type="button"
                          onClick={() => handleDelete(store.id)}
                          disabled={(store.dealsCount ?? 0) > 0}
                          className={`inline-flex items-center rounded-md border border-gray-300 bg-white px-2.5 py-1.5 text-xs font-medium ${
                            (store.dealsCount ?? 0) > 0
                              ? 'cursor-not-allowed text-gray-400'
                              : 'text-red-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
                          }`}
                          title={
                            (store.dealsCount ?? 0) > 0
                              ? 'Cannot delete store with deals'
                              : 'Delete store'
                          }
                        >
                          <TrashIcon className="mr-1 h-4 w-4" />
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
      
      {/* Modal for creating/editing stores */}
      {isModalOpen && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">&#8203;</span>
            <div className="inline-block transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  type="button"
                  onClick={closeModal}
                  className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                >
                  <span className="sr-only">Close</span>
                  <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                </button>
              </div>
              <div>
                <div className="text-center">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">
                    {editingStore ? 'Edit Store' : 'Create Store'}
                  </h3>
                </div>
                {error && (
                  <div className="mt-4 rounded-md bg-red-50 p-4">
                    <div className="flex">
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">Error</h3>
                        <div className="mt-2 text-sm text-red-700">{error}</div>
                      </div>
                    </div>
                  </div>
                )}
                <Formik
                  initialValues={{
                    name: editingStore?.name || '',
                    url: editingStore?.url || '',
                  }}
                  validationSchema={storeSchema}
                  onSubmit={handleSubmit}
                >
                  {({ isSubmitting }) => (
                    <Form className="mt-5 space-y-4">
                      <div>
                        <label
                          htmlFor="name"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Name
                        </label>
                        <Field
                          type="text"
                          name="name"
                          id="name"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                          placeholder="e.g., Amazon"
                        />
                        <ErrorMessage
                          name="name"
                          component="p"
                          className="mt-1 text-sm text-red-600"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="url"
                          className="block text-sm font-medium text-gray-700"
                        >
                          URL
                        </label>
                        <Field
                          type="url"
                          name="url"
                          id="url"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                          placeholder="https://example.com"
                        />
                        <ErrorMessage
                          name="url"
                          component="p"
                          className="mt-1 text-sm text-red-600"
                        />
                      </div>
                      <div className="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse">
                        <button
                          type="submit"
                          disabled={isSubmitting}
                          className="inline-flex w-full justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                          {isSubmitting
                            ? 'Saving...'
                            : editingStore
                            ? 'Update'
                            : 'Create'}
                        </button>
                        <button
                          type="button"
                          onClick={closeModal}
                          className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                        >
                          Cancel
                        </button>
                      </div>
                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminStoresPage;
