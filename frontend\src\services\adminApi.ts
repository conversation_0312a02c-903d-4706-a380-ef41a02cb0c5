import api from './api';
import { DashboardStats, DashboardResponse } from '../types';

interface DealData {
  title: string;
  description?: string;
  price: number;
  originalPrice?: number | null;
  url: string;
  status: 'active' | 'expired' | 'deleted' | 'pending';
  storeId: number;
  categoryId: number;
  imageUrl: string;
  coupon?: string;
}

const adminApi = {
  // Dashboard
  getDashboardStats: async (): Promise<DashboardResponse> => {
    const response = await api.get('/admin/dashboard');
    return response.data;
  },

  // Deals
  getDeal: async (id: number) => {
    const response = await api.get(`/admin/deals/${id}`);
    return response.data;
  },

  getDeals: async (params?: any) => {
    const response = await api.get('/admin/deals', { params });
    return response.data;
  },

  updateDeal: async (id: number, data: DealData) => {
    const response = await api.put(`/admin/deals/${id}`, data);
    return response.data;
  },

  createDeal: async (data: DealData) => {
    const response = await api.post('/admin/deals', data);
    return response.data;
  },

  deleteDeal: async (id: number) => {
    const response = await api.delete(`/admin/deals/${id}`);
    return response.data;
  },

  // Categories
  getCategories: async () => {
    const response = await api.get('/admin/categories');
    return response.data.data?.categories || [];
  },

  // Stores
  getStores: async () => {
    const response = await api.get('/admin/stores');
    return response.data.data?.stores || [];
  },

  // Users
  getUsers: async (params: any = {}) => {
    const response = await api.get('/admin/users', { params });
    return response.data;
  },

  // Scrapers
  getScraperLogs: async () => {
    const response = await api.get('/admin/scrapers');
    return response.data;
  },

  runScraper: async (scraperId: string) => {
    const response = await api.post(`/admin/scrapers/run/${scraperId}`);
    return response.data;
  }
};

export default adminApi;