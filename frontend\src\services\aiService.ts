import api from './api';

// Interface for OpenRouter API responses
interface OpenRouterResponse {
  text: string;
  [key: string]: any;
}

/**
 * Service for AI-related API calls
 */
const aiService = {
  /**
   * Improves a title using the OpenRouter AI model
   * 
   * @param title - The original title to improve
   * @returns The improved title
   */
  async improveTitle(title: string): Promise<string> {
    try {
      const prompt = `Can you change the wording of this title to be more concise while keeping the essential product title? The original title is: "${title}". Give me ONLY the improved title with no explanations or formatting or quotes or product attributes.`;
      
      const response = await api.post<OpenRouterResponse>('/ai/generate', {
        prompt
      });
      
      // Extract just the title from the response
      let improvedTitle = response.data.text.trim();
      
      // Remove any markdown formatting, asterisks, option numbers, etc.
      improvedTitle = improvedTitle
        .replace(/\*\*Option \d+( \([^)]+\))?:\*\*/g, '')
        .replace(/\*Option \d+( \([^)]+\))?:\*/g, '')
        .replace(/Option \d+[:.]/g, '')
        .replace(/\*\*/g, '')
        .replace(/\*/g, '')
        .replace(/^[-*•]/g, '')
        .trim();
      
      // If there are multiple lines, just take the first non-empty line
      if (improvedTitle.includes('\n')) {
        const lines = improvedTitle.split('\n')
          .map(line => line.trim())
          .filter(line => line && !line.toLowerCase().startsWith('option') && !line.toLowerCase().startsWith('here are'));
        
        if (lines.length > 0) {
          improvedTitle = lines[0];
        }
      }
      
      return improvedTitle;
    } catch (error) {
      console.error('Error improving title:', error);
      throw new Error('Failed to improve title with AI');
    }
  },

  /**
   * Generates a product description from a URL using the OpenRouter AI model
   * 
   * @param url - The product URL to scrape and generate a description from
   * @returns The generated description
   */
  async generateDescription(url: string, title: string): Promise<string> {
    try {
      const timestampedUrl = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;
      
      const response = await api.post<OpenRouterResponse>('/ai/generate', {
        prompt: timestampedUrl,
        title
      });
      
      let description = response.data.text.trim();
      description = description
        .replace(/\*\*/g, '')
        .replace(/\*/g, '')
        .replace(/^[-*•]/g, '')
        .trim();
      
      return description;
    } catch (error) {
      console.error('Error generating description:', error);
      throw new Error('Failed to generate description with AI');
    }
  }
};

export default aiService;