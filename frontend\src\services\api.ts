import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { camelize<PERSON>eys } from 'humps';
import toast from 'react-hot-toast';

const DEBUG: number = 1; // Debug flag: Set to 1 to enable console logging, 0 to disable

// Debug logging helper
const debugLog = (...args: any[]) => {
  if (DEBUG > 0) {
    console.log(...args);
  }
};

// Debug error logging helper
const debugError = (...args: any[]) => {
  if (DEBUG > 0) {
    console.error(...args);
  }
};

// Create Axios instance
const axiosInstance: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5010/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Log configuration
debugLog('API Configuration:', {
  baseURL: axiosInstance.defaults.baseURL,
  headers: axiosInstance.defaults.headers
});

// If you find yourself needing to debug, enable the following:
// axiosInstance.defaults.validateStatus = function (status) {
//   return status >= 200 && status < 500; // Default is >= 200 && < 300
// };

// REMOVED: Initial token setup from localStorage caused server-side errors.
// The token is now added only within the client-side request interceptor.

// Request interceptor for API calls
axiosInstance.interceptors.request.use(
  (config) => {
    // Only access localStorage on the client-side
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token');
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
        debugLog('Added token to request:', token.substring(0, 10) + '...');
      } else {
        debugLog('No token available from localStorage');
      }
    } else {
      debugLog('Running in server context, no localStorage access');
    }
    
    // Log request details
    debugLog('API Request:', {
      url: config.url,
      method: config.method,
      baseURL: config.baseURL,
      headers: config.headers,
      params: config.params
    });
    
    return config;
  },
  (error) => {
    debugError('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for API calls
axiosInstance.interceptors.response.use(
  (response) => {
    // If response contains a token, save it
    if (response.data?.data?.token) {
      localStorage.setItem('token', response.data.data.token);
      axiosInstance.defaults.headers.common.Authorization = `Bearer ${response.data.data.token}`;
    }
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    // Handle rate limit errors (429)
    if (error.response?.status === 429) {
      debugError('Rate limit exceeded:', error.response.data);
      // Show user-friendly message
      const message = error.response.data?.message || 'Too many requests. Please try again later.';
      toast.error(message);
    }
    
    // Handle unauthorized errors (401)
    if (error.response?.status === 401 && !originalRequest._retry) {
      // Clear the token if unauthorized
      localStorage.removeItem('token');
      delete axiosInstance.defaults.headers.common.Authorization;
    }
    
    return Promise.reject(error);
  }
);

// Add response interceptor to transform snake_case to camelCase
axiosInstance.interceptors.response.use(response => {
  if (response.data) {
    response.data = camelizeKeys(response.data);
  }
  return response;
});

// API service
const api = {
  // Set auth token
  setAuthToken: (token: string) => {
    if (token) {
      localStorage.setItem('token', token);
      axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
    } else {
      localStorage.removeItem('token');
      delete axiosInstance.defaults.headers.common.Authorization;
    }
  },
  
  // Remove auth token
  removeAuthToken: () => {
    delete axiosInstance.defaults.headers.common.Authorization;
  },
  
  // GET request
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return axiosInstance.get<T>(url, config);
  },
  
  // POST request
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return axiosInstance.post<T>(url, data, config);
  },
  
  // PUT request
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return axiosInstance.put<T>(url, data, config);
  },
  
  // PATCH request
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return axiosInstance.patch<T>(url, data, config);
  },
  
  // DELETE request
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return axiosInstance.delete<T>(url, config);
  },
};

export default api;
