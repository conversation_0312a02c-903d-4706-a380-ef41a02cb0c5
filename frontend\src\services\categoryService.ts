import { Category } from '../types';
import api from './api';

/**
 * Get all categories
 * @returns Array of categories
 */
export const getCategories = async (): Promise<Category[]> => {
  try {
    const response = await api.get('/categories');
    return response.data;
  } catch (error) {
    console.error('Error in getCategories:', error);
    throw error;
  }
};

/**
 * Get a category by ID
 * @param id Category ID
 * @returns Category details
 */
export const getCategoryById = async (id: number): Promise<Category> => {
  try {
    const response = await api.get(`/categories/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error in getCategoryById:', error);
    throw error;
  }
};

/**
 * Create a new category
 * @param categoryData Category data
 * @returns Created category
 */
export const createCategory = async (categoryData: { name: string; slug: string }): Promise<Category> => {
  try {
    const response = await api.post('/categories', categoryData);
    return response.data;
  } catch (error) {
    console.error('Error in createCategory:', error);
    throw error;
  }
};

/**
 * Update a category
 * @param id Category ID
 * @param categoryData Category data to update
 * @returns Updated category
 */
export const updateCategory = async (id: number, categoryData: Partial<Category>): Promise<Category> => {
  try {
    const response = await api.put(`/categories/${id}`, categoryData);
    return response.data;
  } catch (error) {
    console.error('Error in updateCategory:', error);
    throw error;
  }
};

/**
 * Delete a category
 * @param id Category ID
 * @returns Success message
 */
export const deleteCategory = async (id: number): Promise<{ message: string }> => {
  try {
    const response = await api.delete(`/categories/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error in deleteCategory:', error);
    throw error;
  }
};

// Export the functions as a service object
export const categoryService = {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory
};
