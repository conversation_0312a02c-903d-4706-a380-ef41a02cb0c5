import api from './api';
import { Comment, PaginatedResponse } from '../types';

// Define UserCommentResponse interface to match our backend response
interface UserCommentResponse {
  data: Array<{
    id: number;
    text: string;
    dealId: number;
    userId: number;
    parentId?: number;
    createdAt: string;
    dealTitle: string;
    price?: number;
    originalPrice?: number;
    imageUrl?: string;
    thumbnailUrl?: string;
    dealOwnerUsername?: string;
    categoryName?: string;
    dealOwnerId?: number;
    categoryId?: number;
    commenterUsername?: string;
  }>;
  pagination: {
    total: number;
    totalPages: number;
    page: number;
    pageSize: number;
  };
}

// Get comments for a deal
export const getComments = async (dealId: number): Promise<Comment[]> => {
  try {
    const response = await api.get<Comment[]>(`/comments/${dealId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching comments:', error);
    throw new Error('Failed to fetch comments');
  }
};

// Get comments by the current user with pagination
export const getUserComments = async (page: number = 1, pageSize: number = 20): Promise<UserCommentResponse> => {
  try {
    const response = await api.get<UserCommentResponse>(`/comments/user/me?page=${page}&pageSize=${pageSize}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user comments:', error);
    throw new Error('Failed to fetch your comments');
  }
};

// Add a comment
export const addComment = async (dealId: number, text: string, parentId?: number): Promise<Comment> => {
  try {
    const response = await api.post<Comment>('/comments', {
      dealId,
      text,
      parentId
    });
    return response.data;
  } catch (error) {
    console.error('Error adding comment:', error);
    throw new Error('Failed to add comment');
  }
};

// Update a comment
export const updateComment = async (commentId: number, text: string): Promise<Comment> => {
  try {
    const response = await api.put<Comment>(`/comments/${commentId}`, {
      text
    });
    return response.data;
  } catch (error) {
    console.error('Error updating comment:', error);
    throw new Error('Failed to update comment');
  }
};

// Delete a comment
export const deleteComment = async (commentId: number): Promise<void> => {
  try {
    await api.delete(`/comments/${commentId}`);
  } catch (error) {
    console.error('Error deleting comment:', error);
    throw new Error('Failed to delete comment');
  }
}; 