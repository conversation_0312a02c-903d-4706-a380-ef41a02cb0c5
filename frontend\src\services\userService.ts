import { User, UserProfile, UpdateProfileFormValues } from '../types';
import api from './api';

/**
 * Get a user profile by ID
 */
export const getUserProfile = async (userId: number): Promise<UserProfile> => {
  try {
    const response = await api.get(`/users/${userId}/profile`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to fetch user profile');
  }
};

/**
 * Update user profile
 * @param userId - User ID
 * @param data - Profile update data
 */
export const updateUserProfile = async (
  userId: number,
  data: UpdateProfileFormValues
): Promise<User> => {
  try {
    // Create payload with only the necessary fields
    const payload: any = {
      username: data.username,
      email: data.email,
    };

    // Only add password fields if new password is provided
    if (data.newPassword) {
      payload.currentPassword = data.currentPassword;
      payload.newPassword = data.newPassword;
    }

    const response = await api.put<User>(`/users/${userId}/profile`, payload);
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 'Failed to update profile'
    );
  }
};

/**
 * Get user's saved deals
 * @param userId - User ID
 */
export const getSavedDeals = async (userId: number) => {
  try {
    const response = await api.get<any[]>(`/users/${userId}/saved-deals`);
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 'Failed to fetch saved deals'
    );
  }
};

/**
 * Save a deal
 * @param dealId - Deal ID
 */
export const saveDeal = async (dealId: number) => {
  try {
    const response = await api.post(`/saved-deals`, { dealId });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to save deal');
  }
};

/**
 * Unsave a deal
 * @param dealId - Deal ID
 */
export const unsaveDeal = async (dealId: number) => {
  try {
    const response = await api.delete(`/saved-deals/${dealId}`);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to unsave deal');
  }
};

/**
 * Get user's activity (votes, comments, posted deals)
 * @param userId - User ID
 */
export const getUserActivity = async (userId: number) => {
  try {
    const response = await api.get<any[]>(`/users/${userId}/activity`);
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 'Failed to fetch user activity'
    );
  }
};

/**
 * Upload avatar image for a user
 * @param userId - User ID
 * @param file - Image file to upload
 */
export const uploadAvatar = async (userId: number, file: File): Promise<User> => {
  try {
    const formData = new FormData();
    formData.append('avatar', file);
    
    const response = await api.post(`/users/${userId}/avatar`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to upload avatar');
  }
};

/**
 * Get deals created by a user
 * @param userId - User ID
 */
export const getUserDeals = async (userId: number) => {
  try {
    const response = await api.get(`/users/${userId}/deals`);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch user deals');
  }
};

/**
 * Get deals saved by a user
 * @param userId - User ID
 */
export const getUserSavedDeals = async (userId: number) => {
  try {
    const response = await api.get(`/users/${userId}/saved-deals`);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch saved deals');
  }
};
