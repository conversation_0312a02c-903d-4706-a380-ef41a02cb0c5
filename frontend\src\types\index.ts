// Admin Types

export interface DashboardStats {
  totalUsers: number;
  totalDeals: number;
  activeDeals: number;
  pendingDeals: number;
  newUsersToday: number;
  newDealsToday: number;
  userGrowth: number;
  dealGrowth: number;
}

export interface RecentActivity {
  id: number;
  type: 'user' | 'deal';
  action: string;
  title: string;
  timestamp: string;
  user?: {
    id: number;
    username: string;
  };
}

export interface DashboardResponse {
  stats: DashboardStats;
  recentActivity: RecentActivity[];
}

export interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface FilterParams {
  page?: number;
  limit?: number;
  status?: string;
  category?: string;
  store?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  sort?: string;
}

// User types
export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserProfile extends User {
  avatarUrl?: string;
  dealsCount: number;
  commentsCount: number;
}

export interface UpdateProfileFormValues {
  username: string;
  email: string;
  currentPassword?: string;
  newPassword?: string;
  confirmNewPassword?: string;
  bio?: string;
}

export interface ProcessingProgress {
  total: number;
  completed: number;
  currentAction?: string;
  error?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

// Deal types
export interface Deal {
  id: number;
  title: string;
  description: string;
  url: string;
  dealUrl?: string;
  imageUrl?: string;
  thumbnailUrl?: string;
  price?: number;
  originalPrice?: number;
  discount?: number;
  expiresAt?: string;
  coupon?: string;
  status: 'active' | 'expired' | 'deleted' | 'pending';
  source: 'manual' | 'hotukdeals' | 'hukd' | 'system';
  temperature: number;
  totalVotes: number;
  upvotes?: number;
  downvotes?: number;
  categoryId: number;
  categoryName?: string;
  userId: number;
  userUsername?: string;
  storeId?: number;
  storeName?: string;
  createdAt?: string;
  updatedAt?: string;
  isPending?: boolean;
  commentCount?: number;
  userVote: number;
  externalId?: string;

  // Optional nested objects for backwards compatibility
  category?: {
    id: number;
    name: string;
  };
  store?: {
    id: number;
    name: string;
    logoUrl?: string;
  };
  _count?: {
    votes: number;
  };
}

export interface DealListResponse {
  deals: Deal[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface DealFilters {
  search?: string;
  category?: number;
  store?: number;
  status?: 'active' | 'expired' | 'all';
  sort?: 'newest' | 'hottest' | 'price-asc' | 'price-desc' | 'most-commented' | 'getting-warm' | 'trending';
  page?: number;
  pageSize?: number;
  minPrice?: number;
  maxPrice?: number;
  dealType?: string;
}

// Comment types
export interface Comment {
  id: number;
  text: string;
  dealId: number;
  userId: number;
  parentId?: number;
  created_at: string; // Use snake_case to match backend
  updatedAt: string;
  username: string; 
  avatarUrl?: string; 
  _count?: {
    replies: number;
  };
  replies?: Comment[];
}

// Category and Store types
export interface Category {
  id: number;
  name: string;
  slug: string;
  _count?: {
    deals: number;
  };
  dealsCount?: number;
}

export interface Store {
  id: number;
  name: string;
  logoUrl?: string;
  url?: string;
  _count?: {
    deals: number;
  };
  dealsCount?: number;
}

// Scraper types
export interface ScraperLog {
  id: number;
  source: string;
  status: 'started' | 'completed' | 'failed';
  dealsFound?: number;
  dealsAdded?: number;
  error?: string;
  dataFile?: string;
  createdAt: string;
  duration?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// Form types
export interface LoginFormValues {
  credential: string;
  password: string;
}

export interface RegisterFormValues {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  termsAccepted: boolean;
}

export interface DealFormValues {
  title: string;
  description: string;
  dealUrl: string;
  imageUrl?: string; 
  thumbnailUrl?: string;
  price: number; 
  originalPrice: number; 
  expiresAt?: string;
  categoryId: number;
  storeId: number;
  coupon?: string;
}

// Vote types
export interface Vote {
  id: number;
  value: 1 | -1;
  dealId: number;
  userId: number;
  createdAt: string;
}

// Saved Deal types
export interface SavedDeal {
  id: number;
  dealId: number;
  userId: number;
  createdAt: string;
  deal: Deal;
}

export interface ImageLocalizationResponse {
  data: {
    imageUrl: string;
    thumbnailUrl: string;
  };
}
