import axios from 'axios';

// Create an axios instance with base URL and default headers
const instance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5010/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include auth token if available
instance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle common error responses
instance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Optionally handle unauthorized errors globally here
      // For example, redirect to login or clear auth state
    }
    return Promise.reject(error);
  }
);

export default instance;
