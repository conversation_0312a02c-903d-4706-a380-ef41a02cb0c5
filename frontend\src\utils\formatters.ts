/**
 * Format a date string to a more readable format
 * @param dateString ISO date string
 * @param options Formatting options
 * @returns Formatted date string
 */
export const formatDate = (
  dateString: string, 
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }
): string => {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', options).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

/**
 * Format a price number to currency format
 * @param price Number to format
 * @param locale Locale string (default: 'en-US')
 * @param currency Currency code (default: 'USD')
 * @returns Formatted price string
 */
export const formatPriceUSD = (
  price: number | undefined, 
  locale = 'en-US', 
  currency = 'USD'
): string => {
  if (price === undefined || price === null) return 'N/A';
  
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price);
  } catch (error) {
    console.error('Error formatting price:', error);
    return `${price}`;
  }
};

/**
 * Format a price to a readable currency format
 * @param price Number to format as currency
 * @param currency Currency code (default: GBP)
 * @returns Formatted price string
 */
export const formatPrice = (
  price: number | undefined, 
  currency: string = 'GBP'
): string => {
  if (price === undefined || price === null) return 'Free';
  
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(price);
};

/**
 * Format a number with commas for thousands
 * @param num Number to format
 * @returns Formatted number string
 */
export const formatNumber = (num: number | undefined): string => {
  if (num === undefined || num === null) return 'N/A';
  
  try {
    return new Intl.NumberFormat().format(num);
  } catch (error) {
    console.error('Error formatting number:', error);
    return `${num}`;
  }
};

/**
 * Truncate a string to a certain length
 * @param str String to truncate
 * @param length Maximum length
 * @param suffix Suffix to add when truncated (default: '...')
 * @returns Truncated string
 */
export const truncateString = (
  str: string, 
  length: number, 
  suffix = '...'
): string => {
  if (!str) return '';
  if (str.length <= length) return str;
  
  return str.substring(0, length).trim() + suffix;
};

/**
 * Format a percentage
 * @param value Number to format as percentage
 * @param decimals Number of decimal places
 * @returns Formatted percentage string
 */
export const formatPercentage = (
  value: number, 
  decimals = 0
): string => {
  if (value === undefined || value === null) return 'N/A';
  
  try {
    return `${value.toFixed(decimals)}%`;
  } catch (error) {
    console.error('Error formatting percentage:', error);
    return `${value}%`;
  }
};

/**
 * Format a discount percentage
 * @param originalPrice Original price
 * @param discountedPrice Discounted price
 * @param decimals Number of decimal places
 * @returns Formatted discount percentage string
 */
export const formatDiscountPercentage = (
  originalPrice?: number,
  discountedPrice?: number,
  decimals = 0
): string => {
  if (!originalPrice || !discountedPrice || originalPrice <= 0) return '';
  
  try {
    const discountPercentage = ((originalPrice - discountedPrice) / originalPrice) * 100;
    return formatPercentage(discountPercentage, decimals);
  } catch (error) {
    console.error('Error calculating discount percentage:', error);
    return '';
  }
};

// --- Relative Time Formatter (Simple Example) ---
/**
 * Formats a date/time string into a relative time string (e.g., "5 minutes ago").
 * NOTE: This is a basic implementation. Consider using a library like date-fns for more robust formatting.
 * @param dateString ISO date string or Date object
 * @returns Relative time string
 */
export const formatRelativeTime = (dateString: string | Date | undefined): string => {
  if (!dateString) return 'N/A';

  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    const now = new Date();
    const seconds = Math.round((now.getTime() - date.getTime()) / 1000);
    const minutes = Math.round(seconds / 60);
    const hours = Math.round(minutes / 60);
    const days = Math.round(hours / 24);

    if (seconds < 60) return `${seconds} seconds ago`;
    if (minutes < 60) return `${minutes} minutes ago`;
    if (hours < 24) return `${hours} hours ago`;
    if (days === 1) return 'yesterday';
    if (days < 7) return `${days} days ago`;

    // For older dates, return a simple date format
    return formatDate(date.toISOString(), { year: 'numeric', month: 'short', day: 'numeric' });
  } catch (error) {
    console.error('Error formatting relative time:', error);
    // Return original string or a fallback if parsing failed
    return typeof dateString === 'string' ? dateString : 'Invalid Date';
  }
};

/**
 * Get the affiliate URL for a deal
 * @param url Original deal URL
 * @param storeId Store ID to check for special affiliate handling
 * @returns URL with affiliate tags if applicable
 */
export const getAffiliateUrl = (
  url?: string,
  storeId?: number
): string => {
  if (!url) return '';
  
  try {
    // Check if this is an Amazon deal (storeId === 42)
    if (storeId === 42) {
      // Parse the URL
      const urlObj = new URL(url);
      
      // Check if it's already an Amazon URL to be safe
      if (urlObj.hostname.includes('amazon') || urlObj.hostname.includes('amzn')) {
        // Add the affiliate tag
        urlObj.searchParams.set('tag', 'bargn0c-21');
        return urlObj.toString();
      }
    }
    
    // Check if this is an eBay deal (storeId === 43)
    if (storeId === 43) {
      // Parse the URL
      const urlObj = new URL(url);
      
      // Check if it's an eBay URL to be safe
      if (urlObj.hostname.includes('ebay')) {
        // Add eBay affiliate parameters for UK
        urlObj.searchParams.set('mkevt', '1');
        urlObj.searchParams.set('mkcid', '1');
        urlObj.searchParams.set('mkrid', '710-53481-19255-0');
        urlObj.searchParams.set('campid', '5338762847');
        return urlObj.toString();
      }
    }
    
    // Return the original URL for non-affiliate stores
    return url;
  } catch (error) {
    console.error('Error generating affiliate URL:', error);
    return url; // Return the original URL if there's an error
  }
};
