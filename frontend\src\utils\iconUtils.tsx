import React, { ElementType } from 'react';
import {
    DevicePhoneMobileIcon,
    ComputerDesktopIcon,
    TvIcon, // Assuming this was meant for something, keeping it
    CameraIcon, // Assuming this was meant for something, keeping it
    HomeModernIcon,
    WrenchScrewdriverIcon,
    HeartIcon,
    MusicalNoteIcon,
    BookOpenIcon,
    TruckIcon,
    GiftIcon,
    SparklesIcon,
    BuildingStorefrontIcon,
    GlobeAltIcon,
    RocketLaunchIcon,
    SwatchIcon,
    PhoneIcon,
    UserGroupIcon,
    BeakerIcon, // Assuming this was meant for something, keeping it
    QueueListIcon,
    BriefcaseIcon,
    ShoppingBagIcon, // Default icon
    HomeIcon
} from '@heroicons/react/24/outline';

// Icon mapping - add more as needed based on your actual categories
const iconMap: { [key: string]: ElementType } = {
    'electronics': DevicePhoneMobileIcon,
    'computers': ComputerDesktopIcon,
    'gaming': RocketLaunchIcon,
    'home & garden': HomeModernIcon,
    'clothing & accessories': SwatchIcon,
    'beauty & health': HeartIcon,
    'toys & kids': GiftIcon,
    'books & media': BookOpenIcon,
    'food & drink': BuildingStorefrontIcon,
    'travel': GlobeAltIcon,
    'sports & outdoors': RocketLaunchIcon, // Example: Using RocketLaunch
    'automotive': TruckIcon,
    'services': BriefcaseIcon,
    'entertainment': MusicalNoteIcon,
    'home improvement': WrenchScrewdriverIcon,
    'office supplies': BriefcaseIcon,
    'groceries': BuildingStorefrontIcon,
    'health & beauty': HeartIcon, // Duplicate key, consider consolidation
    'fashion & accessories': SwatchIcon, // Duplicate key, consider consolidation
    'culture & leisure': SparklesIcon,
    'broadband & phone contracts': PhoneIcon,
    'home & living': HomeIcon,
    'garden & do it yourself': WrenchScrewdriverIcon, // Duplicate key, consider consolidation
    'family & kids': UserGroupIcon,
    'other': QueueListIcon
};

export const getCategoryIcon = (categoryName: string): React.ReactNode => {
    // Convert category name to lowercase for matching
    const normalizedName = categoryName?.toLowerCase() || '';

    // Get the icon component and explicitly type it
    const IconComponent: ElementType = iconMap[normalizedName] || ShoppingBagIcon; 

    // It's good practice to handle the case where IconComponent might somehow be falsy, though unlikely here
    if (!IconComponent) return null;

    // Return the JSX element
    return <IconComponent className="mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500" aria-hidden="true" />;
};
