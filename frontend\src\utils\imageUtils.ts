// Use UPLOADS_URL for static files, API_URL for API endpoints
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5010/api';
const UPLOADS_BASE_URL = process.env.REACT_APP_UPLOADS_URL || 'http://localhost:5010';

const DEBUG: number = 1; // Debug flag: Set to 1 to enable console logging, 0 to disable

// Debug logging helper
const debugLog = (...args: any[]) => {
  if (DEBUG > 0) {
    console.log(...args);
  }
};

// Debug error logging helper
const debugError = (...args: any[]) => {
  if (DEBUG > 0) {
    console.error(...args);
  }
};

// Placeholder image that exists on the server
export const PLACEHOLDER_IMAGE = '/placeholder-image.png';

// Inline SVG placeholder as a fallback if the server placeholder isn't available
export const INLINE_PLACEHOLDER = 'data:image/svg+xml;charset=UTF-8,%3csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' fill=\'none\'%3e%3crect width=\'100\' height=\'100\' fill=\'%23F3F4F6\'/%3e%3cpath d=\'M40 50 L60 50 M50 40 L50 60\' stroke=\'%239CA3AF\' stroke-width=\'2\'/%3e%3c/svg%3e';

// Check if URL is external (starts with http/https)
export const isExternalUrl = (url: string | null | undefined): boolean => {
  if (!url) {
    debugLog('isExternalUrl: url is null/undefined');
    return false;
  }
  
  // Simple check - if it starts with /uploads/ it's local, otherwise if it starts with http it's external
  const isLocal = url.startsWith('/uploads/');
  const isExternal = !isLocal && (url.startsWith('http://') || url.startsWith('https://'));
  
  debugLog('isExternalUrl check:', { url, isLocal, isExternal });
  return isExternal;
};

// Convert a path to a full URL
export const getImageUrl = (path: string | null | undefined): string => {
  if (!path) return PLACEHOLDER_IMAGE;
  
  // If it's already a full URL, return as is
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }
  
  // Make sure path starts with a slash if not already
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  
  // Append path to base URL
  return `${UPLOADS_BASE_URL}${normalizedPath}`;
};

// Interface to handle both snake_case and camelCase properties
interface ImageUrls {
  imageUrl?: string | null;
  thumbnailUrl?: string | null;
  status?: string;
  userId?: number;
}

// Get the correct deal image based on context rules
export const getDealImage = (
  deal: ImageUrls | null | undefined, 
  options: {
    isAdmin?: boolean;
    currentUserId?: number | null;
    useThumbnail?: boolean;
    forceOwner?: boolean;
  } = {}
): string => {
  // Default options
  const { isAdmin = false, currentUserId = null, useThumbnail = true, forceOwner = false } = options;
  
  debugLog('getDealImage input:', {
    deal,
    currentUserId,
    isAdmin,
    useThumbnail,
    forceOwner
  });

  if (!deal) {
    debugLog('No deal provided, returning placeholder');
    return PLACEHOLDER_IMAGE;
  }
  
  // Get user ID from the deal
  const dealUserId = deal.userId;
  debugLog('Deal user ID:', dealUserId);
  
  // If current user is the owner or forceOwner is true, always show the actual image
  const isOwner = forceOwner || (currentUserId && dealUserId && currentUserId === dealUserId);
  debugLog('Is owner check:', { currentUserId, dealUserId, isOwner, forceOwner });
  
  // Get the image path based on whether to use thumbnail or full image
  let imagePath: string | null | undefined;
  
  if (useThumbnail) {
    // Use thumbnail if specified (default behavior)
    imagePath = deal.thumbnailUrl;
    debugLog('Using thumbnail path:', imagePath);
    // Fall back to main image if thumbnail not available
    if (!imagePath) {
      imagePath = deal.imageUrl;
      debugLog('Falling back to main image:', imagePath);
    }
  } else {
    // Use main image when explicitly requested
    imagePath = deal.imageUrl;
    debugLog('Using main image path:', imagePath);
    // Fall back to thumbnail if main image not available
    if (!imagePath) {
      imagePath = deal.thumbnailUrl;
      debugLog('Falling back to thumbnail:', imagePath);
    }
  }
  
  // If no image at all, return placeholder
  if (!imagePath) {
    debugLog('No image path found, returning placeholder');
    return PLACEHOLDER_IMAGE;
  }
  
  // RULE 1: If image is external, show placeholder (unless in admin area)
  if (isExternalUrl(imagePath)) {
    debugLog('External URL detected:', { imagePath, isAdmin });
    if (isAdmin) {
      // RULE 2: In admin area, always show the image even if external
      debugLog('Admin view, showing external image');
      return imagePath;
    }
    debugLog('Not admin, showing placeholder for external image');
    return PLACEHOLDER_IMAGE;
  }
  
  // At this point, we know the image is local
  
  // RULE 2: If in admin area, always show local images
  if (isAdmin) {
    debugLog('Admin view, showing local image');
    return getImageUrl(imagePath);
  }
  
  // RULE 3 & 4: For local images on public site, check deal status
  const isPending = deal.status === 'pending';
  debugLog('Deal status check:', { status: deal.status, isPending });
  
  if (isPending) {
    // RULE 4: For pending deals, only show to owner
    if (isOwner) {
      debugLog('Pending deal but owner, showing actual image');
      return getImageUrl(imagePath);
    }
    
    debugLog('Pending deal and not owner, showing placeholder');
    return PLACEHOLDER_IMAGE;
  }
  
  // RULE 3: For active deals with local images, always show
  debugLog('Active deal with local image, returning image URL');
  return getImageUrl(imagePath);
};

// Simplified function to get thumbnail (maintains backward compatibility)
export const getThumbnailUrl = (item: ImageUrls | null | undefined): string => {
  return getDealImage(item, { useThumbnail: true });
};

// Function to get full image URL (used for deal detail and admin edit pages)
export const getFullImageUrl = (
  item: ImageUrls | null | undefined,
  options: {
    currentUserId?: number | null;
    isAdmin?: boolean;
    forceOwner?: boolean;
  } = {}
): string => {
  return getDealImage(item, { ...options, useThumbnail: false });
};

// Function specifically for admin routes
export const getAdminImageUrl = (item: ImageUrls | null | undefined): string => {
  return getDealImage(item, { isAdmin: true, useThumbnail: false });
};

// Handle image error by providing fallback
export const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
  debugError('Failed to load image:', e.currentTarget.src);
  e.currentTarget.onerror = null; // Prevent infinite error loop
  e.currentTarget.src = PLACEHOLDER_IMAGE;
};
