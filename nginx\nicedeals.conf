server {
    listen 80;
    server_name www.nicedeals.app;
    
    # Redirect all HTTP to HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 80;
    server_name nicedeals.app;
    
    # Redirect non-www HTTP to www HTTPS
    return 301 https://www.nicedeals.app$request_uri;
}

server {
    listen 443 ssl http2;
    server_name nicedeals.app;
    
    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    
    # Redirect non-www HTTPS to www HTTPS
    return 301 https://www.nicedeals.app$request_uri;
}

server {
    listen 443 ssl http2;
    server_name www.nicedeals.app;

    # SSL Configuration (Cloudflare Full/Strict mode)
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Order matters! More specific paths must come before less specific ones
    
    # API endpoints - the backend already has /api prefix in routes
    location /api/ {
        # Log the request for debugging
        access_log /var/log/nginx/api_access.log;
        error_log /var/log/nginx/api_error.log debug;
        
        # Handle OPTIONS method for CORS preflight requests
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' 'https://www.nicedeals.app' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        
        # Proxy to backend - the backend routes already include /api/
        proxy_pass http://localhost:5010;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers for regular requests
        add_header 'Access-Control-Allow-Origin' 'https://www.nicedeals.app' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Origin, X-Requested-With, Content-Type, Accept, Authorization' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
    }
    
    # This location block must come BEFORE the frontend location block
    # The ^~ prefix ensures that this block is chosen if the URL starts with /uploads/
    # and no further regex matching is attempted
    location ^~ /uploads/ {
        # Server static files directly from backend
        proxy_pass http://localhost:5010;
        
        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Cache settings for static files
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
        
        # CORS headers for image files
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
        
        # Log for debugging
        access_log /var/log/nginx/uploads_access.log;
        error_log /var/log/nginx/uploads_error.log debug;
    }
    
    # Frontend - This must be AFTER the uploads location
    location / {
        proxy_pass http://localhost:3010;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}