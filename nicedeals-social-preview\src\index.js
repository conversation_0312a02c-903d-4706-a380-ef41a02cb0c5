/**
 * NiceDeals Social Media Preview Worker
 * This Cloudflare Worker intercepts requests from social media bots
 * and serves them pre-rendered HTML with meta tags.
 */

addEventListener('fetch', event => {
	event.respondWith(handleRequest(event.request))
  })
  
  /**
   * Handle incoming requests
   * @param {Request} request
   */
  async function handleRequest(request) {
	// Get request details
	const url = new URL(request.url)
	const userAgent = request.headers.get('user-agent') || ''
	
	// Define known social media bots
	const socialBots = [
	  // Facebook crawlers
	  'facebookexternalhit', 'Facebot', 'facebook', 'FB',
	  // LinkedIn crawlers
	  'LinkedInBot', 
	  // Twitter and X.com crawlers
	  'Twitterbot', 'X-Twitter', 'TwitterCards', 'XBot', 
	  // Messaging apps
	  'WhatsApp', 'Telegram',
	  // Search engines
	  'bingbot', 'Googlebot', 'Baiduspider', 'DuckDuckBot', 'YandexBot',
	  // Other social media crawlers
	  'PinterestBot', 'SkypeUriPreview', 'Slackbot'
	]
	
	// Check if request is from a social media bot
	const isSocialBot = socialBots.some(bot => 
	  userAgent.toLowerCase().includes(bot.toLowerCase())
	)
	
	// Log request details (visible in Cloudflare dashboard)
	console.log({
	  url: url.pathname,
	  userAgent: userAgent,
	  isSocialBot: isSocialBot
	})
	
	// Only process deal detail pages
	if (url.pathname.startsWith('/dealDetail/')) {
	  const dealId = url.pathname.split('/').pop()
	  
	  if (isSocialBot) {
		try {
		  // Fetch deal data from your API
		  console.log(`Fetching deal data for ID: ${dealId}`);
		  const apiResponse = await fetch(`https://www.nicedeals.app/api/deals/${dealId}`)
		  
		  // If API fails, fall back to original request
		  if (!apiResponse.ok) {
			console.error(`API returned ${apiResponse.status} for deal ${dealId}`)
			return fetch(request)
		  }
		  
		  const dealData = await apiResponse.json()
		  console.log('API response data:', JSON.stringify(dealData))
		  
		  // Generate HTML with appropriate meta tags
		  const html = generateSocialHTML(dealData, dealId)
		  
		  // Return the HTML with appropriate headers
		  return new Response(html, {
			headers: {
			  'Content-Type': 'text/html;charset=UTF-8',
			  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
			  'Pragma': 'no-cache',
			  'Expires': '0'
			}
		  })
		} catch (error) {
		  console.error('Error in worker:', error.message)
		  // Fall back to original request if anything fails
		  return fetch(request)
		}
	  }
	}
	
	// For regular users or non-deal pages, proceed with original request
	return fetch(request)
  }
  
  /**
   * Generate HTML with Open Graph meta tags
   * @param {Object} dealData Deal data from API
   * @param {string} dealId Deal ID
   */
  function generateSocialHTML(dealData, dealId) {
	// Handle different API response structures
	const deal = dealData.data || dealData.deal || dealData;
	
	// Extract needed properties with fallbacks
	let title = deal.title || deal.name || 'NiceDeals';
	let description = deal.description || '';
	const price = deal.price || deal.current_price || '0';
	
	// Get store name (could be either string or object)
	let storeName = '';
	if (deal.store) {
	  storeName = typeof deal.store === 'object' ? deal.store.name : deal.store;
	} else if (deal.store_name) {
	  storeName = deal.store_name;
	}
	
	// Escape HTML special characters to prevent broken tags
	title = escapeHtml(title);
	description = escapeHtml(description);
	storeName = escapeHtml(storeName);
	
	// Handle different image URL properties
	let imageUrl = '';
	if (deal.image_url) {
	  imageUrl = deal.image_url;
	} else if (deal.imageUrl) {
	  imageUrl = deal.imageUrl;
	} else if (deal.image) {
	  imageUrl = deal.image;
	} else if (deal.thumbnail_url) {
	  imageUrl = deal.thumbnail_url;
	} else if (deal.thumbnailUrl) {
	  imageUrl = deal.thumbnailUrl;
	}
	
	// Ensure image URL is absolute
	imageUrl = imageUrl?.startsWith('http') 
	  ? imageUrl 
	  : `https://www.nicedeals.app${imageUrl || ''}`;
	
	return `<!DOCTYPE html>
  <html lang="en">
  <head>
	<meta charset="utf-8">
	<link rel="icon" href="/favicon.ico">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="theme-color" content="#000000">
	
	<title>${title} | NiceDeals</title>
	<meta name="description" content="Grab a deal on ${title} today. Visit Nicedeals and comment and vote and find other great UK deals.">
	
	<!-- Open Graph / Facebook -->
	<meta property="og:type" content="website">
	<meta property="og:url" content="https://www.nicedeals.app/dealDetail/${dealId}">
	<meta property="og:title" content="${title}">
	<meta property="og:description" content="Currently &pound;${price}. Grab ${title} ${storeName ? `from ${storeName}` : ''} today. Visit Nicedeals and comment and vote and find other great UK deals.">
	<meta property="og:image" content="${imageUrl}">
	<meta property="og:site_name" content="Nicedeals">
	<meta property="fb:app_id" content="1156897059246722">
	<meta property="og:price:amount" content="${price}">
	<meta property="og:price:currency" content="GBP">
	<!-- Twitter -->
	<meta name="twitter:card" content="summary">
	<meta name="twitter:site" content="@NicedealsApp">
	<meta name="twitter:title" content="${title}">
	<meta name="twitter:description" content="Currently &pound;${price}.Grab ${title} ${storeName ? `from ${storeName}` : ''} today. Visit Nicedeals and comment and vote and find other great UK deals.">
	<meta name="twitter:image" content="${imageUrl}">
  
	
	
	<!-- Debug Information (hidden) -->
	<!-- Generated by: NiceDeals Cloudflare Worker -->
	<!-- Generated at: ${new Date().toISOString()} -->
	<!-- Deal ID: ${dealId} -->
	
	<!-- Google Analytics -->
	<script async src="https://www.googletagmanager.com/gtag/js?id=G-SDDBQ0QY5D"></script>
	<script>
	  window.dataLayer = window.dataLayer || [];
	  function gtag(){dataLayer.push(arguments);}
	  gtag('js', new Date());
	  gtag('config', 'G-SDDBQ0QY5D');
	</script>
  </head>
  <body>
	<div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
	  <h1 style="color: #f97316;">${title}</h1>
	  <div style="padding: 10px; background-color: #f8f9fa; border-radius: 5px; margin-bottom: 20px;">
		<p style="color: #666; margin: 0;">This is a social media preview page for NiceDeals. If you're seeing this page directly, you can visit the actual deal page by clicking the link below.</p>
		<p style="margin-top: 10px;"><a href="https://www.nicedeals.app/dealDetail/${dealId}" style="color: #f97316; text-decoration: none; font-weight: bold;">View this deal on NiceDeals</a></p>
	  </div>
	  <div style="margin-bottom: 20px;">
		<p style="color: #333;">${description}</p>
		<p style="font-weight: bold; color: #f97316;">Price: £${price}</p>
	  </div>
	  <img src="${imageUrl}" alt="${title}" style="max-width: 100%; border-radius: 8px; margin-bottom: 20px;">
	  <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
		<p><a href="https://www.nicedeals.app" style="color: #f97316; text-decoration: none; font-weight: bold;">NiceDeals - Find the best deals in the UK</a></p>
	  </div>
	</div>
  </body>
  </html>`
  }

  function escapeHtml(unsafe) {
    return unsafe
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }